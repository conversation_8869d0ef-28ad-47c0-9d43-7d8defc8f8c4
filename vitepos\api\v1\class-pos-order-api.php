<?php
/**
 * Its api for order
 *
 * @since: 12/07/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package VitePos\Api\V1
 */

namespace VitePos\Api\V1;

use Appsbd\V1\libs\API_Data_Response;
use VitePos\Libs\API_Base;
use VitePos\Libs\POS_Order;
use VitePos\Libs\POS_Payment;
use Vitepos\Models\Database\Mapbd_Pos_Cash_Drawer;
use Vitepos\Models\Database\Mapbd_Pos_Cash_Drawer_Types;
use Vitepos\Models\Database\Mapbd_Pos_Role;
use VitePos\Modules\APBD_EPOS_Settings;
use VitePos\Modules\POS_Settings;

/**
 * Class pos_order_api
 *
 * @package VitePos\Api\V1
 */
class Pos_Order_Api extends API_Base {

	/**
	 * The set api base is generated by appsbd
	 *
	 * @return mixed|string
	 */
	public function set_api_base() {
		return 'order';
	}

	/**
	 * The routes is generated by appsbd
	 *
	 * @return mixed|void
	 */
	public function routes() {
		$this->register_rest_route( 'POST', 'make-payment', array( $this, 'make_payment' ) );
		$this->register_rest_route( 'POST', 'order-refund', array( $this, 'order_refund' ) );
		$this->register_rest_route( 'POST', 'order-cancel', array( $this, 'order_cancel' ) );
		$this->register_rest_route( 'POST', 'order-complete', array( $this, 'complete_order_payment' ) );
		$this->register_rest_route( 'POST', 'sync-offline-order', array( $this, 'sync_offline_payment' ) );
		$this->register_rest_route( 'POST', 'order-list', array( $this, 'order_list' ) );
		$this->register_rest_route( 'POST', 'refund-list', array( $this, 'refund_list' ) );
		$this->register_rest_route( 'POST', 'orders-for-refund', array( $this, 'orders_for_refund' ) );
		$this->register_rest_route( 'POST', 'online-list', array( $this, 'online_order_list' ) );
		$this->register_rest_route( 'POST', 'change-status', array( $this, 'change_status' ) );
		$this->register_rest_route( 'POST', 'complete-order', array( $this, 'pay_first_complete_order' ) );
		$this->register_rest_route( 'GET', 'details/(?P<id>\d+)', array( $this, 'order_details' ) );
		$this->register_rest_route( 'GET', 'email/(?P<order_id>\d+)', array( $this, 'send_email' ) );

		$this->register_rest_route( 'GET', 'payment/(?P<id>\d+)', array( $this, 'order_payment' ) );
		$this->register_rest_route( 'POST', 'payment/(?P<id>\d+)', array( $this, 'order_payment' ) );
	}

	/**
	 * The set route permission is generated by appsbd
	 *
	 * @param \VitePos\Libs\any $route Its string.
	 *
	 * @return bool
	 */
	public function set_route_permission( $route ) {
		switch ( $route ) {
			case 'make-payment':
				return current_user_can( 'pos-menu' ) || current_user_can( 'cashier-menu' );
			case 'order-complete':
				return current_user_can( 'pos-menu' ) || current_user_can( 'cashier-menu' ) || current_user_can( 'make-complete-kitchen' );
			case 'pos-menu':
				return current_user_can( 'complete-order' );
			case 'online-list':
				return current_user_can( 'order-online' );
			case 'order-list':
				return current_user_can( 'order-list' );
			case 'refund-list':
				return current_user_can( 'refund-order-list' );
			case 'orders-for-refund':
				return current_user_can( 'refund-order' );
			case 'order_details':
				return current_user_can( 'order-details' );
			case 'payment':
				return true;
			default:
				return POS_Settings::is_pos_user();
		}
		return parent::set_route_permission( $route );
	}

	/**
	 * The make payment is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function make_payment() {
		try {
			return $this->make_order_payment( false );
		} catch ( \Exception $e ) {
			$this->add_error( $e->getMessage() );
			$this->response->set_response( false );
		} catch ( \WC_Data_Exception $e ) {
			$this->add_error( $e->getMessage() );
			$this->response->set_response( false );
		}
		return $this->response;
	}
	/**
	 * The make payment is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function order_cancel() {
		self::set_vite_pos_request();
		$payment = new POS_Payment( $this->payload, $this->get_outlet_id(), $this->get_counter_id() );
		if ( $payment->cancel_order() ) {
			$this->response->set_response( true, '', $payment->get_order_details() );
		} else {
			$this->response->set_response( false, '', $payment->get_order_details() );
		}
		return $this->response;
	}
	/**
	 * The make payment is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function complete_order_payment() {
		self::set_vite_pos_request();
		$payment = new POS_Payment( $this->payload, $this->get_outlet_id(), $this->get_counter_id() );
		if ( $payment->complete_order_payment() ) {
			$this->response->set_response( true, '', $payment->get_order_details() );
		} else {
			$this->response->set_response( false, '', $payment->get_order_details() );
		}
		return $this->response;
	}
	/**
	 * The make payment is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function sync_offline_payment() {
		$offline_id = $this->get_payload( 'offline_id', '' );
		if ( ! empty( $offline_id ) ) {
			$pos_order = POS_Order::get_order_by_meta( '_vtp_offline_id', $offline_id );
			if ( ! empty( $pos_order ) ) {
				$this->set_response( true, '', POS_Order::get_from_woo_order_details( $pos_order ) );

				return $this->response->get_response();
			}
			try {
				return $this->make_order_payment( true );
			} catch ( \Exception $e ) {
				$this->add_error( $e->getMessage() );
				$this->response->set_response( false );
			} catch ( \WC_Data_Exception $e ) {
				$this->add_error( $e->getMessage() );
				$this->response->set_response( false );
			}
		} else {
			$this->add_error( 'Offline id is missing' );
		}
		return $this->response->get_response();
	}
	/**
	 * The make order payment is generated by appsbd
	 *
	 * @param false $is_offline Is offline or not.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 * @throws \WC_Data_Exception Throw data exception.
	 */
	private function make_order_payment( $is_offline = false ) {
		self::set_vite_pos_request();
		$payment = new POS_Payment( $this->payload, $this->get_outlet_id(), $this->get_counter_id() );
		if ( 'P' == POS_Settings::get_pos_mode() ) {
			if ( $payment->restaurant_checkout_pay_first( $is_offline ) ) {
				$this->response->set_response( true, '', $payment->get_order_details() );
			} else {
				$this->response->set_response( false, '', $payment->get_order_details() );
			}
		} else {
			if ( $payment->grocery_checkout( $is_offline ) ) {
				$this->response->set_response( true, '', $payment->get_order_details() );
			} else {
				$this->response->set_response( false, '', $payment->get_order_details() );
			}
		}
		return $this->response->get_response();
	}
	/**
	 * The make order payment is generated by appsbd
	 *
	 * @param false $is_offline Is offline or not.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 * @throws \WC_Data_Exception Throw data exception.
	 */
	private function make_order_payment2( $is_offline = false ) {
		self::set_vite_pos_request();
		if ( floatval( $this->payload['grand_total'] ) < 0 ) {
			$this->response->set_response( false, 'You can not order less than 0 amount' );
			return $this->response->get_response();
		}
		if ( $is_offline ) {
			$outlet_id  = $this->get_payload( 'outlet_id', '' );
			$counter_id = $this->get_payload( 'counter_id', '' );
			if ( ! empty( $outlet_id ) && ! empty( $counter_id ) ) {
				$this->set_outlet( $outlet_id, $counter_id );
			} else {
				$this->add_error( 'No offline outlet and counter found' );
				$this->response->set_response( false );
				return $this->response;
			}
		}
		if ( ! $is_offline && ! POS_Settings::is_admin_user() ) {
			if ( ! current_user_can( 'pos-discount' ) && ( ! empty( $this->payload['discounts'] ) && is_array( $this->payload['discounts'] ) ) ) {
				$this->response->set_response( false, 'You do not have permission to give discount' );

				return $this->response->get_response();
			}
			$current_user  = get_user_by( 'id', $this->get_current_user_id() );
			$user_discount = Mapbd_Pos_Role::get_discount_percentage( $current_user );
			if ( ! $this->check_discount_limit( $this->payload['sub_total'], $user_discount, $this->payload['discounts'] ) ) {
				$this->response->set_response( false, 'You can not give this much discount' );
				return $this->response->get_response();
			}
		}
		if ( ! current_user_can( 'pos-fee' ) && ( ! empty( $this->payload['fees'] ) && is_array( $this->payload['fees'] ) ) ) {
			$this->response->set_response( false, 'You do not have permission to have fees' );
			return $this->response->get_response();
		}
		$outlet_obj   = $this->get_outlet_obj();
		$given_amount = (float) $this->get_payload( 'given_amount', 0.0 );
		$grand_total  = (float) $this->get_payload( 'grand_total', 0.0 );
		if ( $given_amount < $grand_total ) {
			$this->response->set_response( false, POS_Settings::get_module_instance()->__( 'Paid amount must be greater or equal to grand total amount' ), null );
			return $this->response;
		}

		if ( ! empty( $this->payload['items'] ) ) {
			$outlet_address = array(
				'first_name' => $outlet_obj->name,
				'last_name'  => '',
				'email'      => $outlet_obj->email,
				'phone'      => $outlet_obj->phone,
				'address_1'  => 'Y' == $outlet_obj->main_branch ? 'Main Branch' : '',
				'city'       => $outlet_obj->city,
				'state'      => $outlet_obj->state,
				'postcode'   => $outlet_obj->postcode,
				'country'    => $outlet_obj->country,
			);
			$customer_id    = $this->get_payload( 'customer', POS_Settings::get_module_option( 'pos_customer', null ) );
			$order_arg      = array(
				'customer_id' => $customer_id,
			);
						$order        = wc_create_order( $order_arg );
			$total_amount = 0.0;
			$total_tax    = 0.0;
			foreach ( $this->payload['items'] as $item ) {
				$arguments = array(
					'total_tax' => $item['tax_amount'] * $item['quantity'],
				);
				try {
					if ( ! empty( $item['variation_id'] ) ) {
						if ( ! empty( $item['attributes'] ) && is_array( $item['attributes'] ) ) {
							$arguments ['variation'] = array();
							foreach ( $item['attributes'] as $attribute ) {
								$attribute                                       = (object) $attribute;
								$arguments ['variation'][ $attribute->opt_slug ] = $attribute->val_slug;
							}
						} else {
							$arguments ['variation'] = vitepos_get_product_variation_attributes( $item['variation_id'] );
						}

						$arguments ['name'] = wc_get_product( $item['product_id'] )->get_name();
						$product            = new \WC_Product_Variation( $item['variation_id'] );
						$item_id            = $order->add_product( $product, $item['quantity'], $arguments ); 					} else {
						$item_id = $order->add_product( wc_get_product( $item['product_id'] ), $item['quantity'], $arguments ); 					}
					$total_tax += ( $item['quantity'] * $item['tax_amount'] );
					$oitem      = new \WC_Order_Item_Product( $item_id );
					if ( ! empty( $item['regular_price'] ) ) {
						$oitem->add_meta_data( '_vtp_regular_price', $item['regular_price'] );
					} else {
						$oitem->add_meta_data( '_vtp_regular_price', '' );
					}
					$oitem->save();

				} catch ( \Exception $e ) {
					$this->add_error( $e->getMessage() );
				}
			}
						if ( ! empty( $customer_id ) ) {
				/**
				 * Its for check is there any change before process
				 *
				 * @param $billing address
				 * @param $order \WC_Order Object
				 * @param $order_arg customer data
				 * @since 1.0
				 */
				$billing_address = apply_filters( 'vitepos/filter/billing-address', $outlet_address, $order, $customer_id );
			}

			$order->set_address( $outlet_address, 'billing' );
			$order->calculate_totals( true );
			$total_amount = $order->get_subtotal();
						$fee_total = 0.0;
			if ( ! empty( $this->payload['fees'] ) && is_array( $this->payload['fees'] ) ) {
				foreach ( $this->payload['fees'] as $item ) {
					if ( ! empty( $item['type'] ) && ! empty( $item['val'] ) ) {
						$item_val = floatval( $item['val'] );
						$title    = POS_Settings::get_module_instance()->__( 'Fee' );
						if ( $item_val > 0 ) {
							if ( strtoupper( $item['type'] ) == 'P' ) {
								$item_amount = $total_amount * ( $item_val / 100 );
								$title      .= '(' . $item['val'] . '%)';
							} else {
								$item_amount = $item_val;
							}
							$fee_total += $item_amount;
							vitepos_order_add_fee_on_order(
								$order,
								$title,
								$item_amount,
								array(
									'_vtp_cal_type' => $item['type'],
									'_vtp_cal_val'  => $item['val'],
								)
							);
						}
					}
				}
			}
			if ( ! $is_offline && ! POS_Settings::is_admin_user() ) {
				if ( ! $this->check_discount_limit( $total_amount, $user_discount, $this->payload['discounts'] ) ) {
					$order->delete( true );
					$this->response->set_response( false, 'You can not give this much discount' );
					return $this->response->get_response();
				}
			}
						$discount_total = 0.0;
			$discount       = 0.0;
			if ( ! empty( $this->payload['discounts'] ) && is_array( $this->payload['discounts'] ) ) {
				foreach ( $this->payload['discounts'] as $item ) {
					if ( ! empty( $item['type'] ) && ! empty( $item['val'] ) ) {
						$item_val = floatval( $item['val'] );
						$title    = POS_Settings::get_module_instance()->__( 'Discount' );
						if ( $item_val > 0 ) {
							if ( strtoupper( $item['type'] ) == 'P' ) {
								$item_amount = $total_amount * ( $item_val / 100 );
								$title      .= '(' . $item['val'] . '%)';
							} else {
								$item_amount = $item_val;
							}
							$discount += $item_amount;
							vitepos_order_add_discount_on_order(
								$order,
								$title,
								$item_amount,
								array(
									'_vtp_cal_type' => $item['type'],
									'_vtp_cal_val'  => $item['val'],
								)
							);
						}
					}
				}
			}

			try {
				if ( $total_tax > 0 ) {
					$order->set_cart_tax( $total_tax );
				}
			} catch ( \Exception $e ) {
				$this->add_error( $e->getMessage() );
			}

			$order->calculate_totals( false );

			$rounding_factor = null;
			if ( $order->get_total() != $grand_total ) {
				try {
					$order->add_meta_data( '_vtp_miss_total', ( -1 ) * ( $order->get_total() - $grand_total ) );
					$order->set_total( $grand_total );
				} catch ( \Exception $e ) {
					$order->calculate_totals( false );
				}
			}

			$order->add_meta_data( '_vtp_fee_total', -$fee_total );
			$order->add_meta_data( '_vtp_discount_total', -$discount_total );

			$order->add_meta_data( '_vtp_order_note', $this->get_payload( 'note', '' ) );
			$order->add_meta_data( '_vtp_payment_note', $this->get_payload( 'payment_note', '' ) );
			$order->add_meta_data( '_is_vitepos', 'Y' );
			$order->add_meta_data( '_vtp_payment_method', $this->get_payload( 'payment_method', '' ) );
			$order->add_meta_data( '_vtp_tendered_amount', $this->get_payload( 'given_amount', 0.0 ) );
			$change_amount = $this->get_payload( 'returned_amount', 0.0 );
			$order->add_meta_data( '_vtp_change_amount', $change_amount );
			$payment_list = $this->get_payload( 'payment_list', array() );
			$processed_by = $this->get_current_user_id();
			if ( ! $is_offline ) {
				$outlet_id  = $this->get_outlet_id();
				$counter_id = $this->get_counter_id();
			}
			$order->add_meta_data( '_vtp_payment_list', $payment_list );
			$order->add_meta_data( '_vtp_outlet_id', $outlet_id );
			$order->add_meta_data( '_vtp_counter_id', $counter_id );
			$order->set_address( $billing_address, 'billing' );
			if ( $is_offline ) {
				$offline_id = $this->get_payload( 'offline_id', '' );
				if ( ! empty( $offline_id ) ) {
					$order->add_meta_data( '_vtp_offline_id', $offline_id );
				}
				$processed_by_offline = $this->get_payload( 'processed_by' );
				$user                 = get_user_by( 'login', $processed_by_offline );
				if ( ! empty( $user->ID ) ) {
					$order->add_meta_data( '_vtp_offline_synced_by', $processed_by );
					$processed_by = $user->ID;
					$order->add_meta_data( '_vtp_processed_by', $processed_by );
				}
								$cashdrawer_id = $this->get_payload( 'cash_drawer_id', '' );
				if ( ! empty( $cashdrawer_id ) ) {
					$cashdrawer = Mapbd_Pos_Cash_Drawer::find_by(
						'id',
						$cashdrawer_id,
						array(
							'outlet_id'  => $outlet_id,
							'counter_id' => $counter_id,
						)
					);
				} else {
					$cashdrawer = Mapbd_Pos_Cash_Drawer::get_by_counter( $outlet_id, $counter_id, $processed_by );
				}
				$processed_by_offline = $this->get_payload( 'offline_order_time' );
				$order->add_meta_data( '_vtp_offline_process_date', gmdate( 'Y-m-d H:i:s', strtotime( $processed_by_offline ) ) );
			} else {
				$cashdrawer = Mapbd_Pos_Cash_Drawer::get_by_counter( $outlet_id, $counter_id, $processed_by );
			}
			$order->add_meta_data( '_vtp_processed_by', $processed_by );
			if ( ! empty( $cashdrawer ) ) {
				$order->add_meta_data( '_vtp_cash_drawer_id', $cashdrawer->id );
				$cash_found = false;
				foreach ( $payment_list as $payment ) {
					if ( 'C' == $payment['type'] ) {
						$cash_found = true;
						$amount     = doubleval( $payment['amount'] ) - doubleval( $change_amount );
						if ( $amount > 0.0 ) {
							Mapbd_Pos_Cash_Drawer::add_order(
								$this->get_current_user_id(),
								$amount,
								$order->get_id(),
								$outlet_id,
								$counter_id
							);
						} else {
							Mapbd_Pos_Cash_Drawer::add_order(
								$this->get_current_user_id(),
								doubleval( $payment['amount'] ),
								$order->get_id(),
								$outlet_id,
								$counter_id
							);
							Mapbd_Pos_Cash_Drawer::add_change_log( $this->get_current_user_id(), doubleval( $change_amount ), $order->get_id(), $outlet_id, $counter_id );
						}
					}
					Mapbd_Pos_Cash_Drawer_Types::AddLog( $cashdrawer->id, $this->get_current_user_id(), $order->get_id(), $payment['type'], $payment['amount'] );
				}
				if ( $change_amount > 0 ) {
					if ( ! $cash_found ) {
						Mapbd_Pos_Cash_Drawer::add_change_log( $this->get_current_user_id(), $change_amount, $order->get_id(), $outlet_id, $counter_id );
					}
					Mapbd_Pos_Cash_Drawer_Types::AddLog( $cashdrawer->id, $this->get_current_user_id(), $order->get_id(), '_', $change_amount );
				}
			}

			if ( $order->update_status( 'completed', 'Imported order', true ) ) {
				$this->response->set_response( true, 'Order successfully completed', POS_Order::get_from_woo_order_details_by_id( $order->get_id() ) );
			} else {
				$this->response->set_response( false, 'Failed', null );
			}
		} else {
			$this->response->set_response( false, 'Items empty', null );
		}
		return $this->response->get_response();
	}

	/**
	 * The check discount limit is generated by appsbd
	 *
	 * @param any   $subtotal Its subtotal Param.
	 * @param any   $user_discount Its user discount param.
	 * @param array $discounts Its discounts param.
	 *
	 * @return bool
	 */
	public function check_discount_limit( $subtotal, $user_discount, $discounts = array() ) {
		$user_max_discount = 0.00;
		if ( ! empty( $subtotal ) && $user_discount > 0 ) {
			$user_max_discount = $user_max_discount + ( floatval( $subtotal ) ) * ( floatval( $user_discount / 100 ) );
		}
		$discount_payload = 0.00;
		if ( $user_discount > 0 && ( ! empty( $discounts ) && is_array( $discounts ) ) ) {
			foreach ( $discounts as $item ) {
				if ( strtoupper( $item['type'] ) == 'P' ) {
					$discount_payload = $discount_payload + ( floatval( $subtotal ) ) * ( floatval( $item['val'] / 100 ) );
				} else {
					$discount_payload = $discount_payload + ( floatval( $item['val'] ) );
				}
			}
		}
		if ( $user_max_discount < $discount_payload ) {
			return false;
		}
		return true;
	}
	/**
	 * The order list is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function order_list() {
		$response_data = new API_Data_Response();
		$args          = array(
			'status'        => array( 'completed' ),
			'limit'         => $this->get_payload( 'limit', 10 ),
			'page'          => $this->get_payload( 'page', 1 ),
			'orderby'       => 'date',
			'order'         => 'DESC',
			'paginate'      => true,
			'vt_meta_query' => array(
				array(
					'key'     => '_is_vitepos',
					'value'   => 'Y',
					'compare' => '=',
				),
			),
		);
		if ( ! POS_Settings::is_admin_user() && ! current_user_can( 'can-see-any-outlet-orders' ) ) {
			$outlets = get_user_meta( $this->get_current_user_id(), 'outlet_id', true );
			if ( is_array( $outlets ) ) {
				$args['vt_meta_query'][] = array(
					'key'     => '_vtp_outlet_id',
					'value'   => $outlets,
					'compare' => 'IN',
				);
			} else {
				$this->add_error( "You don't have permission to view details of this outlet" );
				$response_data->set_total_records( 0 );
				$this->response->set_response( false, '', $response_data );
				return $this->response->get_response();
			}
		}

		$src_props     = $this->get_payload( 'src_by', array() );
		$sort_by_props = $this->get_payload( 'sort_by', array() );
		POS_Order::order_search_props( $args, $src_props );
		POS_Order::order_sort_param( $sort_by_props, $args );
		$orders = wc_get_orders( $args );

		$orderlist = array();
		if ( ! empty( $orders->orders ) && is_array( $orders->orders ) ) {
			foreach ( $orders->orders as $order ) {
				$order_data  = POS_Order::get_from_woo_order( $order );
				$orderlist[] = $order_data;
			}
		}

		$response_data->limit = $this->get_payload( 'limit', 10 );
		$response_data->page  = $this->get_payload( 'page', 1 );
		if ( $response_data->set_total_records( $orders->total ) ) {
			$response_data->rowdata = $orderlist;
		}

		$this->response->set_response( true, 'Order found', $response_data );
		return $this->response;
	}
	/**
	 * The order list is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function refund_list() {
		$response_data = new API_Data_Response();
		$args          = array(
			'status'        => array( 'completed' ),
			'limit'         => $this->get_payload( 'limit', 10 ),
			'page'          => $this->get_payload( 'page', 1 ),
			'orderby'       => 'date',
			'order'         => 'DESC',
			'paginate'      => true,
			'parent'        =>0,
			'vt_meta_query' => array(
				array(
					'key'     => '_is_vitepos',
					'value'   => 'Y',
					'compare' => '=',
				),
				array(
					'key'     => '_vt_has_refund',
					'value'   => 'Y',
					'compare' => '=',
				),
			),
		);
		if ( ! POS_Settings::is_admin_user() && ! current_user_can( 'can-see-any-outlet-orders' ) ) {
			$outlets = get_user_meta( $this->get_current_user_id(), 'outlet_id', true );
			if ( is_array( $outlets ) ) {
				$args['vt_meta_query'][] = array(
					'key'     => '_vtp_outlet_id',
					'value'   => $outlets,
					'compare' => 'IN',
				);
			} else {
				$this->add_error( "You don't have permission to view details of this outlet" );
				$response_data->set_total_records( 0 );
				$this->response->set_response( false, '', $response_data );
				return $this->response->get_response();
			}
		}

		$src_props     = $this->get_payload( 'src_by', array() );
		$sort_by_props = $this->get_payload( 'sort_by', array() );
		POS_Order::order_search_props( $args, $src_props );
		POS_Order::order_sort_param( $sort_by_props, $args );
		$orders = wc_get_orders( $args );

		$orderlist = array();
		if ( ! empty( $orders->orders ) && is_array( $orders->orders ) ) {
			foreach ( $orders->orders as $order ) {
				$order_data  = POS_Order::get_from_woo_order( $order );
				$orderlist[] = $order_data;
			}
		}

		$response_data->limit = $this->get_payload( 'limit', 10 );
		$response_data->page  = $this->get_payload( 'page', 1 );
		if ( $response_data->set_total_records( $orders->total ) ) {
			$response_data->rowdata = $orderlist;
		}

		$this->response->set_response( true, 'Order found', $response_data );
		return $this->response;
	}
	/**
	 * The order list is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function orders_for_refund() {
		$response_data = new API_Data_Response();
		$args          = array(
			'status'        => array( 'completed' ),
			'limit'         => $this->get_payload( 'limit', 10 ),
			'page'          => $this->get_payload( 'page', 1 ),
			'parent'        =>0,
			'orderby'       => 'date',
			'order'         => 'DESC',
			'paginate'      => true,
		);
		
		if ( ! POS_Settings::is_admin_user() && ! current_user_can( 'can-refund-any-order' ) ) {
			$outlets = get_user_meta( $this->get_current_user_id(), 'outlet_id', true );
			if ( is_array( $outlets ) ) {
				$args['vt_meta_query'][] = array(
					'key'     => '_vtp_outlet_id',
					'value'   => $outlets,
					'compare' => 'IN',
				);
			} else {
				$this->add_error( "You don't have permission to view details of this outlet" );
				$response_data->set_total_records( 0 );
				$this->response->set_response( false, '', $response_data );
				return $this->response->get_response();
			}
		}
		$src_props     = $this->get_payload( 'src_by', array() );
		$sort_by_props = $this->get_payload( 'sort_by', array() );
		POS_Order::order_search_props( $args, $src_props );
		POS_Order::order_sort_param( $sort_by_props, $args );

		$orders = wc_get_orders( $args );

		$orderlist = array();
		if ( ! empty( $orders->orders ) && is_array( $orders->orders ) ) {
			foreach ( $orders->orders as $order ) {
				$order_data  = POS_Order::get_from_woo_order( $order );
				$orderlist[] = $order_data;
			}
		}

		$response_data->limit = $this->get_payload( 'limit', 10 );
		$response_data->page  = $this->get_payload( 'page', 1 );
		if ( $response_data->set_total_records( $orders->total ) ) {
			$response_data->rowdata = $orderlist;
		}

		$this->response->set_response( true, 'Order found', $response_data );
		return $this->response;
	}

	/**
	 * The order list is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function online_order_list() {
		$response_data = new API_Data_Response();
		$args          = array(
			'limit'    => $this->get_payload( 'limit', 10 ),
			'page'     => $this->get_payload( 'page', 1 ),
			'orderby'  => 'date',
			'order'    => 'DESC',
			'parent'=>0,
			'paginate' => true,

		);
		$src_props     = $this->get_payload( 'src_by', array() );
		$sort_by_props = $this->get_payload( 'sort_by', array() );
		POS_Order::order_search_props( $args, $src_props );
		POS_Order::order_sort_param( $sort_by_props, $args );
		$args['vt_meta_query'][] = array(
			'key'     => '_is_vitepos',
			'value'   => 'Y',
			'compare' => 'NOT EXISTS',
		);
		$orders                  = wc_get_orders( $args );
		$orderlist               = array();
		if ( ! empty( $orders->orders ) && is_array( $orders->orders ) ) {
			foreach ( $orders->orders as $order ) {
				$order_data  = POS_Order::get_from_woo_order( $order );
				$orderlist[] = $order_data;
			}
		}

		$response_data->limit = $this->get_payload( 'limit', 10 );
		$response_data->page  = $this->get_payload( 'page', 1 );
		if ( $response_data->set_total_records( $orders->total ) ) {
			$response_data->rowdata = $orderlist;
		}

		$this->response->set_response( true, 'Order found', $response_data );
		return $this->response;
	}

	/**
	 * The order details is generated by appsbd
	 *
	 * @param any $data Its string.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function order_details( $data ) {
		if ( ! empty( $data['id'] ) ) {
			$id    = intval( $data['id'] );
			$order = wc_get_order( $id );
			if ( ! empty( $order ) ) {
				$order_data         = POS_Order::get_from_woo_order_details( $order );
				$order_data->status = $order->get_status();
				$this->response->set_response( true, 'Order Found', $order_data );
				return $this->response;
			} else {
				$this->response->set_response( false, 'Order is empty', null );
				return $this->response;
			}
		} else {
			$this->response->set_response( false, 'request id not found', null );
			return $this->response;
		}
	}
	/**
	 * The order details is generated by appsbd
	 *
	 * @param any $data Its string.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function order_refund(  ) {
		$order_id    = $this->get_payload('order_id');
		$user=wp_get_current_user();
		if(empty($user->ID)){
			$this->add_error("Refund is not possible, no cash drawer found");
			$this->response->set_response( false);
			return $this->response->get_response();
		}
		$cash_drawer = Mapbd_Pos_Cash_Drawer::get_by_counter( $this->get_outlet_id(), $this->get_counter_id(), $user->ID );
		if(empty($cash_drawer->id)){
			$this->add_error("Refund is not possible, no cash drawer found");
			$this->response->set_response( false);
			return $this->response->get_response();
		}
		if ( ! empty( $order_id ) ) {
			$order = wc_get_order( $order_id );
			if ( ! empty( $order ) && $order instanceof \WC_Order) {
				$refunded_amount=vitepos_wc_amount($order->get_total_refunded());
				$total_amount=$order->get_total();
				$can_refund_amount=vitepos_wc_amount($order->get_remaining_refund_amount());

				if( 'refunded' == $order->get_status() || 0>$can_refund_amount ) {
					$this->add_error("Order already refunded");
					$this->response->set_response( false);
					return $this->response->get_response();
				}
				$is_full='Y' == $this->get_payload('is_full','N');
				if($refunded_amount>0.0){
					$is_full=false;
				}
				$refund_amount=0.0;
				$is_vitepos= 'Y' == $order->get_meta('_is_vitepos') ;
				$refund_args = array(
					'reason'         => $this->get_payload('reason',null),
					'order_id'       => $order_id,
					'refund_payment' => false,
					'restock_items'  => false, 				);
				if($is_full){
					if($refunded_amount>0){
						$this->add_error("Order has been partially refunded before this request");
						$this->response->set_response( false);
						return $this->response->get_response();
					}
					$refund_amount=(double)$total_amount;

				}else {
					$line_items = array();
					$items = $this->get_payload( "items", [] );
					if ( empty( $items ) || ! is_array( $items ) ) {
						$this->response->set_response( false, 'Partial return but there is not items to refund',
							$refund_args );

						return $this->response->get_response();
					}

					$vitepos_order_items=POS_Order::get_order_items( $order,true);

					foreach ( $items as $item ) {
						if(!isset($vitepos_order_items[$item['item_id']])){
							$refund_args['line_items']=$line_items;
							$this->response->set_response( false, 'Item not found in the main order',$vitepos_order_items );
							return $this->response->get_response();
						}
						if(isset($line_items[ $item['item_id'] ])){
							$this->add_error("Skiped same item multiple line");
							continue;
						}
						$item['refund_qty']=(int)$item['refund_qty'];
						$order_item=&$vitepos_order_items[$item['item_id']];
						if($order_item->refund_qty+$item['refund_qty'] > $order_item->quantity){
							$this->response->set_response( false, 'Item refund quantity is greater than the order quantity',$vitepos_order_items );
							return $this->response->get_response();
						}
						$tax_total=$item['refund_qty']*$order_item->tax_amount;
						$item_total=$item['refund_qty']*(((float)$order_item->price)+((float)$order_item->fee_amount)-((float)$order_item->discount_amount));
						$line_items[ $item['item_id'] ] = array(
							'qty'          => $item['refund_qty'],
							'refund_total' => vitepos_wc_amount( $item_total+$tax_total ),
							'refund_tax'   => $tax_total
						);
						$refund_amount+=(double)$line_items[ $item['item_id'] ]['refund_total'];
					}

					$refund_args['line_items']=$line_items;
														}

				$refund_args['amount']=$refund_amount;
												$refund = wc_create_refund($refund_args);
				if(is_wp_error($refund)){
					$this->add_error($refund->get_error_message());
					$this->response->set_response( false, '',$refund_args );
					return $this->response->get_response();
				}else{
					
					$log_amount=$refund->get_amount();
					$refund->set_refunded_by($user->ID);
					$refund->update_meta_data("_vtp_processed_by",$user->ID);
					$refund->add_meta_data( '_vtp_outlet_id', $this->get_outlet_id() );
					$refund->add_meta_data( '_vtp_counter_id', $this->get_counter_id() );
					$refund->add_meta_data( '_vtp_cash_drawer_id', $cash_drawer->id );
					$refund->save();
					if(!Mapbd_Pos_Cash_Drawer::add_cash_entry('C','Order returned',$log_amount,$cash_drawer,$order_id,'O')){
						$this->add_error("Refunded but cash drawer amount update failed");
					}
					Mapbd_Pos_Cash_Drawer_Types::AddLog( $cash_drawer->id, $user->ID, $order->get_id(), 'R',  $log_amount);

					$order = wc_get_order( $order_id );
					$order->update_meta_data("_vt_has_refund",'Y');
					$order->save();
					$return_type=$is_full?'F':'P';
					/**
					 * Its for process restore stock
					 *
					 * @since 2.0.2
					 */
					do_action('vitepos/action/return-stock',$return_type,$order,$refund,$this->get_outlet_id());

					$refund_return=new \stdClass();
					$refund_return->amount=vitepos_wc_amount($log_amount);
					$refund_return->refund_id=$refund->get_id();
					$this->response->set_response(true,'Refund is completed',$refund_return);
					return $this->response->get_response();
				}

				return $old_refund;



			} else {
				$this->response->set_response( false, 'Order is empty', null );
				return $this->response;
			}
		} else {
			$this->response->set_response( false, 'request id not found', null );
			return $this->response;
		}
	}
	/**
	 * The order details is generated by appsbd
	 *
	 * @param any $data Its string.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function send_email( $data ) {
		if ( ! empty( $data['order_id'] ) ) {
			$id = intval( $data['order_id'] );
			/**
			 * Its for check is there any change before process
			 *
			 * @since 2.0
			 */
			do_action( 'vitepos/action/send-customer-email', $id );
			$this->response->set_response( true, 'email sent', null );

		} else {
			$this->response->set_response( false, 'request id not found', null );
		}

		return $this->response;
	}

	/**
	 * The order payment is generated by appsbd
	 *
	 * @param any $data Its data param.
	 */
	public function order_payment( $data ) {
		return '';
	}
	/**
	 * The order details is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function change_status() {
		$id    = intval( $this->get_payload( 'id' ) );
		$order = new \WC_Order( $id );
		if ( $order ) {
			if ( $order->get_status() == 'vt_in_kitchen' ) {
				$order->update_meta_data( '_vtp_processed_by', get_current_user_id() );
				if ( $order->update_status( 'vt_preparing', 'Order preparing in kitchen', true ) ) {
					$this->add_time_by_status( $order, 'vt_preparing' );
					$msg           = POS_Order::add_resto_order_msg( $id, 'Order has been completed' );
					$updated_order = POS_Order::get_from_woo_order_restro_by_id( $id, false, true );
					$this->response->set_response( true, 'Order stated cooking', $updated_order );
					return $this->response->get_response();
				}
			} else {
				$processed_by = get_current_user_id();
				$outlet_obj   = $this->get_outlet_obj();
				$order->add_order_note( 'Order completed from ' . "{$outlet_obj->name}" );
				$order->update_meta_data( '_vtp_processed_by', $processed_by );
				$order->update_meta_data( '_vtp_outlet_id', $this->get_outlet_id() );
				if ( $order->update_status( $this->get_payload( 'status' ) ) ) {
					$data               = new \stdClass();
					$order_data         = POS_Order::get_from_woo_order_details( $order );
					$data->processed_by = $order_data->processed_by;
					$data->outlet_info  = $order_data->outlet_info;
					$this->response->set_response( true, 'Order updated successfully', $data );
					return $this->response;
				} else {
					$this->response->set_response( false, 'Not updated', null );
					return $this->response;
				}
			}
		} else {
			$this->response->set_response( false, 'Not order found', null );
			return $this->response;
		}
	}
	/**
	 * The order details is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function pay_first_complete_order() {
		$id    = intval( $this->get_payload( 'id' ) );
		$order = new \WC_Order( $id );
		if ( $order ) {
				$order->update_meta_data( '_vtp_processed_by', get_current_user_id() );
			if ( $order->update_status( 'completed', 'Order preparing in kitchen', true ) ) {
				POS_Order::add_time_by_status( $order, 'completed' );
				$msg           = POS_Order::add_resto_order_msg( $id, 'Order has been completed' );
				$updated_order = POS_Order::get_from_woo_order_restro_by_id( $id, false, true );
				$this->response->set_response( true, 'Order has been completed', $updated_order );
				return $this->response->get_response();
			}
		} else {
			$this->response->set_response( false, 'Order not found', null );
			return $this->response;
		}
	}



}
