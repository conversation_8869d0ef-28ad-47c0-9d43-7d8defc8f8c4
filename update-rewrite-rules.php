<?php
/**
 * Update Rewrite Rules for Kitchen Display Screens
 * Run this file once to update WordPress rewrite rules
 * 
 * Usage: Access this file via browser: yoursite.com/wp-content/plugins/vitepos/update-rewrite-rules.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('../../../wp-load.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

// Add the new rewrite rules
function add_kitchen_display_rewrite_rules() {
    // Kitchen Display Screen
    add_rewrite_rule('^kitchen-display/?$', 'index.php?vitepos_kitchen=true', 'top');
    
    // Customer Display Screen
    add_rewrite_rule('^customer-display/?$', 'index.php?vitepos_customer=true', 'top');
    
    // Preparation Screen
    add_rewrite_rule('^preparation-screen/?$', 'index.php?vitepos_preparation=true', 'top');
}

// Add query vars
function add_kitchen_display_query_vars($vars) {
    $vars[] = 'vitepos_kitchen';
    $vars[] = 'vitepos_customer';
    $vars[] = 'vitepos_preparation';
    return $vars;
}

// Execute the update
try {
    // Add rewrite rules
    add_kitchen_display_rewrite_rules();
    
    // Add query vars
    add_filter('query_vars', 'add_kitchen_display_query_vars');
    
    // Flush rewrite rules
    flush_rewrite_rules();
    
    echo '<h1>✅ تم تحديث قواعد الروابط بنجاح</h1>';
    echo '<p>تم إضافة الشاشات التالية:</p>';
    echo '<ul>';
    echo '<li><a href="' . home_url('/kitchen-display') . '" target="_blank">شاشة المطبخ - Kitchen Display</a></li>';
    echo '<li><a href="' . home_url('/customer-display') . '" target="_blank">شاشة عرض الزبون - Customer Display</a></li>';
    echo '<li><a href="' . home_url('/preparation-screen') . '" target="_blank">شاشة التحضير - Preparation Screen</a></li>';
    echo '</ul>';
    echo '<p><strong>ملاحظة:</strong> يمكنك الآن حذف هذا الملف (update-rewrite-rules.php) حيث أنه لم يعد مطلوباً.</p>';
    
} catch (Exception $e) {
    echo '<h1>❌ حدث خطأ أثناء التحديث</h1>';
    echo '<p>رسالة الخطأ: ' . $e->getMessage() . '</p>';
    echo '<p>يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.</p>';
}

// Add some basic styling
echo '<style>
    body { font-family: Arial, sans-serif; margin: 40px; direction: rtl; }
    h1 { color: #333; }
    ul { background: #f9f9f9; padding: 20px; border-radius: 5px; }
    li { margin: 10px 0; }
    a { color: #0073aa; text-decoration: none; }
    a:hover { text-decoration: underline; }
</style>';
?>
