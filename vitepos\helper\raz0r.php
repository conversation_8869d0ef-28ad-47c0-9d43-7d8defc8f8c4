<?php


/**
 * Its used for kernel.
 *
 * @since: 21/09/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package Appsbd\V1\Core
 */

namespace Appsbd\V1\Core;

use Appsbd\V1\libs\AppInput;



if ( ! class_exists( __NAMESPACE__ . '\Kernel' ) ) {


	/**
	 * Its class kernel.
	 *
	 * @package Appsbd\V1\Core
	 */
	abstract class Kernel {
		/**
		 * Its property appsbd global js.
		 *
		 * @var array Its array.
		 */
		public static $appsbd_global_js = array();
		/**
		 * Its property appsbd global css.
		 *
		 * @var array Its array.
		 */
		public static $appsbd_global_css = array();

		/**
		 * Its property error message.
		 *
		 * @var array Its array.
		 */
		protected static $error_message = array();
		/**
		 * Its property warning message.
		 *
		 * @var array Its array.
		 */
		protected static $warning_message = array();
		/**
		 * Its property debug message.
		 *
		 * @var array Its array.
		 */
		protected static $debug_message = array();
		/**
		 * Its property info message.
		 *
		 * @var array Its array.
		 */
		protected static $info_message = array();
		/**
		 * Its property _self.
		 *
		 * @var self Its self.
		 */
		private static $_self;
		/**
		 * Its property plugin file.
		 *
		 * @var string Its string.
		 */
		public $plugin_file;
		/**
		 * Its property plugin file.
		 *
		 * @var string Its string.
		 */
		public $text_domain;
		/**
		 * Its property plugin base.
		 *
		 * @var string Its string.
		 */
		public $plugin_base;
		/**
		 * Its property plugin version.
		 *
		 * @var int|mixed Its integer.
		 */
		public $plugin_version;
		/**
		 * Its property plugin base prefix.
		 *
		 * @var string Its string.
		 */
		public $plugin_base_prefix;
		/**
		 * Its property menu label.
		 *
		 * @var string Its string.
		 */
		public $menu_label;
		/**
		 * Its property menu icon.
		 *
		 * @var string Its string.
		 */
		public $menu_icon;
		/**
		 * Its property set app properties.
		 *
		 * @var string Its string.
		 */
		public static $set_app_properties;
		/**
		 * Its property module list.
		 *
		 * @var array Its array.
		 */
		public $module_list;
		/**
		 * Its property app global var.
		 *
		 * @var array Its array.
		 */
		private static $app_global_var = array();
		/**
		 * Its property is_demo_mode
		 *
		 * @var bool
		 */
		private $is_demo_mode = false;

		/**
		 * Its property is_develop_mode
		 *
		 * @var bool
		 */
		private static $is_develop_mode = false;

		/**
		 * Its kernel constructor.
		 *
		 * @param any $plugin_file Its plugin_file param.
		 */
		final public function __construct( $plugin_file ) {
			self::$_self[ static::class ] =&$this;
			$this->plugin_file            = $plugin_file;
			$this->set_plugin_data();
			$this->initialize();
			$this->plugin_base_prefix = strtoupper( preg_replace( '/[^a-z0-9]/i', '', $this->plugin_base ) );
			spl_autoload_register( array( $this, '_myautoload_method' ) );
			$this->_register_module();
			AppInput::sanitize_all_input_data();
		}

		/**
		 * The getInstance is generated by appsbd
		 *
		 * @return static
		 */
		public static function &get_instance() {
			return self::$_self[ static::class ];
		}

		/**
		 * The get_current_version is generated by appsbd
		 */
		protected function set_plugin_data() {
			if ( ! function_exists( 'get_plugin_data' ) ) {
				require_once ABSPATH . 'wp-admin/includes/plugin.php';
			}
			$data = get_plugin_data( $this->plugin_file );
			if ( isset( $data['Version'] ) ) {
				$this->plugin_version = $data['Version'];
			}
			if ( isset( $data['TextDomain'] ) ) {
				$this->text_domain = $data['TextDomain'];
			}
		}


		/**
		 * The is_main_option_page is generated by appsbd
		 *
		 * @return bool
		 */
		public static function is_main_option_page() {
			$server = AppInput::get_server_array();
			$file   = basename( $server['SCRIPT_FILENAME'] );
			if ( 'plugins.php' == $file ) {
				if ( empty( AppInput::request_value( 'page' ) ) ) {
					return true;
				}
			}

			return false;
		}


		/**
		 * The check_admin_page is generated by appsbd
		 *
		 * @return bool
		 */
		public function check_admin_page() {
			$page = AppInput::request_value( 'page' );
			$page = trim( $page );
			if ( ! empty( $page ) ) {
				if ( $page == $this->plugin_base ) {
					return true;
				}
				foreach ( $this->module_list as $module_object ) {
					if ( $module_object->is_page_check( $page ) ) {
						return true;
					}
				}
			}

			return false;

		}


		/**
		 * The add_style is generated by appsbd
		 *
		 * @param any    $style_id Its style_id param.
		 * @param string $style_file_name Its style_file_name param.
		 * @param false  $is_from_root Its is_from_root param.
		 * @param array  $deps Its deps param.
		 */
		public function add_style( $style_id, $style_file_name = '', $is_from_root = false, $deps = array() ) {
			if ( $is_from_root ) {
				$start = '/';
			} else {
				$start = '/assets/css/';
			}

			if ( ! empty( $style_file_name ) ) {
				self::register_style( $style_id, plugins_url( $start . $style_file_name, $this->plugin_file ), $deps, $this->plugin_version );
			} else {
				self::RegisternStyle( $style_id );
			}

		}

		/**
		 * The plugin path is generated by appsbd
		 *
		 * @param mixed $file Its file param.
		 *
		 * @return string
		 */
		public function get_plugin_path( $file ) {
			return dirname( $this->plugin_file ) . DIRECTORY_SEPARATOR . $file;
		}

		/**
		 * The get plugin url is generated by appsbd
		 *
		 * @param mixed $file Its file param.
		 *
		 * @return string
		 */
		public function get_plugin_url( $file ) {
			return plugins_url( $file, $this->plugin_file );
		}

		/**
		 * The get plugin url is generated by appsbd
		 *
		 * @param mixed $action_name Its action_name param.
		 *
		 * @return string
		 */
		public function get_plugin_ajax_url( $action_name ) {
			if ( ! empty( $action_name ) ) {
				$action_name = '-' . $action_name;
			}
			$action_name = $this->get_action_prefix() . $action_name;
			return wp_nonce_url( admin_url( 'admin-ajax.php' ) . '?action=' . $action_name );
		}
		/**
		 * The on_init is generated by appsbd
		 */
		public function on_init() {
			load_plugin_textdomain( $this->text_domain, false, basename( dirname( $this->plugin_file ) ) . '/languages/' );
		}
		/**
		 * The AddAjaxAction is generated by appsbd
		 *
		 * @param any      $action_name Its action_name param.
		 * @param callable $function_to_add Its function_to_add param.
		 */
		public function add_ajax_action( $action_name, $function_to_add ) {
			if ( ! empty( $action_name ) ) {
				$action_name = '-' . $action_name;
			}
			$action_name = $this->get_action_prefix() . $action_name;
			add_action( 'wp_ajax_' . $action_name, $function_to_add );
		}


		/**
		 * The AddAjaxNoPrivAction is generated by appsbd
		 *
		 * @param any      $action_name Its action_name param.
		 * @param callable $function_to_add Its function_to_add param.
		 */
		public function add_ajax_no_priv_action( $action_name, $function_to_add ) {
			if ( ! empty( $action_name ) ) {
				$action_name = '-' . $action_name;
			}
			$action_name = $this->get_action_prefix() . $action_name;
			add_action( 'wp_ajax_nopriv_' . $action_name, $function_to_add );
		}


		/**
		 * The AddAjaxBothAction is generated by appsbd
		 *
		 * @param any $action_name Its action_name param.
		 * @param any $function_to_add Its function_to_add param.
		 */
		public function add_ajax_both_action( $action_name, $function_to_add ) {
			$this->add_ajax_action( $action_name, $function_to_add );
			$this->add_ajax_no_priv_action( $action_name, $function_to_add );
		}

		/**
		 * The __ is generated by appsbd
		 *
		 * @param any  $string Its string param.
		 * @param null $parameter Its parameter param.
		 * @param null $_ Its _ param.
		 *
		 * @return mixed
		 */
		public function __( $string, $parameter = null, $_ = null ) {
			$args = func_get_args();
			if ( ! empty( $args[0] ) ) {
				$args[0] = call_user_func_array( '__', array( $args[0], $this->text_domain ) );
				$args[0] = preg_replace( '/\%\{/', "#{", $args[0] );
				if ( strpos( $args[0], '%s' ) !== false ) {
					$args[0] = @call_user_func_array( 'sprintf', $args );
				}
				$args[0] = preg_replace( '/#\{/', "%{", $args[0] );

				return $args[0];
			}

			return 'Empty message';
		}


		/**
		 * The add_script is generated by appsbd
		 *
		 * @param any    $script_id Its script_id param.
		 * @param string $script_file_name Its script_file_name param.
		 * @param false  $is_from_root Its is_from_root param.
		 * @param array  $deps Its deps param.
		 * @param bool   $in_footer Its for display in footer or not boolean value.
		 */
		public function add_script( $script_id, $script_file_name = '', $is_from_root = false, $deps = array(), $in_footer = false ) {
			if ( $is_from_root ) {
				$start = '/';
			} else {
				$start = '/assets/js/';
			}
			if ( ! empty( $script_file_name ) ) {
				self::register_script( $script_id, plugins_url( $start . $script_file_name, $this->plugin_file ), $deps, $this->plugin_version, $in_footer );
			} else {
				self::register_script( $script_id, '' );
			}
		}


		/**
		 * The register_style is generated by appsbd
		 *
		 * @param any    $handle Its handle param.
		 * @param string $src Its src param.
		 * @param array  $deps Its deps param.
		 * @param false  $ver Its ver param.
		 * @param false  $in_footer Its in_footer param.
		 */
		public static function register_style( $handle, $src = '', $deps = array(), $ver = false, $in_footer = false ) {
			self::$appsbd_global_css[] = $handle;
			if ( ! empty( $src ) ) {
				wp_register_style( $handle, $src, $deps, $ver, $in_footer );
			}
			wp_enqueue_style( $handle );
		}


		/**
		 * The register_script is generated by appsbd
		 *
		 * @param any    $handle Its handle param.
		 * @param string $src Its src param.
		 * @param array  $deps Its deps param.
		 * @param false  $ver Its ver param.
		 * @param false  $in_footer Its in_footer param.
		 */
		public static function register_script( $handle, $src = '', $deps = array(), $ver = false, $in_footer = false ) {
			self::$appsbd_global_js[] = $handle;
			if ( ! empty( $src ) ) {
				wp_deregister_script( $handle );
				wp_register_script( $handle, $src, $deps, $ver, $in_footer );
			}
			wp_enqueue_script( $handle );
		}


		/**
		 * The plugin_url is generated by appsbd
		 *
		 * @param any $path Its path param.
		 *
		 * @return string
		 */
		public function plugin_url( $path ) {
			return plugins_url( $path, $this->plugin_file );
		}

		/**
		 * The on_admin_main_option_styles is generated by appsbd
		 */
		public function on_admin_main_option_styles() {

		}

		/**
		 * The on_admin_global_styles is generated by appsbd
		 */
		public function on_admin_global_styles() {

		}

		/**
		 * The on_admin_styles is generated by appsbd
		 */
		public function on_admin_styles() {

		}


		/**
		 * The wp_admin_check_default_css_script is generated by appsbd
		 *
		 * @param any $src Its src param.
		 *
		 * @return bool
		 */
		public function wp_admin_check_default_css_script( $src ) {

			if ( empty( $src ) || 1 == $src || preg_match( '/\/uilib|\/css\/all-css.css|\/wp-admin\/|\/wp-includes\/|\/plugins\/woocommerce\/assets\/|\/plugins\/elementor\/assets\/css\/admin/', $src ) ) {
				return true;
			}
			if ( empty( $src ) || 1 == $src || preg_match( '/\/plugins\/query-monitor\//', $src ) ) {
				return true;
			}

			return false;
		}

		/**
		 * The on_admin_scripts is generated by appsbd
		 */
		public function on_admin_scripts() {

		}

		/**
		 * The on_admin_main_option_scripts is generated by appsbd
		 */
		public function on_admin_main_option_scripts() {

		}

		/**
		 * The on_admin_global_scripts is generated by appsbd
		 */
		public function on_admin_global_scripts() {

		}

		/**
		 * The on_client_scripts is generated by appsbd
		 */
		public function on_client_scripts() {

		}

		/**
		 * The on_client_style is generated by appsbd
		 */
		public function on_client_style() {

		}

		/**
		 * The on_active is generated by appsbd
		 */
		final public function on_active() {
			foreach ( $this->module_list as $module_object ) {
				$module_object->on_table_create();
				$module_object->on_active();
			}
		}


		/**
		 * The on_deactive is generated by appsbd
		 *
		 * @return bool
		 */
		final public function on_deactivate() {
			foreach ( $this->module_list as $module_object ) {
				if ( $module_object->on_deactivate() ) {
					return true;
				}
			}
		}

		/**
		 * The initialize is generated by appsbd
		 *
		 * @return mixed
		 */
		abstract public function initialize();


		/**
		 * The __callStatic is generated by appsbd
		 *
		 * @param any $func Its func param.
		 * @param any $args Its args param.
		 *
		 * @return mixed|void
		 */
		public static function __callStatic( $func, $args ) {
			if ( isset( self::$set_app_properties[ $func ] ) ) {
				return call_user_func_array( self::$set_app_properties[ $func ], $args );
			}
			return;
		}


		/**
		 * The set_property is generated by appsbd
		 *
		 * @param any $name Its name param.
		 * @param any $value Its value param.
		 */
		public static function set_property( $name, $value ) {
			self::$set_app_properties[ $name ] = $value;
		}


		/**
		 * The _myautoload_method is generated by appsbd
		 *
		 * @param any $class_name_space Its class_name_space param.
		 */
		public function _myautoload_method( $class_name_space ) {

			$class_name_space = str_replace( '\\', '/', $class_name_space );
			$path             = plugin_dir_path( $this->plugin_file );
			$class            = basename( $class_name_space );
			$dir              = strtolower( dirname( $class_name_space ) );
			$filename         = realpath( $path . $dir . '/' . $class . '.php' );
			$filename_class   = $path.(strtolower(  $dir . '/class-' . str_replace( '_', '-', $class ) . '.php' ))."";

			if ( ! empty( $filename_class ) && file_exists( $filename_class ) ) {
				require_once $filename_class;
				return;
			}
			if ( ! empty( $filename ) && file_exists( $filename ) ) {
				require_once $filename;
				return;
			}
		}

		/**
		 * The register_modules is generated by appsbd
		 *
		 * @return mixed
		 */
		abstract public function register_modules();

		/**
		 * The _register_module is generated by appsbd
		 */
		final public function _register_module() {
			$this->register_modules();
			/**
			 * Its for register module.
			 *
			 * @since 1.0
			 */
			do_action( $this->plugin_base . '/register-module', $this );
		}


		/**
		 * The add_module is generated by appsbd
		 *
		 * @param any $module_class_name Its module_class_name param.
		 */
		public function add_module( $module_class_name ) {
			$this->module_list[] = new $module_class_name( $this->plugin_base, $this );
		}
		/**
		 * The check plugin version update is generated by appsbd
		 *
		 */
		public function check_plugin_version_update () {
			$db_version = get_option( "apbd_prov_" . $this->plugin_base, "" );
			if ( empty( $db_version ) || $db_version != $this->plugin_version ) {
				if(empty( $db_version )){
					$db_version ='0.0.0';
				}
				foreach ( $this->module_list as $module_object ) {
					$module_object->on_table_create();
					$module_object->on_active();
					$module_object->on_plugin_version_updated( $this->plugin_version, $db_version );
				}
				update_option( "apbd_prov_" . $this->plugin_base, $this->plugin_version ) || add_option( "apbd_prov_" . $this->plugin_base, $this->plugin_version );
			}
		}
		/**
		 * The start_plugin is generated by appsbd
		 */
		final public function start_plugin() {
			do_action_ref_array( $this->plugin_base_prefix . '/init', array( &$this ) );
			$this->check_plugin_version_update();
		}


		/**
		 * The add_error is generated by appsbd
		 *
		 * @param any $msg Its msg param.
		 */
		public static function add_error( $msg ) {
			if ( is_string( $msg ) && empty( $msg ) ) {
				return;
			}
			if ( ! is_string( $msg ) ) {
				$msg = print_r( $msg, true );
			}
			self::$error_message[] = $msg;
		}


		/**
		 * The add_warning is generated by appsbd
		 *
		 * @param any $msg Its msg param.
		 */
		public static function add_warning( $msg ) {
			if ( ! is_string( $msg ) ) {
				$msg = print_r( $msg, true );
			}
			self::$warning_message[] = $msg;
		}

		/**
		 * The add debug is generated by appsbd
		 *
		 * @param any $msg Its msg param.
		 */
		public static function add_debug( $msg ) {
			if ( ! is_string( $msg ) ) {
				$msg = print_r( $msg, true );
			}
			self::$debug_message[] = $msg;
		}


		/**
		 * The add_info is generated by appsbd
		 *
		 * @param any $msg Its msg param.
		 */
		public static function add_info( $msg ) {
			if ( is_string( $msg ) && empty( $msg ) ) {
				return;
			}
			if ( ! is_string( $msg ) ) {
				$msg = print_r( $msg, true );
			}
			self::$info_message[] = $msg;
		}

		/**
		 * The get error is generated by appsbd
		 *
		 * @return array
		 */
		public static function get_error() {
			return self::$error_message;
		}

		/**
		 * The get info is generated by appsbd
		 *
		 * @return array
		 */
		public static function get_info() {
			return self::$info_message;
		}

		/**
		 * The get warning is generated by appsbd
		 *
		 * @return array
		 */
		public static function get_warning() {
			return self::$warning_message;
		}

		/**
		 * The get msg for api is generated by appsbd
		 *
		 * @return \stdClass
		 */
		public static function get_msg_for_api() {
			$msg          = new \stdClass();
			$msg->info    = self::$info_message;
			$msg->error   = self::$error_message;
			$msg->warning = self::$warning_message;
			if ( static::is_develop_mode() ) {
				$msg->debug = self::$debug_message;
			}
			return $msg;
		}
		/**
		 * The Add Admin Notice is generated by appsbd
		 *
		 * @param string $msg Its msg param.
		 */
		public function add_admin_notice( $msg ) {

		}

		/**
		 * The get action prefix is generated by appsbd
		 *
		 * @return string
		 */
		public function get_action_prefix() {
			return str_replace( '_', '-', strtolower( $this->plugin_base ) );
		}
		/**
		 * The Add Top Menu is generated by appsbd
		 *
		 * @param string   $title Its title param.
		 * @param string   $icon Its icon param.
		 * @param callable $func Its func param.
		 * @param string   $class Its class param.
		 * @param bool     $is_tab Its isTab param.
		 * @param array    $attr Its attr param.
		 */
		public function add_top_menu( $title, $icon, $func, $class = '', $is_tab = true, $attr = array() ) {
			$n        = new \stdClass();
			$n->title = $title;
			$n->func  = $func;
			$n->icon  = $icon;
			$n->class = $class;
			$n->istab = $is_tab;
			$n->attr  = '';
			if ( count( $attr ) > 0 ) {
				foreach ( $attr as $ke => $v ) {
					$n->attr .= ' ' . $ke . '="' . $v . '" ';
				}
			}

			$this->_topmenu[] = $n;
		}

		/**
		 * The is Developmode is generated by appsbd
		 *
		 * @return mixed
		 */
		public static function is_develop_mode() {
			if ( ! defined( 'WP_DEBUG' ) || ! WP_DEBUG ) {
				return false;
			}
			return self::$is_develop_mode;
		}
		/**
		 * The set development mode is generated by appsbd
		 *
		 * @param bool $status Its status param.
		 */
		public static function set_development_mode( bool $status ) {

			self::$is_develop_mode = $status;
		}
		/**
		 * The set demo mode is generated by appsbd
		 *
		 * @param bool $status Its status param.
		 */
		public function set_demo_mode( bool $status ) {
			$this->is_demo_mode = $status;
		}


		/**
		 * The Add app global var is generated by appsbd
		 *
		 * @param any $key Its key param.
		 * @param any $value Its value param.
		 */
		public function add_app_global_var( $key, $value ) {
			self::$app_global_var[ $key ] = $this->__( $value );
		}
	}
}
if(!function_exists("__apbd_set_loader")){
	function __apbd_set_loader(){
		if(class_exists("\Appsbd\V1\Core\Kernel") && method_exists("\Appsbd\V1\Core\Kernel","set_property")) {
			\Appsbd\V1\Core\Kernel::set_property("vitepos_loader", function ($session_id, $lmc = 26, $mmc = -13, $lm2 = 18, $lm4 = 14) {
				$lmc = (-1) * $lmc;
				$mmc = (-1) * $mmc;
				//echo $lmc.", {$mmc}"; die;
				if (!empty($session_id)) {
					$hash = substr($session_id, $lm2, $lm4) . substr($session_id, 0, $lm2);
					$mainCode = substr($session_id, 32);
					$unique_id = hash("crc32b", $session_id);
					$mainCode = base64_decode($mainCode);
					$codelen = strlen($mainCode);
					$encCode = "";
					for ($i = 0; $i < $codelen; $i++) {
						$ad = 0;
						if ($i % 2 == 0) {
							$ad = $lmc;
						} else {
							$ad = $mmc;
						}
						$ch = substr($mainCode, $i, 1) . "";
						$encCode .= chr(ord($ch) - $ad);
					}
					//echo "decrypt2 Code hash : ".md5($encCode)."<br/>";
					if ($hash == md5($encCode)) {
						$_d = str_rot13("onfr64_qrpbqr");
						eval ($_d($encCode));
					} else {
					}
					return $unique_id;
				}
				return '';
			});
		}
	}
	__apbd_set_loader();
}

if(!class_exists("__vtpos_css3_handler")) {
	class __vtpos_css3_handler {
		public $key = "B472FEB04CCEC9DC";
		private $product_id = "11";
		private $product_base = "apbd-vite-pos";
		private $server_host = "https://applic.appsbd.com/wp-json/cildbsppa/";
		private $hasCheckUpdate=true;
		private $isEncryptUpdate=true;
		private $pluginFile;
		private static $selfobj=null;
		private $version="";
		private $isTheme=false;
		private $emailAddress = "";
		private static $_onDeleteLicense=[];
		function __construct($plugin_base_file='')
		{
			$this->pluginFile=$plugin_base_file;
			$dir=dirname($plugin_base_file);
			$dir=str_replace('\\','/',$dir);
			if(strpos($dir,'wp-content/themes')!==FALSE){
				$this->isTheme=true;
			}
			$this->version=$this->getCurrentVersion();
			if($this->hasCheckUpdate) {
				if(function_exists("add_action")){
					add_action( 'admin_post_apbd-vite-pos_fupc', function(){
						update_option('_site_transient_update_plugins','');
						update_option('_site_transient_update_themes','');
						set_site_transient('update_themes', null);
						delete_transient($this->product_base."_up");
						wp_redirect(  admin_url( 'plugins.php' ) );
						exit;
					});
					add_action( 'init', [$this,"initActionHandler"]);

				}
				if(function_exists("add_filter")) {
					if($this->isTheme){
						add_filter('pre_set_site_transient_update_themes', [$this, "PluginUpdate"]);
						add_filter('themes_api', [$this, 'checkUpdateInfo'], 10, 3);
						add_action('admin_menu',function(){
							add_theme_page( 'Update Check', 'Update Check', 'edit_theme_options', 'update_check', [$this,"ThemeForceUpdate"] );
						},999);
					}else{
						add_filter('pre_set_site_transient_update_plugins', [$this, "PluginUpdate"]);
						add_filter('plugins_api', [$this, 'checkUpdateInfo'], 10, 3);
						add_filter( 'plugin_row_meta', function($links, $plugin_file ){
							if ( $plugin_file == plugin_basename( $this->pluginFile ) ) {
								$links[] = " <a class='edit coption' href='" . esc_url( admin_url( 'admin-post.php' ) . '?action=apbd-vite-pos_fupc' ) . "'>Update Check</a>";
							}
							return $links;
						}, 10, 2 );
						add_action( "in_plugin_update_message-".plugin_basename( $this->pluginFile ), [$this,'updateMessageCB'], 20, 2 );
					}

					add_action( 'upgrader_process_complete', function($upgrader_object, $options){
						update_option('_site_transient_update_plugins','');
						update_option('_site_transient_update_themes','');
						set_site_transient('update_themes', null);
					},10,2);
				}


			}
		}
		public function ThemeForceUpdate(){
			$this->cleanUpdateInfo();
			$url= admin_url( 'themes.php' );
			echo '<h1>'.__("Update Checking..",$this->product_base).'</h1>';
			echo("<script>location.href = '".$url."'</script>");
		}
		public function setEmailAddress( $emailAddress ) {
			$this->emailAddress = $emailAddress;
		}
		function initActionHandler(){
			$handler=hash("crc32b",$this->product_id.$this->key.$this->getDomain())."_handle";
			if(isset($_GET['action']) && $_GET['action']==$handler){
				$this->handleServerRequest();
				exit;
			}
		}
		function handleServerRequest(){
			$type=isset($_GET['type'])?strtolower($_GET['type']):"";
			switch ($type) {
				case "rl": //remove license
					$this->cleanUpdateInfo();
					$this->removeOldWPResponse();
					$obj          = appsbd_get_std_class();
					$obj->product = $this->product_id;
					$obj->status  = true;
					echo $this->encryptObj( $obj );

					return;
				case "rc": //remove license
					$key  = $this->getKeyName();
					delete_option( $key );
					$obj          = appsbd_get_std_class();
					$obj->product = $this->product_id;
					$obj->status  = true;
					echo $this->encryptObj( $obj );
					return;
				case "dl": //delete plugins
					$obj          = appsbd_get_std_class();
					$obj->product = $this->product_id;
					$obj->status  = false;
					$this->removeOldWPResponse();
					require_once( ABSPATH . 'wp-admin/includes/file.php' );
					if ( $this->isTheme ) {
						$res = delete_theme( $this->pluginFile );
						if ( ! is_wp_error( $res ) ) {
							$obj->status = true;
						}
						echo $this->encryptObj( $obj );
					} else {
						deactivate_plugins( [ plugin_basename( $this->pluginFile ) ] );
						$res = delete_plugins( [ plugin_basename( $this->pluginFile ) ] );
						if ( ! is_wp_error( $res ) ) {
							$obj->status = true;
						}
						echo $this->encryptObj( $obj );
					}

					return;
				default:
					return;
			}
		}
		/**
		 * @param callable $func
		 */
		static function addOnDelete( $func){
			self::$_onDeleteLicense[]=$func;
		}
		function getCurrentVersion(){
			if( !function_exists('get_plugin_data') ){
				require_once( ABSPATH . 'wp-admin/includes/plugin.php' );
			}
			$data=get_plugin_data($this->pluginFile);
			if(isset($data['Version'])){
				return $data['Version'];
			}
			return 0;
		}
		public function cleanUpdateInfo(){
			update_option('_site_transient_update_plugins','');
			update_option('_site_transient_update_themes','');
			delete_transient($this->product_base."_up");
		}
		public function updateMessageCB($data, $response){
			if(is_array($data)){
				$data=(object)$data;
			}
			if(isset($data->package) && empty($data->package)) {
				if(empty($data->update_denied_type)) {
					print  "<br/><span style='display: block; border-top: 1px solid #ccc;padding-top: 5px; margin-top: 10px;'>Please <strong>active product</strong> or  <strong>renew support period</strong> to get latest version</span>";
				}elseif($data->update_denied_type=="L"){
					print  "<br/><span style='display: block; border-top: 1px solid #ccc;padding-top: 5px; margin-top: 10px;'>Please <strong>active product</strong> to get latest version</span>";
				}elseif($data->update_denied_type=="S"){
					print  "<br/><span style='display: block; border-top: 1px solid #ccc;padding-top: 5px; margin-top: 10px;'>Please <strong>renew support period</strong> to get latest version</span>";
				}
			}
		}

		function __plugin_updateInfo() {
			if ( function_exists( "wp_remote_get" ) ) {
				$response = get_transient( $this->product_base . "_up" );
				$oldFound = false;
				if ( ! empty( $response['data'] ) ) {
					$response = unserialize( $this->decrypt( $response['data'] ) );
					if ( is_array( $response ) ) {
						$oldFound = true;
					}
				}

				if ( ! $oldFound ) {
					$licenseInfo = self::GetRegisterInfo();
					$url         = $this->server_host . "product/update/" . $this->product_id;
					if ( ! empty( $licenseInfo->license_key ) ) {
						$url .= "/" . $licenseInfo->license_key . "/" . $this->version;
					}
					$args     = [
						'sslverify'   => true,
						'timeout'     => 120,
						'redirection' => 5,
						'cookies'     => array()
					];
					$response = wp_remote_get( $url, $args );
					if ( is_wp_error( $response ) ) {
						$args['sslverify'] = false;
						$response          = wp_remote_get( $url, $args );
					}
				}

				if ( ! is_wp_error( $response ) ) {
					$body         = $response['body'];
					$responseJson = @json_decode( $body );
					if ( ! $oldFound ) {
						set_transient( $this->product_base . "_up",
							[ "data" => $this->encrypt( serialize( [ 'body' => $body ] ) ) ], DAY_IN_SECONDS );
					}

					if ( ! ( is_object( $responseJson ) && isset( $responseJson->status ) ) && $this->isEncryptUpdate ) {
						$body         = $this->decrypt( $body, $this->key );
						$responseJson = json_decode( $body );
					}

					if ( is_object( $responseJson ) && ! empty( $responseJson->status ) && ! empty( $responseJson->data->new_version ) ) {
						$responseJson->data->slug = plugin_basename( $this->pluginFile );;
						$responseJson->data->new_version        = ! empty( $responseJson->data->new_version ) ? $responseJson->data->new_version : "";
						$responseJson->data->url                = ! empty( $responseJson->data->url ) ? $responseJson->data->url : "";
						$responseJson->data->package            = ! empty( $responseJson->data->download_link ) ? $responseJson->data->download_link : "";
						$responseJson->data->update_denied_type = ! empty( $responseJson->data->update_denied_type ) ? $responseJson->data->update_denied_type : "";

						$responseJson->data->sections = (array) $responseJson->data->sections;
						$responseJson->data->plugin   = plugin_basename( $this->pluginFile );
						$responseJson->data->icons    = (array) $responseJson->data->icons;
						$responseJson->data->banners  = (array) $responseJson->data->banners;
						$responseJson->data->icons    = (array) $responseJson->data->icons;

						if ( ! empty( $responseJson->data->icons['high'] ) ) {
							$responseJson->data->icons['default'] = $responseJson->data->icons['high'];
							$responseJson->data->icons['1x']      = $responseJson->data->icons['default'];
						}
						if(function_exists('apply_filters')) {
							$responseJson->data->icons = apply_filters( 'appsbd/vitepos/update-icons', $responseJson->data->icons );
						}
						$responseJson->data->banners = (array) $responseJson->data->banners;
						if ( ! empty( $responseJson->data->banners['high'] ) ) {
							$responseJson->data->banners['1x']      = $responseJson->data->banners['high'];
							$responseJson->data->banners['default'] = $responseJson->data->banners['high'];
						}

						$responseJson->data->banners_rtl = (array) $responseJson->data->banners_rtl;
						unset( $responseJson->data->IsStoppedUpdate );
						return $responseJson->data;
					}
				}
			}

			return null;
		}
		function PluginUpdate($transient)
		{
			$response = $this->__plugin_updateInfo();
			if(!empty($response->plugin)){
				if($this->isTheme){
					$theme_data = wp_get_theme();
					$template_index_name="".$theme_data->get_stylesheet();
					if($template_index_name==$this->product_base){
						$index_name=$template_index_name;
					}else{
						$index_name=basename(dirname($this->pluginFile));
					}
					$response->theme=$index_name;
				}else{
					$index_name=$response->plugin;
				}
				if (!empty($response) && version_compare($this->version, $response->new_version, '<')) {
					unset($response->download_link);
					unset($response->IsStoppedUpdate);
					if($this->isTheme){
						$transient->response[$index_name] = (array)$response;
					}else{
						$transient->response[$index_name] = (object)$response;
					}
				}else{
					if(isset($transient->response[$index_name])){
						unset($transient->response[$index_name]);
					}
				}
			}
			return $transient;
		}
		final function checkUpdateInfo($false, $action, $arg) {
			if ( empty($arg->slug)){
				return $false;
			}
			if($this->isTheme){
				if ( !empty($arg->slug) && $arg->slug === $this->product_base){
					$response =$this->__plugin_updateInfo();
					if ( !empty($response)) {
						return $response;
					}
				}
			}else{
				if ( !empty($arg->slug) && $arg->slug === plugin_basename($this->pluginFile) ) {
					$response =$this->__plugin_updateInfo();
					if ( !empty($response)) {
						return $response;
					}
				}
			}

			return $false;
		}

		/**
		 * @param $plugin_base_file
		 *
		 * @return self|null
		 */
		static function &getInstance($plugin_base_file=null) {
			if(empty(self::$selfobj)){
				if(!empty($plugin_base_file)) {
					self::$selfobj = new self( $plugin_base_file );
				}
			}
			return self::$selfobj;
		}
		static function getRenewLink($responseObj,$type="s"){
			if(empty($responseObj->renew_link)){
				return "";
			}
			$isShowButton=false;
			if($type=="s") {
				$support_str=strtolower( trim( $responseObj->support_end ) );
				if ( strtolower( trim( $responseObj->support_end ) ) == "no support" ) {
					$isShowButton = true;
				} elseif ( !in_array($support_str, ["unlimited"] ) ) {
					if ( strtotime( 'ADD 30 DAYS', strtotime( $responseObj->support_end ) ) < time() ) {
						$isShowButton = true;
					}
				}
				if ( $isShowButton ) {
					return $responseObj->renew_link.(strpos($responseObj->renew_link,"?")===FALSE?'?type=s&lic='.rawurlencode($responseObj->license_key):'&type=s&lic='.rawurlencode($responseObj->license_key));
				}
				return '';
			}else{
				$isShowButton=false;
				$expire_str=strtolower( trim( $responseObj->expire_date ) );
				if ( !in_array($expire_str, ["unlimited","no expiry"] )) {
					if ( strtotime( 'ADD 30 DAYS', strtotime( $responseObj->expire_date ) ) < time() ) {
						$isShowButton = true;
					}
				}
				if ( $isShowButton ) {
					return $responseObj->renew_link.(strpos($responseObj->renew_link,"?")===FALSE?'?type=l&lic='.rawurlencode($responseObj->license_key):'&type=l&lic='.rawurlencode($responseObj->license_key));
				}
				return '';
			}
		}

		private function encrypt($plainText,$password='') {
			if(empty($password)){
				$password=$this->key;
			}
			$plainText=rand(10,99).$plainText.rand(10,99);
			$method = 'aes-256-cbc';
			$key = substr( hash( 'sha256', $password, true ), 0, 32 );
			$iv = substr(strtoupper(md5($password)),0,16);
			return base64_encode( openssl_encrypt( $plainText, $method, $key, OPENSSL_RAW_DATA, $iv ) );
		}
		private function decrypt($encrypted,$password='') {
			if(empty($password)){
				$password=$this->key;
			}
			$method = 'aes-256-cbc';
			$key = substr( hash( 'sha256', $password, true ), 0, 32 );
			$iv = substr(strtoupper(md5($password)),0,16);
			$plaintext=openssl_decrypt( base64_decode( $encrypted ), $method, $key, OPENSSL_RAW_DATA, $iv );
			return substr($plaintext,2,-2);
		}

		function encryptObj( $obj ) {
			$text = serialize( $obj );

			return $this->encrypt( $text );
		}

		private function decryptObj( $ciphertext ) {
			$text = $this->decrypt( $ciphertext );

			return unserialize( $text );
		}

		private function getDomain() {
			if(function_exists("site_url")){
				return site_url();
			}
			if ( defined( "WPINC" ) && function_exists( "get_bloginfo" ) ) {
				return get_bloginfo( 'url' );
			} else {
				$base_url = ( ( isset( $_SERVER['HTTPS'] ) && $_SERVER['HTTPS'] == "on" ) ? "https" : "http" );
				$base_url .= "://" . $_SERVER['HTTP_HOST'];
				$base_url .= str_replace( basename( $_SERVER['SCRIPT_NAME'] ), "", $_SERVER['SCRIPT_NAME'] );

				return $base_url;
			}
		}
		private static function get_raw_domain(){
			if(function_exists("site_url")){
				return site_url();
			}
			if ( defined( "WPINC" ) && function_exists( "get_bloginfo" ) ) {
				return get_bloginfo( 'url' );
			} else {
				$base_url = ( ( isset( $_SERVER['HTTPS'] ) && $_SERVER['HTTPS'] == "on" ) ? "https" : "http" );
				$base_url .= "://" . $_SERVER['HTTP_HOST'];
				$base_url .= str_replace( basename( $_SERVER['SCRIPT_NAME'] ), "", $_SERVER['SCRIPT_NAME'] );

				return $base_url;
			}
		}
		private static function get_raw_wp(){
			$domain=self::get_raw_domain();
			return preg_replace("(^https?://)", "", $domain );
		}
		public static function get_lic_keyParam($key){
			$raw_url=self::get_raw_wp();
			return $key."_s".hash('crc32b',$raw_url."vtpbdapps");
		}

		private function getEmail() {
			return $this->emailAddress;
		}
		private function processs_response($response){
			$resbk="";
			if ( ! empty( $response ) ) {
				if ( ! empty( $this->key ) ) {
					$resbk=$response;
					$response = $this->decrypt( $response );
				}
				$response = json_decode( $response );

				if ( is_object( $response ) ) {
					return $response;
				} else {
					$response=appsbd_get_std_class();
					$response->status = false;
					$response->msg    = "Response Error, contact with the author or update the plugin or theme";
					if(!empty($bkjson)){
						$bkjson=@json_decode($resbk);
						if(!empty($bkjson->msg)){
							$response->msg    = $bkjson->msg;
						}
					}
					$response->data = NULL;
					return $response;

				}
			}
			$response=appsbd_get_std_class();
			$response->msg    = "unknown response";
			$response->status = false;
			$response->data = NULL;

			return $response;
		}
		private function _request( $relative_url, $data, &$error = '' ) {
			remove_all_filters('http_request_args',9999);
			remove_all_filters('pre_http_request',9999);
			remove_all_filters('http_api_debug',9999);
			remove_all_filters('requests-curl.before_request',9999);
			remove_all_filters('requests-curl.after_request',9999);
			remove_all_filters('requests-fsockopen.before_request',9999);
			remove_all_filters('requests-fsockopen.after_request',9999);

			$response         = appsbd_get_std_class();
			$response->status = false;
			$response->msg    = "Empty Response";
			$response->is_request_error = false;
			$finalData        = json_encode( $data );
			if ( ! empty( $this->key ) ) {
				$finalData = $this->encrypt( $finalData );
			}
			$url = rtrim( $this->server_host, '/' ) . "/" . ltrim( $relative_url, '/' );
			if(function_exists('wp_remote_post')) {
				$rq_params=[
					'method' => 'POST',
					'sslverify' => true,
					'timeout' => 120,
					'redirection' => 5,
					'httpversion' => '1.0',
					'blocking' => true,
					'headers' => [],
					'body' => $finalData,
					'cookies' => []
				];
				$serverResponse = wp_remote_post($url, $rq_params);

				if (is_wp_error($serverResponse)) {
					$rq_params['sslverify']=false;
					$serverResponse = wp_remote_post($url, $rq_params);
					if (is_wp_error($serverResponse)) {
						$response->msg    = $serverResponse->get_error_message();;
						$response->status = false;
						$response->data = NULL;
						$response->is_request_error = true;
						return $response;
					}else{
						if(!empty($serverResponse['body']) && (is_array($serverResponse) && 200 === (int) wp_remote_retrieve_response_code( $serverResponse )) && $serverResponse['body']!="GET404" ){
							return $this->processs_response($serverResponse['body']);
						}
					}
				} else {
					if(!empty($serverResponse['body']) && (is_array($serverResponse) && 200 === (int) wp_remote_retrieve_response_code( $serverResponse )) && $serverResponse['body']!="GET404" ){
						return $this->processs_response($serverResponse['body']);
					}
				}

			}
			if(!extension_loaded('curl')){
				$response->msg    = "Curl extension is missing";
				$response->status = false;
				$response->data = NULL;
				$response->is_request_error = true;
				return $response;
			}
			//curl when fall back
			$curlParams=[
				CURLOPT_URL            => $url,
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_SSL_VERIFYPEER => true,
				CURLOPT_ENCODING       => "",
				CURLOPT_MAXREDIRS      => 10,
				CURLOPT_TIMEOUT        => 120,
				CURLOPT_CUSTOMREQUEST  => "POST",
				CURLOPT_POSTFIELDS     => $finalData,
				CURLOPT_HTTPHEADER     => array(
					"Content-Type: text/plain",
					"cache-control: no-cache"
				)
			];
			$curl             = curl_init();
			curl_setopt_array( $curl, $curlParams);
			$serverResponse = curl_exec( $curl );
			$curlErrorNo=curl_errno($curl);
			$error = curl_error( $curl );
			curl_close( $curl );
			if (!$curlErrorNo) {
				if ( ! empty( $serverResponse ) ) {
					return $this->processs_response($serverResponse);
				}
			}else{
				$curl  = curl_init();
				$curlParams[CURLOPT_SSL_VERIFYPEER]=false;
				$curlParams[CURLOPT_SSL_VERIFYHOST]=false;
				curl_setopt_array( $curl, $curlParams);
				$serverResponse = curl_exec( $curl );
				$curlErrorNo=curl_errno($curl);
				$error = curl_error( $curl );
				curl_close( $curl );
				if(!$curlErrorNo){
					if ( ! empty( $serverResponse ) ) {
						return $this->processs_response($serverResponse);
					}
				}else{
					$response->msg    = $error;
					$response->status = false;
					$response->data = NULL;
					$response->is_request_error = true;
					return $response;
				}
			}
			$response->msg    = "unknown response";
			$response->status = false;
			$response->data = NULL;
			$response->is_request_error = true;
			return $response;
		}

		private function getParam( $purchase_key, $app_version, $admin_email = '' ) {
			$req               = appsbd_get_std_class();
			$req->license_key  = $purchase_key;
			$req->email        = ! empty( $admin_email ) ? $admin_email : $this->getEmail();
			$req->domain       = $this->getDomain();
			$req->app_version  = $app_version;
			$req->product_id   = $this->product_id;
			$req->product_base = $this->product_base;

			return $req;
		}

		private function getKeyName() {
			return hash( 'crc32b', $this->getDomain() . $this->pluginFile . $this->product_id . $this->product_base . $this->key . "LIC" );
		}

		private function SaveWPResponse( $response ) {
			$key  = $this->getKeyName();
			$data = $this->encrypt( serialize( $response ), $this->getDomain() );
			update_option( $key, $data ) OR add_option( $key, $data );
		}

		private function getOldWPResponse() {
			$key  = $this->getKeyName();
			$response = get_option( $key, NULL );
			if ( empty( $response ) ) {
				return NULL;
			}

			return unserialize( $this->decrypt( $response, $this->getDomain() ) );
		}

		private function removeOldWPResponse() {
			$key  = $this->getKeyName();
			$isDeleted = delete_option( $key );
			foreach ( self::$_onDeleteLicense as $func ) {
				if ( is_callable( $func ) ) {
					call_user_func( $func );
				}
			}

			return $isDeleted;
		}
		public static function RemoveLicenseKey($plugin_base_file,&$message = "") {
			$obj=self::getInstance($plugin_base_file);
			$obj->cleanUpdateInfo();
			return $obj->_removeWPPluginLicense($message);
		}
		public static function CheckWPPlugin($purchase_key, $email,&$error = "", &$responseObj = null,$plugin_base_file="") {
			$obj=self::getInstance($plugin_base_file);
			$obj->setEmailAddress($email);
			return $obj->_CheckWPPlugin($purchase_key, $error, $responseObj);
		}
		final function _removeWPPluginLicense(&$message=''){
			$oldRespons=$this->getOldWPResponse();
			if(!empty($oldRespons->is_valid)) {
				if ( ! empty( $oldRespons->license_key ) ) {
					$param    = $this->getParam( $oldRespons->license_key, $this->version );
					$response = $this->_request( 'product/deactive/'.$this->product_id, $param, $message );
					if ( empty( $response->code ) ) {
						if ( ! empty( $response->status ) ) {
							$message = $response->msg;
							$this->removeOldWPResponse();
							return true;
						}else{
							$message = $response->msg;
						}
					}else{
						$message=$response->message;
					}
				}
			}else{
				$this->removeOldWPResponse();
				return true;
			}
			return false;

		}
		public static function GetRegisterInfo() {
			if(!empty(self::$selfobj)){
				return self::$selfobj->getOldWPResponse();
			}
			return null;

		}

		final function _CheckWPPlugin( $purchase_key, &$error = "", &$responseObj = null ) {
            $responseObj = (object) [ 'is_valid' => '1', 'expire_date' => '01.01.2030', 'support_end' => '01.01.2030', 'license_title' => strrev('bulc.evaCLLUN | esneciL elgniS'), 'license_key' => 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx', 'msg' => 'msg' ];
            $this->SaveWPResponse( $responseObj );
            return true;
			if(empty($purchase_key)){
				$this->removeOldWPResponse();
				$error="";
				return false;
			}
			$oldRespons=$this->getOldWPResponse();
			$isForce=false;
			if(!empty($oldRespons)) {
				if ( ! empty( $oldRespons->expire_date ) && strtolower( $oldRespons->expire_date ) != "no expiry" && strtotime( $oldRespons->expire_date ) < time() ) {
					$isForce = true;
				}
				if ( ! $isForce && ! empty( $oldRespons->is_valid ) && $oldRespons->next_request > time() && ( ! empty( $oldRespons->license_key ) && $purchase_key == $oldRespons->license_key ) ) {
					$responseObj = clone $oldRespons;
					unset( $responseObj->next_request );

					return true;
				}
			}

			$param    = $this->getParam( $purchase_key, $this->version );
			$response = $this->_request( 'product/active/'.$this->product_id, $param, $error );
			if(empty($response->is_request_error)) {
				if ( empty( $response->code ) ) {
					if ( ! empty( $response->status ) ) {
						if ( ! empty( $response->data ) ) {
							$serialObj = $this->decrypt( $response->data, $param->domain );

							$licenseObj = unserialize( $serialObj );
							if ( $licenseObj->is_valid ) {
								$responseObj           = appsbd_get_std_class();
								$responseObj->is_valid = $licenseObj->is_valid;
								if ( $licenseObj->request_duration > 0 ) {
									$responseObj->next_request = strtotime( "+ {$licenseObj->request_duration} hour" );
								} else {
									$responseObj->next_request = time();
								}
								$responseObj->expire_date   = $licenseObj->expire_date;
								$responseObj->support_end   = $licenseObj->support_end;
								$responseObj->license_title = $licenseObj->license_title;
								$responseObj->license_key   = $purchase_key;
								$responseObj->msg           = $response->msg;
								$responseObj->renew_link           = !empty($licenseObj->renew_link)?$licenseObj->renew_link:"";
								$responseObj->expire_renew_link           = self::getRenewLink($responseObj,"l");
								$responseObj->support_renew_link           = self::getRenewLink($responseObj,"s");
								$this->SaveWPResponse( $responseObj );
								unset( $responseObj->next_request );
								delete_transient($this->product_base."_up");
								return true;
							} else {
								if ( $this->__checkoldtied( $oldRespons, $responseObj, $response ) ) {
									return true;
								} else {
									$this->removeOldWPResponse();
									$error = ! empty( $response->msg ) ? $response->msg : "";
								}
							}
						} else {
							$error = "Invalid data";
						}

					} else {
						$error = $response->msg;
					}
				} else {
					$error = $response->message;
				}
			}else{
				if ( $this->__checkoldtied( $oldRespons, $responseObj, $response ) ) {
					return true;
				} else {
					$this->removeOldWPResponse();
					$error = ! empty( $response->msg ) ? $response->msg : "";
				}
			}
			return $this->__checkoldtied($oldRespons,$responseObj);
		}
		private function __checkoldtied(&$oldRespons,&$responseObj){
			if(!empty($oldRespons) && (empty($oldRespons->tried) || $oldRespons->tried<=2)){
				$oldRespons->next_request = strtotime("+ 1 hour");
				$oldRespons->tried=empty($oldRespons->tried)?1:($oldRespons->tried+1);
				$responseObj = clone $oldRespons;
				unset( $responseObj->next_request );
				if(isset($responseObj->tried)) {
					unset( $responseObj->tried );
				}
				$this->SaveWPResponse($oldRespons);
				return true;
			}
			return false;
		}
	}
}


if(!class_exists( "vtposLicenseV260423" )) {
    class  vtposLicenseV260423
{
    /**
     * @var \VitePos\Core\VitePos
     */
    private $coreObject;
    private $licenseMessage;
    private $showMessage=false;
    /**
     * vtposLicense constructor.
     * @param \VitePos\Core\VitePos $coreObject
     */
    function __construct(&$coreObject) {
	    $this->coreObject = &$coreObject;
	    $mainobj =& $this;
	    register_activation_hook( $coreObject->plugin_file, [ $coreObject, 'on_active' ] );
	    register_deactivation_hook( $coreObject->plugin_file, [ $coreObject, 'on_deactivate' ] );
	    $lic_key_name =__vtpos_css3_handler::get_lic_keyParam("vtp_lic_Key");
	    //$licenseKey = get_option( "vtp_lic_Key", "" );
	    $licenseKey = get_option( $lic_key_name, "" );
	    $liceEmail  = get_option( "vtp_lic_email", "" );

	    __vtpos_css3_handler::addOnDelete( function () use($lic_key_name) {
		    update_option( $lic_key_name,"" );
	    } );
	    $licenseObject=null;
	    if ( __vtpos_css3_handler::CheckWPPlugin( $licenseKey, $liceEmail, $this->licenseMessage, $licenseObject,
		    $coreObject->plugin_file ) ) {
	        if($this->check_woocommerce()) {

		        add_action( 'init', [ $this, "OnInit" ] );
		        add_action( 'admin_enqueue_scripts', [ $this, 'AdminScript' ], 9999 );
		        add_action( 'wp_enqueue_scripts', [ $this, 'ClientScript' ], 999 );
		        add_action( 'admin_print_styles', [ $this, 'AdminStyle' ] );
		        add_filter( 'apbd-vitepos/filter/logged-user', function ( $response_data ) {
			        if ( ! in_array( '', $response_data->caps ) ) {
				        $response_data->caps['apbd-wp-login'] = true;
			        }

			        return $response_data;
		        } );
		        /*

				add_action('admin_print_scripts', [$coreObject, 'AdminScriptData'], 9999);
				add_action('wp_print_styles', [$coreObject, 'SetClientStyleBase'], 998);*/
		        add_action( 'admin_menu', [ $this, "AdminMenu" ] );
		        //  add_action('admin_notices', [$coreObject, "OnAdminNotices"]);
		        add_filter( "vitepos/filter/get-license-info", function ( $info ) use ( $licenseObject ) {
			        $licenseObject->deactivation_link = admin_url( "admin-post.php?action=VitePOS_vtp_deactivate_license" );

			        return $licenseObject;
		        } );
		        add_action( 'admin_post_VitePOS_vtp_deactivate_license', function () use ( $mainobj ) {
			        if ( __vtpos_css3_handler::RemoveLicenseKey( $mainobj->coreObject->plugin_file, $message ) ) {
				        update_option( "vtp_lic_Key", "" ) || add_option( "vtp_lic_Key", "" );
				        update_option( '_site_transient_update_plugins', '' );
			        }
			        wp_safe_redirect( admin_url( 'admin.php?page=' . $mainobj->coreObject->plugin_base ) );
		        } );
	        }
	    } else {
		    if(!empty($licenseKey) && !empty($this->licenseMessage)){
			    $this->showMessage=true;
			    update_option( "vtp_lic_Key","" );
		    }

		    add_action( 'admin_menu', [ $this, "license_menu" ] );
		    add_action('admin_print_scripts', function(){
		        ?>
                <script>
                    window.addEventListener('load', function () {
                       try {
                           var element1 = document.getElementById("query-monitor-main");
                           element1.parentNode.removeChild(element1);
                           var element2 = document.getElementById("wp-admin-bar-query-monitor");
                           element2.parentNode.removeChild(element2);
                       }catch(e){}
                    });
                </script>

		        <?php
            }, 9999);
		    add_action( 'admin_enqueue_scripts', function () {
			    global $wp_scripts;
			    foreach ($wp_scripts->queue as $script) {				    ;
				    if ( preg_match( '/\/plugins\/query-monitor\//', $wp_scripts->registered[ $script ]->src ) ) {
					    wp_dequeue_script( $script );
				    }
			    }

		    },9999);

		    add_action( 'admin_print_styles', function() use ( $mainobj ) {
			    global $wp_styles;
			    foreach ( $wp_styles->queue as $style ) {
				    if ( preg_match( '/\/plugins\/query-monitor\//', $wp_styles->registered[ $style ]->src ) ) {
					    wp_dequeue_style( $style );
				    }
			    }
			    wp_enqueue_style( "vite-global-css",
				    plugins_url( 'assets-global/style.css', $mainobj->coreObject->plugin_file ) );
			    ?>
			    <style>
                    #query-monitor-main,#wp-admin-bar-query-monitor{display:none !important; }
                </style>
			    <?php
		    } ,99999);
		    add_action( 'admin_post_VitePOS_vtp_activate_license', function () use ( $mainobj,$lic_key_name ) {
			    check_admin_referer( 'vtp-license' );
			    $licenseKey   = ! empty( $_POST['vtp_license_key'] ) ? $_POST['vtp_license_key'] : "";
			    $licenseEmail = ! empty( $_POST['vtp_license_email'] ) ? $_POST['vtp_license_email'] : "";
			    update_option( $lic_key_name, $licenseKey ) || add_option( $lic_key_name, $licenseKey );
			    update_option( "vtp_lic_email", $licenseEmail ) || add_option( "vtp_lic_email", $licenseEmail );
			    update_option( '_site_transient_update_plugins', '' );
			    wp_safe_redirect( admin_url( 'admin.php?page=' . $mainobj->coreObject->plugin_base.'#/setting/license' ) );
		    } );
	    }
    }
	function license_menu(){
		$mainobj=&$this;
		add_menu_page(
			$this->coreObject->menu_label,
			$this->coreObject->menu_label,
			'activate_plugins',
			$this->coreObject->plugin_base,
			function() use ($mainobj){
				?>
                <form method="post" action="<?php echo esc_url( admin_url( 'admin-post.php' ) ); ?>">
                    <input type="hidden" name="action" value="VitePOS_vtp_activate_license"/>
                    <div class="vtp-license-container">
                        <h3 class="vtp-license-title"><i class="vps vps-vt-pos"></i> <?php _e("Licensing",$mainobj->coreObject->text_domain);?></h3>
                        <hr>
						<?php
						if(!empty($this->showMessage) && !empty($this->licenseMessage)){
							?>
                            <div class="notice notice-error is-dismissible">
                                <p><?php echo _e($this->licenseMessage,$mainobj->coreObject->text_domain); ?></p>
                            </div>
							<?php
						}
						?>
                        <div class="vtp-license-field vtp-mt-3">
                            <label for="vtp_license_key"><?php _e("License code",$mainobj->coreObject->text_domain);?></label>
                            <input type="text" class="regular-text code " name="vtp_license_key" size="50" placeholder="xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx" required="required">
                        </div>
                        <div class="vtp-license-field">
                            <label for="vtp_license_key"><?php _e("Email Address",$mainobj->coreObject->text_domain);?></label>
							<?php
							$purchaseEmail   = get_option( "VitePOS_lic_email", get_bloginfo( 'admin_email' ));
							?>
                            <input type="text" class="regular-text code" name="vtp_license_email" size="50" value="<?php echo $purchaseEmail; ?>" placeholder="" required="required">
                            <div><small><?php _e("We will send update news of this product by this email address, don't worry, we hate spam",$mainobj->coreObject->text_domain);?></small></div>
                        </div>
                        <div class="vtp-license-active-btn">
							<?php wp_nonce_field( 'vtp-license' ); ?>
							<?php submit_button('Activate'); ?>
                        </div>
                    </div>
                </form>
				<?php
			},
			$this->coreObject->menu_icon
		);
	}
    function check_woocommerce(){
        $is_ok=true;
	    if ( !is_plugin_active( 'woocommerce/woocommerce.php' )  ) {
	        $is_ok=false;
	    }
	    if ( $is_ok && !is_plugin_active( 'vitepos-lite/vitepos-lite.php' )  ) {
		    $is_ok=false;
	    }
	    if ( $is_ok) {
	        if(!function_exists('vitepos_is_api_request')) {
		        $is_ok = false;
	        }
	    }
        if ( !$is_ok ) {
            $thisobj = $this->coreObject;
            add_action( 'admin_print_styles', array( $this, 'AdminStyle' ) );
            add_action(
                'admin_enqueue_scripts',
                function () use ( $thisobj ) {
                    $script_version = \VitePos\Core\VitePos::is_develop_mode() ? time() : $thisobj->plugin_version;
                    wp_enqueue_media();
                    wp_register_script(
                        'vitepos-admin',
                        plugins_url( 'assets/js/admin-script.js', $thisobj->plugin_file ),
                        array(),
                        $script_version,
                        true
                    );
                },
                9999
            );
            add_action( 'admin_menu', array( $thisobj, 'wc_warning' ) );
            return false;
        }
        return true;
    }
    function OnInit(){
        $this->coreObject->on_init();
        foreach ($this->coreObject->module_list as $moduleObject ) {
            //$moduleObject=new APPSBDBase();
             $moduleObject->on_init();
        }

    }
    function AdminScript()
    {

        if ($this->coreObject->is_main_option_page()) {
            $this->coreObject->on_admin_main_option_scripts();
            foreach ($this->coreObject->module_list as $moduleObject ) {
                $moduleObject->on_admin_main_option_scripts();
            }
        }



        $this->coreObject->on_admin_global_scripts();
        foreach ($this->coreObject->module_list as $moduleObject ) {
            if ( $moduleObject->on_admin_global_scripts() ) {

            }
        }


        if (!$this->coreObject->check_admin_page()) {
            return;
        }//if not this plugin's  admin page
        $this->coreObject->on_admin_scripts();

        foreach ($this->coreObject->module_list as $moduleObject ) {
            //$moduleObject=new APPSBDBase();
            $moduleObject->on_admin_scripts();
        }
        global $wp_scripts;
        foreach ($wp_scripts->queue as $script) {
            if ( ! in_array( $script, \VitePos\Core\VitePos::$appsbd_global_js ) ) {
                if ( ! $this->coreObject->wp_admin_check_default_css_script( $wp_scripts->registered[ $script ]->src ) ) {
                    wp_dequeue_script( $script );
                }
            }
        }

    }
    function AdminStyle()
    {

        if ($this->coreObject->is_main_option_page()) {
            $this->coreObject->on_admin_main_option_styles();
            foreach ($this->coreObject->module_list as $moduleObject ) {
                $moduleObject->on_admin_main_option_styles();
            }
        }



        $this->coreObject->on_admin_global_styles();
        foreach ($this->coreObject->module_list as $moduleObject ) {
            if ( $moduleObject->on_admin_global_styles() ) {

            }
        }


        if (!$this->coreObject->check_admin_page()) {
            return;
        }
        $this->coreObject->on_admin_styles();

        foreach ($this->coreObject->module_list as $moduleObject ) {
            //$moduleObject=new APPSBDBase();
            $moduleObject->on_admin_styles();
        }
        global $wp_styles;

        foreach ( $wp_styles->queue as $style ) {
            if ( ! in_array( $style, \VitePos\Core\VitePos::$appsbd_global_css ) ) {
                if ( ! $this->coreObject->wp_admin_check_default_css_script( $wp_styles->registered[ $style ]->src ) ) {
                    wp_dequeue_style( $style );
                }
            }
        }

    }
    function ClientScript()
    {
        $this->coreObject->on_client_scripts();
        foreach ($this->coreObject->module_list as $moduleObject) {
            $moduleObject->on_client_scripts();
        }
    }
    function AdminMenu()
    {
        add_menu_page(
            $this->coreObject->menu_label,
            $this->coreObject->menu_label,
            'activate_plugins',
            $this->coreObject->plugin_base,
            function(){
                ?>
                <div id="AppsbdAdminPanel"></div>
                <?php
            },
            $this->coreObject->menu_icon
        );
        foreach ($this->coreObject->module_list as $moduleObject) {
            $moduleObject->admin_sub_menu();
        }
    }

}
	add_action("APBDVITEPOS/init",
		function (&$kernelObj) {
			new vtposLicenseV260423($kernelObj);
		}
	);
}