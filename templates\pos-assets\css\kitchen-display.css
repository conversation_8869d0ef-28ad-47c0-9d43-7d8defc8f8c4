/* Kitchen Display Styles */
.kitchen-display-body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
    direction: rtl;
}

.kitchen-header {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-bottom: 3px solid #007bff;
}

.kitchen-header h2 {
    color: #2c3e50;
    font-weight: 700;
}

.status-indicator .badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

.current-time {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Category Tabs */
.kitchen-categories {
    background: rgba(255, 255, 255, 0.9);
    padding: 1rem 0;
}

.category-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.category-tab {
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 1rem 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #6c757d;
    min-width: 150px;
    justify-content: center;
}

.category-tab:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.category-tab.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

.category-tab .badge {
    margin-right: 0.5rem;
}

/* Orders Grid */
.kitchen-content {
    padding: 2rem 1rem;
    flex: 1;
}

.orders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    max-height: calc(100vh - 300px);
    overflow-y: auto;
}

/* Order Card */
.order-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 5px solid #007bff;
    position: relative;
}

.order-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.order-card.urgent {
    border-left-color: #dc3545;
    animation: pulse 2s infinite;
}

.order-card.preparing {
    border-left-color: #ffc107;
}

.order-card.ready {
    border-left-color: #28a745;
}

@keyframes pulse {
    0% { box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1); }
    50% { box-shadow: 0 5px 20px rgba(220, 53, 69, 0.3); }
    100% { box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1); }
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.order-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #007bff;
}

.order-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.9rem;
}

.time-elapsed {
    font-weight: 600;
}

.time-urgent {
    color: #dc3545;
    font-weight: 700;
}

/* Order Items */
.order-items {
    margin-bottom: 1rem;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.order-item:last-child {
    border-bottom: none;
}

.item-name {
    font-weight: 600;
    color: #2c3e50;
}

.item-quantity {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 600;
}

.item-notes {
    font-size: 0.8rem;
    color: #6c757d;
    font-style: italic;
    margin-top: 0.25rem;
}

/* Order Actions */
.order-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-btn {
    flex: 1;
    min-width: 100px;
    padding: 0.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.action-btn:hover {
    transform: translateY(-1px);
}

.btn-start {
    background: #ffc107;
    color: #212529;
}

.btn-start:hover {
    background: #e0a800;
}

.btn-complete {
    background: #28a745;
    color: white;
}

.btn-complete:hover {
    background: #218838;
}

.btn-deny {
    background: #dc3545;
    color: white;
}

.btn-deny:hover {
    background: #c82333;
}

.btn-note {
    background: #6c757d;
    color: white;
}

.btn-note:hover {
    background: #5a6268;
}

/* Order Info */
.order-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 0.5rem;
    border-top: 1px solid #e9ecef;
    font-size: 0.9rem;
    color: #6c757d;
}

.table-info {
    font-weight: 600;
}

.priority-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
}

.priority-high {
    background: #dc3545;
    color: white;
}

.priority-medium {
    background: #ffc107;
    color: #212529;
}

.priority-low {
    background: #28a745;
    color: white;
}

/* Loading States */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .orders-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .category-tabs {
        flex-direction: column;
        align-items: center;
    }
    
    .category-tab {
        width: 100%;
        max-width: 300px;
    }
    
    .order-actions {
        flex-direction: column;
    }
    
    .action-btn {
        min-width: auto;
    }
}

/* Print Styles */
@media print {
    .kitchen-header,
    .kitchen-categories,
    .order-actions {
        display: none !important;
    }
    
    .orders-grid {
        display: block;
    }
    
    .order-card {
        break-inside: avoid;
        margin-bottom: 1rem;
    }
}
