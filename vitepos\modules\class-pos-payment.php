<?php
/**
 * Its for EPOS settings module
 *
 * @package VitePos\Modules
 */

namespace VitePos\Modules;

use Appsbd\V1\Core\BaseModule;
use Appsbd\V1\libs\Ajax_Confirm_Response;
use Appsbd\V1\libs\AppInput;

/**
 * Class APBD_EPOS_Settings
 */
class POS_Payment extends BaseModule {
	/**
	 * The initialize is generated by appsbd
	 */
	public function initialize() {
		add_filter('vitepos/filter/payment-gw',array($this,'check_gateways_settings'),5,2);
		add_filter( 'vitepos/filter/header-links', array($this,'prefetch_script'),11);
		add_filter( 'vitepos/filter/footer-scripts', array($this,'footer_script'),9);
			}

	/**
	 * The on init is generated by appsbd
	 */
	public function on_init() {
		parent::on_init();
		if ( current_user_can( 'activate_plugins' ) ) {
			$this->add_ajax_action( 'payment-status', array( $this, 'update_payment_status' ) );
			$this->add_ajax_action( 'payment-settings', array( $this, 'update_payment_settings' ) );
		}
	}

	/**
	 * The prefetch script is generated by appsbd
	 *
	 * @param mixed $links Its links param.
	 *
	 * @return mixed
	 */
	public function prefetch_script($links){
				if(self::is_enable_gw('stripe')) {
			$links[] = array(
				'href' => 'https://js.stripe.com/v3/',
				'rel'  => 'preload',
				'as'   => 'script',
			);
		}
		return $links;
	}

	/**
	 * The footer script is generated by appsbd
	 *
	 * @param mixed $scripts Its scripts param.
	 *
	 * @return mixed
	 */
	public function footer_script($scripts){
		if(self::is_enable_gw('stripe')) {
			$scripts[] = array(
				'src' => 'https://js.stripe.com/v3/'
			);
		}
		return $scripts;
	}

	public static function get_gw_new_obj(){
		$obj=new \stdClass();
		$obj->is_enable='N';
		$obj->can_split='Y';
		$obj->settings=new \stdClass();
		return $obj;
	}

	/**
	 * The is enable gw is generated by appsbd
	 *
	 * @param mixed $gw Its gateway name like stripe;
	 *
	 * @return bool
	 */
	public static function is_enable_gw($gw){
		$gws=self::get_clients_payment_getways();
		return !empty($gws[$gw]->is_enable) && $gws[$gw]->is_enable=='Y';
	}
	public static function get_payment_getways(){
		$gws=self::get_module_option('vtp_gw',[]);
		if(empty($gws)){
			$gws=[];
			$gws['swipe']=self::get_gw_new_obj();
			$gws['swipe']->is_enable='Y';
			$gws['others']=self::get_gw_new_obj();
			$gws['others']->is_enable='Y';
			$gws['stripe']=self::get_gw_new_obj();
			$gws['stripe_terminal']=self::get_gw_new_obj();
			$gws['authorize']=self::get_gw_new_obj();
			self::get_module_instance()->add_option('vtp_gw',$gws);
		}
		return $gws;
	}
	public static function get_payment_gw_settings($gw) {
		$gws = self::get_module_option( 'vtp_gw', [] );
		return ! empty( $gws[ $gw ]) ? $gws[ $gw ]:null;
	}
	public function check_gateways_settings($gw,$gw_name) {
		if ( $gw_name == 'stripe' ) {
			if ( ( empty( $gw['settings']->pub_key ) || empty($gw['settings']->secret_key) ) ) {
				$gw['settings']->is_enable = 'N';
			}
			if ( isset( $gw['settings']->secret_key ) ) {
				unset( $gw['settings']->secret_key );
			}
		}
		if ( empty($gw['can_split']) ) {
			$gw['can_split'] = 'Y';
		}
		return $gw;
	}
	/**
	 * The get clients payment getways is generated by appsbd
	 *
	 * @return mixed
	 */
	public static function get_clients_payment_getways(){
		$gws=self::get_module_option('vtp_gw',[]);
		/**
		 * Its for check is there any change before process
		 *
		 * @since 1.0
		 */
		$res_gws=[];
		foreach ( $gws as $gw_name=>$gw ) {
			$gw=(array)$gw;
			$gw['settings'] = (array)$gw['settings'];   			$gw['settings'] = (object) $gw['settings']; 			$res_gws[$gw_name]=(object)apply_filters('vitepos/filter/payment-gw',$gw,$gw_name);
		}
		return $res_gws;
	}
	public function update_payment_status() {
		$gw_name       = AppInput::post_value( 'gw' );
		$gw_status     = AppInput::post_value( 'status', '' );
		$main_response = new Ajax_Confirm_Response();
		if ( empty( $gw_name )  || empty( $gw_status ) ) {
			$this->add_error( "Invalid param" );
			$main_response->display_with_response( false );
		}
		$gws = self::get_payment_getways();
		if ( isset( $gws[ $gw_name ] ) ) {
			$gws[ $gw_name ]->is_enable = $gw_status;
		}else{
			$gws[$gw_name]=self::get_gw_new_obj();
			$gws[ $gw_name ]->is_enable = $gw_status;
		}
		if ( $this->add_option( 'vtp_gw', $gws ) ) {
			$this->add_info( "Successfully updated" );
			$main_response->display_with_response( true );
		}
		$this->add_error( "Update failed" );
		$main_response->display_with_response( false);
	}
	public function update_payment_settings() {
		$gw_name       = AppInput::post_value( 'gw' );
		$gw_settings     = AppInput::post_value( 'settings', null );
		$main_response = new Ajax_Confirm_Response();
		if ( empty( $gw_name )  || empty( $gw_settings ) ) {
			$this->add_error( "Invalid param" );
			$main_response->display_with_response( false );
		}
		$gws = self::get_payment_getways();
		if ( isset( $gws[ $gw_name ] ) ) {
			$gws[ $gw_name ]->settings = (object)$gw_settings;
		}else{
			$gws[$gw_name]=self::get_gw_new_obj();
			$gws[ $gw_name ]->settings = (object)$gw_settings;
		}
		if ( $this->add_option( 'vtp_gw', $gws ) ) {
			$this->add_info( "Successfully updated" );
			$main_response->display_with_response( true );
		}
		$this->add_error( "Update failed" );
		$main_response->display_with_response( false );
	}
	public function get_admin_options() {
		$response = new Ajax_Confirm_Response();
		$res=new \stdClass();
		$res->payments=self::get_payment_getways();
		$response->display_with_response( true, $res);
	}

}
