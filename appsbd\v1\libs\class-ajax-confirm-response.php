<?php
/**
 * Its pos appsbd-ajax-confirm-response model
 *
 * @since: 21/09/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package Appsbd\V1\libs
 */

namespace Appsbd\V1\libs;

if ( ! class_exists( __NAMESPACE__ . '\Ajax_Confirm_Response' ) ) {
	/**
	 * Class appsbd_ajax_confirm_response
	 *
	 * @package Appsbd\V1\libs
	 */
	class Ajax_Confirm_Response {
		/**
		 * Its property status
		 *
		 * @var bool
		 */
		public $status = false;
		/**
		 * Its property msg
		 *
		 * @var string
		 */
		public $msg = '';
		/**
		 * Its property data
		 *
		 * @var null
		 */
		public $data = null;
		/**
		 * Its property icon
		 *
		 * @var string
		 */
		public $icon = '';
		/**
		 * Its property is_sticky
		 *
		 * @var bool
		 */
		public $is_sticky = false;
		/**
		 * Its property title
		 *
		 * @var null
		 */
		public $title = null;

		/**
		 * The set response is generated by appsbd
		 *
		 * @param bool   $status Its status parameter.
		 * @param null   $data Its data parameter.
		 * @param string $icon Its icon parameter.
		 * @param null   $title Its title parameter.
		 * @param false  $is_sticky Its sticky parameter.
		 */
		public function set_response( $status, $data = null, $icon = '', $title = null, $is_sticky = false ) {
			if ( empty( $icon ) ) {
				$icon = $status ? ' fa fa-check-circle-o ' : ' fa fa-times-circle-o ';
			}
			$this->status    = $status;
			$this->msg       = appsbd_get_msg_api();
			$this->data      = $data;
			$this->icon      = $icon;
			$this->is_sticky = $is_sticky;
			$this->title     = $title;

		}

		/**
		 * The display with response is generated by appsbd
		 *
		 * @param mixed $status Its status param.
		 * @param null  $data Its data param.
		 */
		public function display_with_response( $status, $data = null ) {
			$this->set_response( $status, $data );
			$this->display();
		}

		/**
		 * The display is generated by appsbd
		 */
		public function display() {
			wp_send_json( $this );
		}
	}
}
