<?php
/**
 * Pos Vendor Database Model
 *
 * @package Vitepos\Models\Database
 */

namespace Vitepos\Models\Database;

use VitePos\Core\ViteposModel;

/**
 * Class Mapbd_pos_vendor
 *
 * @properties id,name,email,contact_no,vendor_note,status,added_by
 */
class Mapbd_Pos_Addon_Rule_Group extends ViteposModel {
	/**
	 * Its property id
	 *
	 * @var int
	 */
	public $id;
	/**
	 * Its property status
	 *
	 * @var bool
	 */
	public $status;
	/**
	 * Its property addon_id
	 *
	 * @var int
	 */
	public $addon_id;


	/**
	 * Mapbd_pos_vendor constructor.
	 */
	public function __construct() {
		parent::__construct();
		$this->set_validation();
		$this->table_name     = 'apbd_pos_addon_rule_group';
		$this->primary_key    = 'id';
		$this->unique_key     = array();
		$this->multi_key      = array();
		$this->auto_inc_field = array( 'id' );
		$this->app_base_name  = 'apbd-elite-pos';

	}


	/**
	 * The set validation is generated by appsbd
	 */
	public function set_validation() {
		$this->validations = array(
			'id'       => array(
				'Text' => 'Id',
				'Rule' => 'max_length[11]|integer',
			),
			'status'   => array(
				'Text' => 'Status',
				'Rule' => 'max_length[1]',
			),
			'addon_id' => array(
				'Text' => 'Added By',
				'Rule' => 'max_length[11]|integer',
			),

		);
	}

	/**
	 * The get property raw options is generated by appsbd
	 *
	 * @param \Appsbd\V1\Core\any $property Its string.
	 * @param false               $is_with_select Its bool.
	 *
	 * @return array|string[]
	 */
	public function get_property_raw_options( $property, $is_with_select = false ) {
		$return_obj = array();
		switch ( $property ) {
			case 'status':
				$return_obj = array(
					'A' => 'Active',
					'I' => 'Inactive',
				);
				break;
			default:
		}
		if ( $is_with_select ) {
			return array_merge( array( '' => 'Select' ), $return_obj );
		}
		return $return_obj;

	}

	/**
	 * The create db table is generated by appsbd
	 */
	public static function create_db_table() {
		$this_obj = new static();
		$table    = $this_obj->db->prefix . $this_obj->table_name;
		if ( $this_obj->db->get_var( "show tables like '{$table}'" ) != $table ) {
			$sql = "CREATE TABLE `{$table}` (
					  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
					  `status` char(1) NOT NULL DEFAULT 'A' COMMENT 'bool(A=Active,I=Inactive)',
					  `addon_id` int(11) unsigned NOT NULL,
					  PRIMARY KEY (`id`)
					) ";
			require_once ABSPATH . 'wp-admin/includes/upgrade.php';
			dbDelta( $sql );
		}
	}

	/**
	 * The delete by id is generated by appsbd
	 *
	 * @param any $id Its vendor id param.
	 *
	 * @return bool
	 */
	public static function delete_by_id( $id ) {
		if ( parent::delete_by_key_value( 'id', $id ) ) {
			Mapbd_Pos_Addon_Rule::delete_by_group_id( $id );
			return true;
		}
		return false;
	}
	/**
	 * The delete by id is generated by appsbd
	 *
	 * @param any $addon_id Its vendor id param.
	 *
	 * @return bool
	 */
	public static function delete_by_addon_id( $addon_id ) {
		$rules      = self::find_all_by( 'addon_id', $addon_id );
		$is_deleted = true;
		foreach ( $rules as $rule ) {
			if ( ! self::delete_by_id( $rule->id ) ) {
				$is_deleted = false;
			}
		}
		return $is_deleted;
	}

	/**
	 * The delete by id is generated by appsbd
	 *
	 * @param any $addon_id Its vendor id param.
	 *
	 * @return bool
	 */
	public static function get_all_rule_groups_by( $addon_id ) {
		$rule_groups = self::find_all_grid_data_by( 'addon_id', $addon_id );
		foreach ( $rule_groups as &$group ) {
			$group->rules = array();
			$group->rules = Mapbd_Pos_Addon_Rule::get_all_rules_by( $group->id );
		}
		return $rule_groups;
	}

}
