@media print {
  html, body {
    margin: 0;
  }
  .payment-note {
    display: none !important;
  }
  .order-barcode {
    display: unset !important;
  }
}
/*@media all {
  body {
    -webkit-print-color-adjust: exact !important;

  }*/
@page {
  margin: 0 5mm 0 1mm;
  padding: 0;
  display: flex;
  justify-content: center;
}
.invoice-POS {
  padding: 3mm;
  width: 97%;
  margin-left: -10px !important;
  background: #FFF;
  font-family: Arial, Vrinda, system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
.invoice-POS, .invoice-POS * {
  color: #000 !important;
}
.invoice-POS .quillWrapper {
  width: 100%;
}
.invoice-POS .ql-align-center {
  text-align: center;
}
.invoice-POS .ql-align-justify {
  text-align: justify;
}
.invoice-POS .ql-align-right {
  text-align: right;
}
.invoice-POS h1 {
  font-size: 14px;
}
.invoice-POS h2 {
  font-size: 13px;
}
.invoice-POS h3 {
  font-size: 12px;
  font-weight: 300;
  line-height: 2em;
}
.invoice-POS .custom-info {
  border-bottom: 1px solid #000;
  padding-bottom: 2px;
  padding-top: 2px;
}
.invoice-POS .custom-info .outlet-info h2 {
  margin: 0;
}
.invoice-POS .custom-info .customer-info * {
  font-size: var(--vt-pos-invoice-font-size, 10px);
}
.invoice-POS .custom-info .customer-info h2 {
  margin: 0;
}
.invoice-POS p {
  font-size: var(--vt-pos-invoice-font-size, 10px);
  line-height: calc(var(--vt-pos-invoice-font-size, 10px) + 4px);
  margin: 0;
}
.invoice-POS .invoice-header, .invoice-POS #mid, .invoice-POS #bot { /* Targets all id with 'col-' */
  border-bottom: 1px solid rgba(0, 0, 0, 0.52);
}
.invoice-POS .unit-price {
  white-space: nowrap;
}
.invoice-POS .item-dis-price {
  text-align: right;
  font-size: var(--vt-pos-invoice-font-size-depns, 8px);
  font-style: italic;
}
.invoice-POS .invoice-header .logo-pnl .invoice-logo {
  max-width: 100px;
  max-height: 60px;
  margin-bottom: 5px;
  overflow: hidden;
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.invoice-POS .invoice-header .logo-pnl .invoice-logo img {
  width: 100%;
}
.invoice-POS .invoice-header .invoice-custom-header * {
  margin-bottom: 0;
}
.invoice-POS .invoice-header .counter-info {
  font-size: var(--vt-pos-invoice-font-size, 10px);
  text-align: center;
}
.invoice-POS .invoice-header .order-info {
  font-size: var(--vt-pos-invoice-font-size, 10px);
  display: flex;
  justify-content: space-between;
  padding-top: 10px;
  flex-wrap: wrap;
}
.invoice-POS .invoice-header .order-info > div {
  white-space: nowrap;
}
.invoice-POS .invoice-header .ref-title {
  font-size: 12px;
}
.invoice-POS .info {
  display: block;
  margin-left: 0;
}
.invoice-POS .total-row {
  display: flex;
  justify-content: flex-end;
  font-weight: bold;
  font-size: var(--vt-pos-invoice-font-size, 10px);
}
.invoice-POS .total-row > span {
  margin-left: 15px;
}
.invoice-POS .total-row.nb {
  font-weight: normal !important;
}
.invoice-POS .grand-total {
  border-top: 1px solid rgba(0, 0, 0, 0.51);
}
.invoice-POS .refund-counter {
  border-top: 1px solid rgba(0, 0, 0, 0.51);
  border-bottom: none;
}
.invoice-POS .total-value {
  width: 25mm;
}
.invoice-POS table {
  width: 100%;
  border-collapse: collapse;
}
.invoice-POS .tabletitle, .invoice-POS .tabletitle tr, .invoice-POS .tabletitle td, .invoice-POS .tabletitle th {
  border-bottom: 1px solid #000;
  font-size: var(--vt-pos-invoice-font-size, 10px);
}
.invoice-POS .tabletitle .item-head-sl {
  padding-right: 5px;
  width: 20px;
}
.invoice-POS .service {
  border-bottom: 1px solid rgba(0, 0, 0, 0.51);
}
.invoice-POS .service td:last-child {
  width: 20mm;
}
.invoice-POS .service td.item-qty {
  width: 5mm;
}
.invoice-POS .service .item-sl {
  position: relative;
}
.invoice-POS .service .item-sl p {
  position: absolute;
  top: 2px;
}
.invoice-POS .itemtext {
  font-size: var(--vt-pos-invoice-font-size, 10px);
}
.invoice-POS .invoice-footer {
  font-size: var(--vt-pos-invoice-font-size-depns, 8px);
  margin-top: 10px;
  padding-bottom: 10px;
  text-align: center;
}
.invoice-POS .invoice-footer .invoice-custom-footer * {
  margin-bottom: 0;
  font-size: calc(var(--vt-pos-invoice-font-size, 10px) - 1px);
}
.invoice-POS .invoice-footer .invoice-custom-footer p {
  font-size: calc(var(--vt-pos-invoice-font-size, 10px) - 1px);
}
.invoice-POS .invoice-footer .invoice-custom-footer.apbd-branding {
  display: none;
  margin-top: 10px;
  font-style: italic;
  font-size: 11px;
  font-weight: bold;
}
.invoice-POS .invoice-footer .invoice-custom-footer.apbd-branding.show {
  display: block !important;
}
.invoice-POS .invoice-footer .invoice-custom-footer.apbd-line {
  display: none;
}
.invoice-POS .invoice-footer .invoice-custom-footer.apbd-line.show {
  display: block !important;
}
.invoice-POS .text-end {
  text-align: right;
}
.invoice-POS .text-center {
  text-align: center;
}
.invoice-POS .text-start {
  text-align: left;
}
.invoice-POS .payment-type-amount {
  white-space: nowrap;
  display: block;
}
.invoice-POS .order-barcode {
  display: none;
}
.invoice-POS .order-barcode .code-position {
  display: flex;
  justify-content: center;
  align-items: center;
}
.invoice-POS .order-barcode .code-position.bottom {
  margin-top: 10px;
}
.invoice-POS .refund-panel {
  font-size: calc(var(--vt-pos-invoice-font-size, 10px) - 1px);
}
.invoice-POS .refund-panel .refund-header {
  border-bottom: 1px solid;
  display: flex;
  justify-content: center;
  align-items: center;
}
.invoice-POS .refund-panel .refund-header > div {
  font-weight: bold;
}

/*# sourceMappingURL=print.css.map */
