<?php
/**
 * Its used for dependacy check
 *
 * @since: 1.4
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.4
 * @package VitePos\Libs
 */

namespace VitePos\Libs;

require_once dirname( __FILE__ ) . '/../../appsbd/v1/libs/class-appinput.php';
require_once dirname( __FILE__ ) . '/../../appsbd/v1/libs/class-wp-dependency.php';

use \Appsbd\V1\libs\WP_Dependency;

if ( ! class_exists( __NAMESPACE__ . '\Vitepos_Dependency' ) ) {
	/**
	 * Class Vitepos_Dependency
	 *
	 * @package VitePos\Libs
	 */
	class Vitepos_Dependency extends WP_Dependency {

		/**
		 * The set details is generated by appsbd
		 *
		 * @return mixed|void
		 */
		public function set_details() {
			$this->plugin_file            = 'vitepos/vitepos.php';
			$this->lite_plugin_file       = 'vitepos-lite/vitepos-lite.php';
			$this->lite_version_file_link = 'https://downloads.wordpress.org/plugin/vitepos-lite.latest-stable.zip';
			$this->lite_min_version       = '2.0.3';
			$this->lite_slug              = 'vitepos-lite';
			$this->lite_title             = 'Vitepos Lite';
		}
	}
}
