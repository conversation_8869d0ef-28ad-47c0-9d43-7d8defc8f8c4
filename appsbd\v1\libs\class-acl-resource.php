<?php
/**
 * Its used for acl resources
 *
 * @since: 21/09/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package Appsbd\V1\libs
 */

namespace Appsbd\V1\libs;

if ( ! class_exists( __NAMESPACE__ . '\ACL_Resource' ) ) {
	/**
	 * Class POS ACL Resource
	 *
	 * @package Appsbd\V1\libs
	 */
	class ACL_Resource {
		/**
		 * Its property res_id
		 *
		 * @var int
		 */
		public $res_id;
		/**
		 * Its property action_param
		 *
		 * @var string
		 */
		public $action_param;
		/**
		 * Its property title
		 *
		 * @var string
		 */
		public $title;
		/**
		 * Its property group_title
		 *
		 * @var string
		 */
		public $group_title;
		/**
		 * Its property tooltip_note
		 *
		 * @var string
		 */
		public $tooltip_note;

		/**
		 * The get resource is generated by appsbd
		 *
		 * @param any    $action_param  Its action parameter.
		 * @param any    $title Its title parameter.
		 * @param any    $group_title Its group title parameter.
		 * @param string $tooltip_note Its tooltip note parameter.
		 *
		 * @return ACL_Resource
		 */
		public static function get_resource( $action_param, $title, $group_title, $tooltip_note = '' ) {
			$acl_object               = new ACL_Resource();
			$acl_object->title        = $title;
			$acl_object->group_title  = $group_title;
			$acl_object->res_id       = hash( 'crc32b', $action_param );
			$acl_object->action_param = $action_param;
			$acl_object->tooltip_note = esc_attr( $tooltip_note );
			return $acl_object;
		}
	}
}
