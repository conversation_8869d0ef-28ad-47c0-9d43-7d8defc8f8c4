<?php
/**
 * Its for Pos Warehouse module
 *
 * @package VitePos\Modules
 */

namespace VitePos\Modules;

use Appsbd\V1\Core\BaseModule;
use Appsbd\V1\libs\Ajax_Confirm_Response;
use Appsbd\V1\libs\Ajax_Data_Response;
use Appsbd\V1\libs\API_Data_Response;
use Appsbd\V1\libs\AppInput;
use VitePos\Libs\POS_Customer;
use Vitepos\Models\Database\Mapbd_pos_counter;
use Vitepos\Models\Database\Mapbd_Pos_Role;
use Vitepos\Models\Database\Mapbd_pos_warehouse;

/**
 * Class Apbd_pos_warehouse
 */
class POS_Warehouse extends BaseModule {
	/**
	 * The initialize is generated by appsbd
	 */
	public function initialize() {

		 $this->add_ajax_action( 'add-outlet', array( $this, 'add_outlet' ) );
		 $this->add_ajax_action( 'edit-outlet', array( $this, 'edit_outlet' ) );
		 $this->add_ajax_action( 'delete-outlet', array( $this, 'delete_outlet' ) );
		 $this->add_ajax_action( 'outlet-details', array( $this, 'outlet_details' ) );
		 $this->add_ajax_action( 'outlet-user-list', array( $this, 'outlet_user_list' ) );
		 $this->add_ajax_action( 'remove-outlet-user', array( $this, 'remove_outlet_user' ) );
		 $this->add_ajax_action( 'add-outlet-user', array( $this, 'add_outlet_user' ) );
		 $this->add_ajax_action( 'status_change', array( $this, 'status_change' ) );
		 $this->add_ajax_action( 'main-branch', array( $this, 'main_branch' ) );
		 $this->add_ajax_action( 'counter-add', array( $this, 'counter_add' ) );
		 $this->add_ajax_action( 'counter-edit', array( $this, 'counter_edit' ) );
		 $this->add_ajax_action( 'counter-details', array( $this, 'counter_details' ) );
		 $this->add_ajax_action( 'counter-delete', array( $this, 'counter_delete' ) );
		 add_filter( 'vitepos/filter/outlet-stock', array( $this, 'outlet_stock' ), 99, 3 );
		 add_filter( 'vitepos/filter/check-stock', array( $this, 'check_outlet_stock' ), 99, 3 );
	}

	/**
	 * The outlet stock is generated by appsbd
	 *
	 * @param float       $stock Its stock param.
	 * @param \WC_Product $product it's product param.
	 * @param int         $outlet_id it's outlet_id param.
	 *
	 * @return int
	 */
	public function outlet_stock( $stock, $product, $outlet_id ) {
				if(POS_Settings::is_default_stock()) {
			if (! ($product instanceof \WC_Product) ) {
				$product = wc_get_product( $product );
			}
			return $product->get_stock_quantity();
		}
		if($product instanceof \WC_Product){
			$product_id=$product->get_id();
		}else{
			$product_id=$product;
		}
		if ( ! empty( $outlet_id ) ) {
			$outlet_stocks=(array)get_post_meta($product_id,'_vt_stocks',true);
			if(!empty($outlet_stocks)){
				if ( ! empty( $outlet_stocks[ $outlet_id ] ) ) {
					return $outlet_stocks[ $outlet_id ];
				}
			}
		}
		return $stock;
	}
	/**
	 * The check outlet stock is generated by appsbd
	 *
	 * @param int   $product its product param.
	 * @param float $trans_qty its trans qty param.
	 * @param int   $trans_outlet its trans outlet param.
	 */
	public function check_outlet_stock( $product, $trans_qty, $trans_outlet ) {
		/**
		 * This filter is applied on outlet stock.
		 *
		 * @since 1.0.0
		 */
		$current_stock = apply_filters( 'vitepos/filter/outlet-stock', 0, $product, $trans_outlet );
		if ( $trans_qty <= $current_stock ) {
			return true;
		} else {
			return false;
		}
	}


	/**
	 * The OptionForm is generated by appsbd
	 */
	public function option_form() {
		$this->set_title( 'Outlet List' );
		$this->set_subtitle( '' );
		$this->display();
	}

	/**
	 * The on init is generated by appsbd
	 */
	public function on_init() {
		parent::on_init();
	}

	/**
	 * The get menu title is generated by appsbd
	 *
	 * @return mixed Its mixed.
	 */
	public function get_menu_title() {
		return $this->__( 'Outlet' );
	}
	/**
	 * The get menu sub title is generated by appsbd
	 *
	 * @return mixed Its mixed.
	 */
	public function get_menu_sub_title() {
		return $this->__( 'View All Outlet' );
	}

	/**
	 * The get menu icon is generated by appsbd
	 *
	 * @return string Its string.
	 */
	public function get_menu_icon() {
		return 'aps aps-shop';
	}

	/**
	 * The add is generated by appsbd
	 */
	public function add_outlet() {
		$response = new Ajax_Confirm_Response();
		if ( APPSBD_IS_POST_BACK ) {
			$nobject = new Mapbd_pos_warehouse();
			AppInput::set_bool_value( 'status', 'A', 'I' );
			if ( $nobject->set_from_post_data( true ) ) {
				if ( $nobject->save() ) {
					$this->add_info( 'Successfully added' );
					$response->display_with_response( true );
					return;
				}
			} else {
				$this->add_error( 'Invalid request' );
				$response->display_with_response( false );
				return;
			}
		}
	}

	/**
	 * The edit outlet is generated by appsbd
	 */
	public function edit_outlet() {
		$response  = new Ajax_Confirm_Response();
		$outlet_id = AppInput::post_value( 'id' );
		if ( empty( $outlet_id ) ) {
			$this->add_error( 'Invalid request' );
			$response->display_with_response( false );
			return;
		}
		if ( APPSBD_IS_POST_BACK ) {
			$uobject = new Mapbd_pos_warehouse();
			AppInput::set_bool_value( 'status', 'A', 'I' );
			if ( $uobject->set_from_post_data( false ) ) {
				$propes = 'allowed_ip,wh_timezone,country,name,email,street,city,state,phone,zip_code,status';
				if ( $uobject->unset_all_excepts( $propes ) ) {
					$uobject->set_where_update( 'id', $outlet_id );
					if ( $uobject->Update() ) {
						$this->add_info( 'Successfully updated' );
						$response->display_with_response( true );
						return;
					} else {
						$this->add_error( 'Update Failed' );
						$response->display_with_response( false );
						return;
					}
				} else {
					$this->add_error( 'This Property Should not be update' );
					$response->display_with_response( false );
					return;
				}
			} else {
				$this->add_error( 'Invalid request3' );
				$response->display_with_response( false );
				return;
			}
		} else {
			$this->add_error( 'Invalid request3' );
			$response->display_with_response( false );
			return;
		}
	}


	/**
	 * The grid counter add is generated by appsbd
	 */
	public function counter_add() {
		$response  = new Ajax_Confirm_Response();
		$outlet_id = AppInput::request_value( 'outlet_id' );
		if ( empty( $outlet_id ) ) {
			$this->add_error( 'Invalid request' );
			$response->display_with_response( false );
		}
		if ( APPSBD_IS_POST_BACK ) {
			$nobject = new Mapbd_pos_counter();
			$nobject->outlet_id( $outlet_id );
			if ( $nobject->set_from_post_data( true ) ) {
				if ( $nobject->Save() ) {
					$this->add_info( 'Successfully added' );
					$response->display_with_response( true );
					return;
				}
			}
		}
		$response->display_with_response( false );
	}

	/**
	 * The grid counter edit is generated by appsbd
	 */
	public function counter_edit() {
		$response   = new Ajax_Confirm_Response();
		$outlet_id  = AppInput::request_value( 'outlet_id' );
		$counter_id = AppInput::request_value( 'id' );
		if ( empty( $outlet_id ) || empty( $counter_id ) ) {
			$this->add_error( 'Invalid request' );
			$response->display_with_response( false );
			return;
		}
		if ( APPSBD_IS_POST_BACK ) {
			$uobject = new Mapbd_pos_counter();
			if ( $uobject->set_from_post_data( false ) ) {
				$propes = 'name,counter_number';
				$uobject->unset_all_excepts( $propes );
				$uobject->set_where_update( 'id', $counter_id );
				$uobject->set_where_update( 'outlet_id', $outlet_id );
				if ( $uobject->Update() ) {
					$this->add_info( 'Successfully updated' );
					$response->display_with_response( true );
					return;
				}
			}
		}
		$response->display_with_response( false );
	}
	/**
	 * The grid counter edit is generated by appsbd
	 */
	public function counter_details() {
		$response   = new Ajax_Confirm_Response();
		$outlet_id  = AppInput::post_value( 'outlet_id' );
		$counter_id = AppInput::post_value( 'id' );
		if ( empty( $outlet_id ) || empty( $counter_id ) ) {
			$this->add_error( 'Invalid request' );
			$response->display_with_response( false );
			return;
		}
		$propes  = 'id,outlet_id,name,counter_number';
		$details = new Mapbd_pos_counter();
		$details->id( $counter_id );
		$details->outlet_id( $outlet_id );
		if ( $details->select() ) {
			$response->display_with_response( true, $details->get_properties_api_response( $propes ) );
		} else {
			$this->add_error( 'No counter found with these ids' );
			$response->display_with_response( false, null );
		}

	}

	/**
	 * The view warehouse details is generated by appsbd
	 */
	public function outlet_details() {
		$response  = new Ajax_Confirm_Response();
		$outlet_id = AppInput::post_value( 'id' );
		if ( empty( $outlet_id ) ) {
			$this->add_error( 'Invalid request' );
			$response->display_with_response( false );
			return;
		}
		$propes  = 'id,allowed_ip,email,phone,name,country,state,city,street,zip_code,wh_timezone,status';
		$details = new Mapbd_pos_warehouse();
		$details->id( $outlet_id );
		$details->outlet_id( $outlet_id );
		if ( $details->select() ) {
			$response->display_with_response( true, $details->get_properties_api_response( $propes ) );
		} else {
			$this->add_error( 'No Outlet found with these ids' );
			$response->display_with_response( false, null );
		}
	}

	/**
	 * The view warehouse details is generated by appsbd
	 */
	public function outlet_user_list() {
		$page          = AppInput::post_value( 'page', 1 );
		$limit         = AppInput::post_value( 'limit', 20 );
		$response_user = array();
		$response_data = new Ajax_Data_Response();
		$src_props     = AppInput::post_value( 'src_by', array() );
		$sort_by_props = AppInput::post_value( 'sort_by', array() );
		$roles         = Mapbd_Pos_Role::get_role_list();
		if ( empty( $roles ) ) {
			$response_data->display_grid_response();
		}
		$args = array(
			'role__in'     => array_keys( $roles ),
			'role__not_in' => array( 'customer', 'subscriber' ),
			'count_total'  => true,
			'offset'       => $limit,
			'paged'        => $page,
		);
		POS_Customer::set_search_param( $src_props, $args );
		POS_Customer::set_sort_param( $sort_by_props, $args );
		$user_search = new \WP_User_Query( $args );
		$total_user  = $user_search->get_total();
		$users       = $user_search->get_results();
		foreach ( $users as $user ) {
			$response_user[] = $this->get_user_object( $user );
		}
		$response_data->limit = $limit;
		$response_data->page  = $page;

		if ( $response_data->set_grid_records( $total_user ) ) {
			$response_data->set_grid_data( $response_user );
		}
		$response_data->display_grid_response();

	}

	/**
	 * The remove outlet user is generated by appsbd
	 */
	public function remove_outlet_user() {

		$user_id   = AppInput::post_value( 'user_id' );
		$outlet_id = AppInput::post_value( 'outlet_id' );
		$response  = new Ajax_Confirm_Response();
		if ( ! empty( $user_id ) ) {
			$outlets = get_user_meta( $user_id, 'outlet_id', true );
			if ( in_array( $outlet_id, $outlets ) ) {
				$outlets = array_diff( $outlets, array( $outlet_id ) );
				update_user_meta( $user_id, 'outlet_id', $outlets );
				$this->add_info( 'Outlet removed successfully' );
				$response->display_with_response( true );
				return;
			}
		} else {
			$this->add_error( 'Invalid request' );
			$response->display_with_response( false );
			return;
		}
	}
	/**
	 * The remove outlet user is generated by appsbd
	 */
	public function add_outlet_user() {
		$user_id   = AppInput::post_value( 'user_id' );
		$outlet_id = AppInput::post_value( 'outlet_id' );
		$response  = new Ajax_Confirm_Response();
		if ( ! empty( $user_id ) ) {
			$outlets = get_user_meta( $user_id, 'outlet_id', true );
			if ( empty( $outlets ) ) {
				$outlets = array();
			}
			if ( ! empty( $outlet_id ) ) {
				$outlets = array_merge( $outlets, array( $outlet_id ) );
				update_user_meta( $user_id, 'outlet_id', $outlets );
				$this->add_info( 'Outlet added successfully' );
				$response->display_with_response( true );
				return;
			}
		} else {
			$this->add_error( 'Invalid request' );
			$response->display_with_response( false );
			return;
		}
	}
	/**
	 * The get user object is generated by appsbd
	 *
	 * @param any $user Its string.
	 *
	 * @return \stdClass
	 */
	public function get_user_object( $user ) {
		$users_obj             = new \stdClass();
		$users_obj->id         = $user->ID;
		$users_obj->first_name = $user->first_name;
		$users_obj->last_name  = $user->last_name;
		$users_obj->username   = $user->user_nicename;
		$users_obj->email      = $user->user_email;
		$users_obj->city       = get_user_meta( $user->ID, 'billing_city', true );
				$users_obj->contact_no  = get_user_meta( $user->ID, 'billing_phone', true );
		$users_obj->street      = get_user_meta( $user->ID, 'billing_address_1', true );
		$users_obj->country     = get_user_meta( $user->ID, 'billing_country', true );
		$users_obj->postcode    = get_user_meta( $user->ID, 'billing_postcode', true );
		$users_obj->designation = get_user_meta( $user->ID, 'designation', true );
		$users_obj->outlet_id   = get_user_meta( $user->ID, 'outlet_id', true );
		$users_obj->role        = array();
		$roles                  = Mapbd_Pos_Role::get_role_list();
		foreach ( $user->roles as $role_slug ) {
			if ( isset( $roles[ $role_slug ] ) ) {
				$obj               = new \stdClass();
				$obj->slug         = $role_slug;
				$obj->name         = $roles[ $role_slug ]->name;
				$users_obj->role[] = $obj;
			}
		}
		if ( '' == $users_obj->outlet_id ) {
			$users_obj->outlet_id = array();
		}
		return $users_obj;
	}
	/**
	 * The data is generated by appsbd
	 */
	public function data() {
		$main_response = new Ajax_Data_Response();
		$main_response->set_download_filename( 'pos-outlet-list' );
		$mainobj = new Mapbd_pos_warehouse();
		$mainobj->set_search_by_param( $main_response->src_by, 'name,phone' );
		$mainobj->set_sort_by_param( $main_response->sort_by );
		$records = $mainobj->count_aLL( $main_response->src_item, $main_response->src_text, $main_response->multiparam, 'after' );

		if ( $records > 0 ) {
			$main_response->set_grid_records( $records );
			$result = $mainobj->select_all_grid_data(
				'',
				'',
				'',
				$main_response->limit,
				$main_response->limit_start(),
				$main_response->src_item,
				$main_response->src_text,
				$main_response->multiparam,
				'after'
			);
			if ( $result ) {
				$counters = Mapbd_pos_counter::get_counters();
				foreach ( $result as &$data ) {
					$data->counters = ! empty( $counters[ $data->id ] ) ? $counters[ $data->id ] : array();
				}
			}
			$main_response->set_grid_data( $result );
		}
		$main_response->display_grid_response();
	}
	/**
	 * The delete item is generated by appsbd
	 */
	public function delete_outlet() {
		$param         = AppInput::post_value( 'id' );
		$main_response = new Ajax_Confirm_Response();
		if ( empty( $param ) ) {
			$this->add_error( 'Invalid Request' );
			$main_response->display_with_response( false );

			return;
		}
		$mr = new Mapbd_pos_warehouse();
		$mr->id( $param );
		if ( $mr->select() ) {
			if ( Mapbd_pos_warehouse::delete_by_id( $param ) ) {
				$this->add_info( 'Successfully deleted' );
				$main_response->display_with_response( true );
			} else {
				$this->add_error( 'Delete failed try again' );
				$main_response->display_with_response( false );
			}
		}
	}

	/**
	 * The grid counter delete is generated by appsbd
	 */
	public function counter_delete() {
		$param         = AppInput::post_value( 'id' );
		$main_response = new Ajax_Confirm_Response();
		if ( empty( $param ) ) {
			$this->add_error( 'Invalid Request' );
			$main_response->display_with_response( false );

			return;
		}
		$mr = new Mapbd_pos_counter();
		$mr->id( $param );
		if ( $mr->select() ) {
			if ( Mapbd_pos_counter::delete_by_id( $param ) ) {
				$this->add_info( 'Successfully deleted' );
				$main_response->display_with_response( true );
			} else {
				$this->add_error( 'Delete failed try again' );
				$main_response->display_with_response( false );
			}
		}
	}

	/**
	 * The status change is generated by appsbd
	 */
	public function status_change() {
		$param         = APBD_GetValue( 'id' );
		$main_response = new AppsbdAjaxConfirmResponse();
		if ( empty( $param ) ) {
			$main_response->DisplayWithResponse( false, __( 'Invalid Request' ) );
			 return;
		}
		$mr            = new Mapbd_pos_warehouse();
		$status_change = $mr->GetPropertyOptionsTag( 'status' );

		$mr->id( $param );
		if ( $mr->Select( 'status' ) ) {
			$new_status = 'A' == $mr->status ? 'I' : 'A';
			$uo         = new Mapbd_pos_warehouse();
			$uo->status( $new_status );
			$uo->SetWhereUpdate( 'id', $param );
			if ( $uo->Update() ) {
				$status_text = appsbd_get_text_by_key( $uo->status, $status_change );
				APBD_AddLog( 'U', $uo->settedPropertyforLog(), 'l002', 'Wp_apbd_pos_warehouse' );
				$main_response->DisplayWithResponse( true, __( 'Successfully Updated' ), $status_text );
			} else {
				$main_response->DisplayWithResponse( false, __( 'Update failed try again' ) );
			}
		}

	}

	/**
	 * The main branch is generated by appsbd
	 */
	public function main_branch() {
		$param         = AppInput::post_value( 'id' );
		$main_response = new Ajax_Confirm_Response();
		if ( empty( $param ) ) {
			$this->add_error( 'Invalid Request' );
			$main_response->display_with_response( false );

			return;
		}
		$mr = new Mapbd_pos_warehouse();
		$mr->id( $param );
		if ( $mr->Select( 'main_branch' ) ) {
			$new_status = ( 'Y' == $mr->main_branch ? 'N' : 'Y' );
			$up         = new Mapbd_pos_warehouse();
			$up->main_branch( 'N' );
			$up->set_where_update( 'main_branch', 'Y' );
			$up->update( true );

			$uo = new Mapbd_pos_warehouse();
			$uo->main_branch( $new_status );
			$uo->set_where_update( 'id', $param );
			if ( $uo->update() ) {
				$this->add_info( 'Successfully Updated' );
				$main_response->display_with_response( true );
			} else {
				$this->add_error( 'Update failed try again' );
				$main_response->display_with_response( false );
			}
		}
	}
}
