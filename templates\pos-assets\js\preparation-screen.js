/**
 * Preparation Screen JavaScript
 * Handles category-based order preparation management
 */

class PreparationScreen {
    constructor() {
        this.orders = [];
        this.categories = [];
        this.selectedCategory = 'all';
        this.refreshInterval = null;
        this.timerInterval = null;
        this.shiftStartTime = new Date();
        this.stats = {
            pending: 0,
            preparing: 0,
            ready: 0,
            completed: 0
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startTimer();
        this.loadCategories();
        this.loadOrders();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // Category filter
        document.getElementById('category-filter').addEventListener('change', (e) => {
            this.selectedCategory = e.target.value;
            this.renderOrders();
        });

        // Refresh button
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.loadOrders();
        });

        // Confirmation modal
        document.getElementById('confirm-action').addEventListener('click', () => {
            this.executeConfirmedAction();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F5') {
                e.preventDefault();
                this.loadOrders();
            }
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                this.loadOrders();
            }
        });
    }

    async loadCategories() {
        try {
            const response = await fetch(preparationConfig.urls.category_list, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();

            if (data.success) {
                this.categories = data.data || [];
                this.populateCategoryFilter();
            }
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    }

    populateCategoryFilter() {
        const select = document.getElementById('category-filter');
        const currentValue = select.value;

        // Clear existing options except "all"
        select.innerHTML = '<option value="all">جميع الفئات</option>';

        this.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.term_id;
            option.textContent = category.name;
            select.appendChild(option);
        });

        // Restore selected value
        select.value = currentValue;
    }

    async loadOrders() {
        try {
            this.showLoading();

            const response = await fetch(preparationConfig.urls.kitchen_order_list, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    limit: 100,
                    page: 1
                })
            });

            const data = await response.json();

            if (data.success) {
                this.orders = data.data.orders || [];
                this.updateStats();
                this.renderOrders();
            } else {
                this.showError('فشل في تحميل الطلبات');
            }
        } catch (error) {
            console.error('Error loading orders:', error);
            this.showError('خطأ في الاتصال بالخادم');
        }
    }

    updateStats() {
        this.stats = {
            pending: this.orders.filter(o => o.status === 'vt_in_kitchen').length,
            preparing: this.orders.filter(o => o.status === 'vt_preparing').length,
            ready: this.orders.filter(o => o.status === 'vt_ready_to_srv').length,
            completed: this.getCompletedTodayCount()
        };

        document.getElementById('pending-count').textContent = this.stats.pending;
        document.getElementById('preparing-count').textContent = this.stats.preparing;
        document.getElementById('ready-count').textContent = this.stats.ready;
        document.getElementById('completed-count').textContent = this.stats.completed;

        // Update badges
        document.getElementById('pending-badge').textContent = this.stats.pending;
        document.getElementById('preparing-badge').textContent = this.stats.preparing;
        document.getElementById('ready-badge').textContent = this.stats.ready;
    }

    getCompletedTodayCount() {
        const today = new Date().toDateString();
        return this.orders.filter(order => {
            const orderDate = new Date(order.date_modified || order.date_created).toDateString();
            return orderDate === today && order.status === 'completed';
        }).length;
    }

    renderOrders() {
        const pendingContainer = document.getElementById('pending-orders');
        const preparingContainer = document.getElementById('preparing-orders');
        const readyContainer = document.getElementById('ready-orders');

        const pendingOrders = this.filterOrdersByCategory(
            this.orders.filter(o => o.status === 'vt_in_kitchen')
        );
        const preparingOrders = this.filterOrdersByCategory(
            this.orders.filter(o => o.status === 'vt_preparing')
        );
        const readyOrders = this.filterOrdersByCategory(
            this.orders.filter(o => o.status === 'vt_ready_to_srv')
        );

        pendingContainer.innerHTML = this.renderOrderSection(pendingOrders, 'pending');
        preparingContainer.innerHTML = this.renderOrderSection(preparingOrders, 'preparing');
        readyContainer.innerHTML = this.renderOrderSection(readyOrders, 'ready');

        this.attachOrderEventListeners();
    }

    filterOrdersByCategory(orders) {
        if (this.selectedCategory === 'all') {
            return orders;
        }

        return orders.filter(order => {
            return order.line_items.some(item => {
                return item.categories && item.categories.includes(parseInt(this.selectedCategory));
            });
        });
    }

    renderOrderSection(orders, section) {
        if (orders.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <p>لا توجد طلبات</p>
                </div>
            `;
        }

        return orders.map(order => this.createOrderCard(order, section)).join('');
    }

    createOrderCard(order, section) {
        const timeElapsed = this.calculateTimeElapsed(order.date_created);
        const isUrgent = timeElapsed > 30;
        const urgentClass = isUrgent ? 'urgent' : '';

        return `
            <div class="order-card ${urgentClass}"
                 data-order-id="${order.id}"
                 data-status="${order.status}"
                 draggable="true">

                <div class="order-header">
                    <div class="order-number">#${order.id}</div>
                    <div class="order-time">
                        <i class="fas fa-clock"></i>
                        <span class="time-elapsed ${isUrgent ? 'time-urgent' : ''}">${timeElapsed} دقيقة</span>
                    </div>
                </div>

                <div class="order-items">
                    ${order.line_items.map(item => `
                        <div class="order-item">
                            <div class="item-name">${item.name}</div>
                            <div class="item-quantity">${item.quantity}</div>
                            ${this.getItemCategory(item) ? `
                                <div class="item-category">${this.getItemCategory(item)}</div>
                            ` : ''}
                            ${item.meta_data && item.meta_data.length > 0 ? `
                                <div class="item-notes">${item.meta_data.map(meta => meta.value).join(', ')}</div>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>

                <div class="order-actions">
                    ${this.getActionButtons(order, section)}
                </div>

                <div class="order-category">
                    <span class="category-badge">${this.getOrderMainCategory(order)}</span>
                </div>

                ${isUrgent ? '<div class="priority-indicator priority-high"></div>' : ''}
            </div>
        `;
    }

    getItemCategory(item) {
        if (!item.categories || item.categories.length === 0) return null;

        const category = this.categories.find(cat =>
            item.categories.includes(cat.term_id)
        );

        return category ? category.name : null;
    }

    getOrderMainCategory(order) {
        const categories = new Set();

        order.line_items.forEach(item => {
            if (item.categories) {
                item.categories.forEach(catId => {
                    const category = this.categories.find(cat => cat.term_id === catId);
                    if (category) categories.add(category.name);
                });
            }
        });

        return Array.from(categories).join(', ') || 'عام';
    }

    getActionButtons(order, section) {
        switch (section) {
            case 'pending':
                return `
                    <button class="action-btn btn-start" onclick="preparationScreen.startPreparing(${order.id})">
                        <i class="fas fa-play"></i> بدء
                    </button>
                    <button class="action-btn btn-details" onclick="preparationScreen.showOrderDetails(${order.id})">
                        <i class="fas fa-eye"></i> تفاصيل
                    </button>
                `;
            case 'preparing':
                return `
                    <button class="action-btn btn-complete" onclick="preparationScreen.completePreparing(${order.id})">
                        <i class="fas fa-check"></i> اكتمل
                    </button>
                    <button class="action-btn btn-details" onclick="preparationScreen.showOrderDetails(${order.id})">
                        <i class="fas fa-eye"></i> تفاصيل
                    </button>
                `;
            case 'ready':
                return `
                    <button class="action-btn btn-details" onclick="preparationScreen.showOrderDetails(${order.id})">
                        <i class="fas fa-eye"></i> تفاصيل
                    </button>
                `;
            default:
                return '';
        }
    }

    calculateTimeElapsed(dateCreated) {
        const now = new Date();
        const created = new Date(dateCreated);
        const diffInMinutes = Math.floor((now - created) / (1000 * 60));
        return diffInMinutes;
    }

    async startPreparing(orderId) {
        this.showConfirmation(
            'بدء تحضير الطلب',
            `هل تريد بدء تحضير الطلب #${orderId}؟`,
            () => this.executeStartPreparing(orderId)
        );
    }

    async executeStartPreparing(orderId) {
        try {
            const response = await fetch(preparationConfig.urls.start_preparing, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ order_id: orderId })
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('تم بدء تحضير الطلب');
                this.loadOrders();
            } else {
                this.showError('فشل في بدء التحضير');
            }
        } catch (error) {
            console.error('Error starting preparation:', error);
            this.showError('خطأ في الاتصال');
        }
    }

    async completePreparing(orderId) {
        this.showConfirmation(
            'إكمال تحضير الطلب',
            `هل تريد إكمال تحضير الطلب #${orderId}؟`,
            () => this.executeCompletePreparing(orderId)
        );
    }

    async executeCompletePreparing(orderId) {
        try {
            const response = await fetch(preparationConfig.urls.complete_preparing, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ order_id: orderId })
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('تم إكمال تحضير الطلب');
                this.loadOrders();
            } else {
                this.showError('فشل في إكمال التحضير');
            }
        } catch (error) {
            console.error('Error completing preparation:', error);
            this.showError('خطأ في الاتصال');
        }
    }

    async showOrderDetails(orderId) {
        try {
            const response = await fetch(preparationConfig.urls.order_details, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ order_id: orderId })
            });

            const data = await response.json();

            if (data.success) {
                this.displayOrderDetails(data.data);
            } else {
                this.showError('فشل في تحميل تفاصيل الطلب');
            }
        } catch (error) {
            console.error('Error loading order details:', error);
            this.showError('خطأ في الاتصال');
        }
    }

    displayOrderDetails(order) {
        const modalContent = document.getElementById('order-details-content');

        modalContent.innerHTML = `
            <div class="order-details-view">
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات الطلب</h6>
                        <p><strong>رقم الطلب:</strong> #${order.id}</p>
                        <p><strong>الوقت:</strong> ${new Date(order.date_created).toLocaleString('ar-SA')}</p>
                        <p><strong>الحالة:</strong> ${this.getStatusText(order.status)}</p>
                        ${order.table_number ? `<p><strong>الطاولة:</strong> ${order.table_number}</p>` : ''}
                        ${order.customer_note ? `<p><strong>ملاحظة العميل:</strong> ${order.customer_note}</p>` : ''}
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات العميل</h6>
                        <p><strong>الاسم:</strong> ${order.billing.first_name} ${order.billing.last_name}</p>
                        ${order.billing.phone ? `<p><strong>الهاتف:</strong> ${order.billing.phone}</p>` : ''}
                        ${order.billing.email ? `<p><strong>البريد:</strong> ${order.billing.email}</p>` : ''}
                    </div>
                </div>

                <hr>

                <h6>عناصر الطلب</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الفئة</th>
                                <th>الكمية</th>
                                <th>الملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${order.line_items.map(item => `
                                <tr>
                                    <td>${item.name}</td>
                                    <td>${this.getItemCategory(item) || '-'}</td>
                                    <td><span class="badge bg-primary">${item.quantity}</span></td>
                                    <td>${item.meta_data && item.meta_data.length > 0 ?
                                        item.meta_data.map(meta => meta.value).join(', ') : '-'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                ${order.total ? `
                    <div class="text-end mt-3">
                        <h5>المجموع: ${order.total} ريال</h5>
                    </div>
                ` : ''}
            </div>
        `;

        const modal = new bootstrap.Modal(document.getElementById('orderDetailsModal'));
        modal.show();
    }

    getStatusText(status) {
        switch (status) {
            case 'vt_in_kitchen': return 'في المطبخ';
            case 'vt_preparing': return 'قيد التحضير';
            case 'vt_ready_to_srv': return 'جاهز للتقديم';
            case 'vt_served': return 'تم التقديم';
            default: return status;
        }
    }

    startTimer() {
        const updateTimer = () => {
            const now = new Date();
            const elapsed = now - this.shiftStartTime;

            const hours = Math.floor(elapsed / (1000 * 60 * 60));
            const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((elapsed % (1000 * 60)) / 1000);

            const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('timer-display').textContent = timeString;
        };

        updateTimer();
        this.timerInterval = setInterval(updateTimer, 1000);
    }

    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.loadOrders();
        }, preparationConfig.refreshInterval);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    attachOrderEventListeners() {
        // Add drag and drop functionality
        document.querySelectorAll('.order-card').forEach(card => {
            card.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', card.dataset.orderId);
                card.classList.add('dragging');
            });

            card.addEventListener('dragend', () => {
                card.classList.remove('dragging');
            });

            // Click to show details
            card.addEventListener('click', (e) => {
                if (!e.target.closest('.order-actions')) {
                    this.showOrderDetails(card.dataset.orderId);
                }
            });
        });

        // Add drop zones
        document.querySelectorAll('.orders-container').forEach(container => {
            container.addEventListener('dragover', (e) => {
                e.preventDefault();
                container.classList.add('drag-over');
            });

            container.addEventListener('dragleave', () => {
                container.classList.remove('drag-over');
            });

            container.addEventListener('drop', (e) => {
                e.preventDefault();
                container.classList.remove('drag-over');

                const orderId = e.dataTransfer.getData('text/plain');
                const targetStatus = this.getContainerStatus(container);

                if (targetStatus) {
                    this.moveOrderToStatus(orderId, targetStatus);
                }
            });
        });
    }

    getContainerStatus(container) {
        if (container.id === 'preparing-orders') return 'vt_preparing';
        if (container.id === 'ready-orders') return 'vt_ready_to_srv';
        return null;
    }

    moveOrderToStatus(orderId, targetStatus) {
        const order = this.orders.find(o => o.id == orderId);
        if (!order) return;

        if (order.status === 'vt_in_kitchen' && targetStatus === 'vt_preparing') {
            this.startPreparing(orderId);
        } else if (order.status === 'vt_preparing' && targetStatus === 'vt_ready_to_srv') {
            this.completePreparing(orderId);
        }
    }

    showConfirmation(title, message, callback) {
        document.querySelector('#confirmationModal .modal-title').textContent = title;
        document.getElementById('confirmation-message').textContent = message;

        this.pendingAction = callback;

        const modal = new bootstrap.Modal(document.getElementById('confirmationModal'));
        modal.show();
    }

    executeConfirmedAction() {
        if (this.pendingAction) {
            this.pendingAction();
            this.pendingAction = null;
        }

        bootstrap.Modal.getInstance(document.getElementById('confirmationModal')).hide();
    }

    showLoading() {
        ['pending-orders', 'preparing-orders', 'ready-orders'].forEach(containerId => {
            document.getElementById(containerId).innerHTML = `
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                </div>
            `;
        });
    }

    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showError(message) {
        this.showToast(message, 'error');
    }

    showToast(message, type) {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            ${message}
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

// Initialize preparation screen when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.preparationScreen = new PreparationScreen();

    // Set up POS integration if available
    if (window.posIntegration) {
        // Listen for new orders
        window.posIntegration.onOrderCreated((order) => {
            console.log('New order received in preparation screen:', order);
            window.preparationScreen.loadOrders(); // Refresh the display
            window.preparationScreen.showSuccess('طلب جديد رقم #' + order.id);
        });

        // Listen for order updates
        window.posIntegration.onOrderUpdated((order) => {
            console.log('Order updated in preparation screen:', order);
            window.preparationScreen.loadOrders(); // Refresh the display
        });

        // Listen for status changes
        window.posIntegration.onOrderStatusChanged((data) => {
            console.log('Order status changed in preparation screen:', data);
            window.preparationScreen.loadOrders(); // Refresh the display

            if (data.newStatus === 'vt_ready_to_srv') {
                window.preparationScreen.showSuccess('طلب رقم #' + data.order.id + ' جاهز للتقديم');
            }
        });
    }
});

// Handle page visibility change
document.addEventListener('visibilitychange', () => {
    if (window.preparationScreen) {
        if (document.hidden) {
            window.preparationScreen.stopAutoRefresh();
        } else {
            window.preparationScreen.startAutoRefresh();
            window.preparationScreen.loadOrders();
        }
    }
});
