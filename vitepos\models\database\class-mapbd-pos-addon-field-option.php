<?php
/**
 * Pos Vendor Database Model
 *
 * @package Vitepos\Models\Database
 */

namespace Vitepos\Models\Database;

use VitePos\Core\ViteposModel;

/**
 * Class Mapbd_pos_vendor
 *
 * @properties id,name,email,contact_no,vendor_note,status,added_by
 */
class Mapbd_Pos_Addon_Field_Option extends ViteposModel {
	/**
	 * Its property id
	 *
	 * @var int
	 */
	public $id;
	/**
	 * Its property label
	 *
	 * @var string
	 */
	public $label;
	/**
	 * Its property visual
	 *
	 * @var bool
	 */
	public $visual;
	/**
	 * Its property price
	 *
	 * @var float
	 */
	public $price;
	/**
	 * Its property is_selected
	 *
	 * @var bool
	 */
	public $is_selected;
	/**
	 * Its property field_id
	 *
	 * @var int
	 */
	public $field_id;


	/**
	 * Mapbd_pos_vendor constructor.
	 */
	public function __construct() {
		parent::__construct();
		$this->set_validation();
		$this->table_name     = 'apbd_pos_addon_field_option';
		$this->primary_key    = 'id';
		$this->unique_key     = array();
		$this->multi_key      = array();
		$this->auto_inc_field = array( 'id' );
		$this->app_base_name  = 'apbd-elite-pos';

	}


	/**
	 * The set validation is generated by appsbd
	 */
	public function set_validation() {
		$this->validations = array(
			'id'          => array(
				'Text' => 'Id',
				'Rule' => 'max_length[11]|integer',
			),
			'label'       => array(
				'Text' => 'Label',
				'Rule' => 'required|max_length[150]',
			),
			'visual'      => array(
				'Text' => 'Visual',
				'Rule' => 'max_length[1]',
			),
			'price'       => array(
				'Text' => 'Price',
				'Rule' => 'max_length[10]|numeric',
			),
			'is_selected' => array(
				'Text' => 'Is Selected',
				'Rule' => 'max_length[1]',
			),
			'field_id'    => array(
				'Text' => 'Field Id',
				'Rule' => 'max_length[11]|integer',
			),

		);
	}

	/**
	 * The get property raw options is generated by appsbd
	 *
	 * @param \Appsbd\V1\Core\any $property Its string.
	 * @param false               $is_with_select Its bool.
	 *
	 * @return array|string[]
	 */
	public function get_property_raw_options( $property, $is_with_select = false ) {
		$return_obj = array();
		switch ( $property ) {
			case 'visual':
				$return_obj = array(
					'C' => 'Color',
					'I' => 'Image',
					'N' => 'None',
				);
				break;
			case 'is_selected':
				$return_obj = array(
					'Y' => 'Yes',
					'N' => 'No',
				);
				break;
			default:
		}
		if ( $is_with_select ) {
			return array_merge( array( '' => 'Select' ), $return_obj );
		}
		return $return_obj;

	}

	/**
	 * The create db table is generated by appsbd
	 */
	public static function create_db_table() {
		$this_obj = new static();
		$table    = $this_obj->db->prefix . $this_obj->table_name;
		if ( $this_obj->db->get_var( "show tables like '{$table}'" ) != $table ) {
			$sql = "CREATE TABLE `{$table}` (
					  	`id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  						`label` char(150) CHARACTER SET utf8mb4 NOT NULL DEFAULT '',
  						`visual` char(1) NOT NULL DEFAULT 'N' COMMENT 'radio(I=Image,C=Color,N=None)',
  						`price` decimal(10,2) unsigned NOT NULL,
  						`is_selected` char(1) NOT NULL COMMENT 'bool(Y=Yes,N=No)',
  						`field_id` int(11) unsigned NOT NULL,
  						PRIMARY KEY (`id`)
					) ";
			require_once ABSPATH . 'wp-admin/includes/upgrade.php';
			dbDelta( $sql );
		}
	}

	/**
	 * The delete by id is generated by appsbd
	 *
	 * @param any $id Its vendor id param.
	 *
	 * @return bool
	 */
	public static function delete_by_id( $id ) {
		return parent::delete_by_key_value( 'id', $id );
	}
	/**
	 * The delete by id is generated by appsbd
	 *
	 * @param any $field_id Its vendor field_id param.
	 *
	 * @return bool
	 */
	public static function delete_by_field_id( $field_id ) {
		return parent::delete_by_key_value( 'field_id', $field_id, true );
	}
}
