/* Preparation Screen Styles */
.preparation-screen-body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
    direction: rtl;
}

/* Header Styles */
.preparation-header {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    border-bottom: 3px solid #28a745;
}

.preparation-header h2 {
    color: #2c3e50;
    font-weight: 700;
}

.filter-controls .form-select {
    min-width: 150px;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    font-weight: 600;
}

.timer-display {
    background: #f8f9fa;
    padding: 0.5rem 1rem;
    border-radius: 10px;
    font-weight: 700;
    color: #28a745;
    border: 2px solid #28a745;
}

/* Statistics Section */
.stats-section {
    padding: 1rem;
    margin-bottom: 1rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.stat-card.pending {
    border-top: 4px solid #ffc107;
}

.stat-card.preparing {
    border-top: 4px solid #fd7e14;
}

.stat-card.ready {
    border-top: 4px solid #28a745;
}

.stat-card.completed {
    border-top: 4px solid #6f42c1;
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.stat-card.pending .stat-icon {
    color: #ffc107;
}

.stat-card.preparing .stat-icon {
    color: #fd7e14;
}

.stat-card.ready .stat-icon {
    color: #28a745;
}

.stat-card.completed .stat-icon {
    color: #6f42c1;
}

.stat-info h4 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.stat-info p {
    color: #6c757d;
    font-weight: 600;
    margin-bottom: 0;
}

/* Category Sections */
.category-sections {
    padding: 0 1rem 1rem;
    height: calc(100vh - 300px);
}

.category-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 1rem;
    height: 100%;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    margin-bottom: 1rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section-header h5 {
    margin: 0;
    font-weight: 700;
    color: #2c3e50;
}

.pending-section .section-header {
    border-left: 4px solid #ffc107;
}

.preparing-section .section-header {
    border-left: 4px solid #fd7e14;
}

.ready-section .section-header {
    border-left: 4px solid #28a745;
}

.orders-container {
    height: calc(100% - 80px);
    overflow-y: auto;
    padding-right: 10px;
}

/* Order Cards */
.order-card {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border-left: 4px solid #e9ecef;
}

.order-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.order-card[data-status="vt_in_kitchen"] {
    border-left-color: #ffc107;
}

.order-card[data-status="vt_preparing"] {
    border-left-color: #fd7e14;
}

.order-card[data-status="vt_ready_to_srv"] {
    border-left-color: #28a745;
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.order-number {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
}

.order-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.time-elapsed {
    font-weight: 600;
}

.time-urgent {
    color: #dc3545;
    font-weight: 700;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

/* Order Items */
.order-items {
    margin-bottom: 1rem;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.order-item:last-child {
    border-bottom: none;
}

.item-name {
    font-weight: 600;
    color: #2c3e50;
    flex: 1;
}

.item-quantity {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 600;
    min-width: 30px;
    text-align: center;
}

.item-category {
    font-size: 0.7rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.item-notes {
    font-size: 0.8rem;
    color: #6c757d;
    font-style: italic;
    margin-top: 0.25rem;
}

/* Order Actions */
.order-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-btn {
    flex: 1;
    min-width: 80px;
    padding: 0.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    font-size: 0.8rem;
}

.action-btn:hover {
    transform: translateY(-1px);
}

.btn-start {
    background: #ffc107;
    color: #212529;
}

.btn-start:hover {
    background: #e0a800;
}

.btn-complete {
    background: #28a745;
    color: white;
}

.btn-complete:hover {
    background: #218838;
}

.btn-details {
    background: #17a2b8;
    color: white;
}

.btn-details:hover {
    background: #138496;
}

/* Order Category Badge */
.order-category {
    margin-top: 0.5rem;
    text-align: center;
}

.category-badge {
    background: #f8f9fa;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 600;
    border: 1px solid #e9ecef;
}

/* Priority Indicators */
.priority-indicator {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.priority-high {
    background: #dc3545;
    animation: pulse 2s infinite;
}

.priority-medium {
    background: #ffc107;
}

.priority-low {
    background: #28a745;
}

/* Loading States */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #28a745;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    font-size: 1.1rem;
    margin: 0;
}

/* Drag and Drop */
.order-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.orders-container.drag-over {
    background: rgba(40, 167, 69, 0.1);
    border: 2px dashed #28a745;
    border-radius: 10px;
}

/* Responsive Design */
@media (max-width: 992px) {
    .category-sections {
        height: auto;
    }
    
    .category-section {
        height: auto;
        margin-bottom: 1rem;
    }
    
    .orders-container {
        height: 400px;
    }
}

@media (max-width: 768px) {
    .stats-section .col-md-3 {
        margin-bottom: 1rem;
    }
    
    .filter-controls {
        margin-bottom: 1rem;
    }
    
    .timer-display {
        margin-bottom: 1rem;
    }
    
    .preparation-header .d-flex {
        flex-direction: column;
        gap: 1rem;
    }
    
    .order-actions {
        flex-direction: column;
    }
    
    .action-btn {
        min-width: auto;
    }
}

/* Scrollbar Styling */
.orders-container::-webkit-scrollbar {
    width: 8px;
}

.orders-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.orders-container::-webkit-scrollbar-thumb {
    background: #28a745;
    border-radius: 10px;
}

.orders-container::-webkit-scrollbar-thumb:hover {
    background: #218838;
}

/* Animation for new orders */
.order-card.new-order {
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Pulse animation for urgent orders */
@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
    }
}
