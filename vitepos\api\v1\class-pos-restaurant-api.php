<?php
/**
 * Its api for order
 *
 * @since: 12/07/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package VitePos\Api\V1
 */

namespace VitePos\Api\V1;

use Appsbd\V1\libs\API_Data_Response;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;
use VitePos\Libs\API_Base;
use VitePos\Libs\POS_Order;
use VitePos\Libs\POS_Payment;
use Vitepos\Models\Database\Mapbd_Pos_Cash_Drawer;
use Vitepos\Models\Database\Mapbd_Pos_Cash_Drawer_Types;
use Vitepos\Models\Database\Mapbd_Pos_Message;
use Vitepos\Models\Database\Mapbd_Pos_Role;
use VitePos\Modules\APBD_EPOS_Settings;
use VitePos\Modules\POS_Settings;

/**
 * Class pos_order_api
 *
 * @package VitePos\Api\V1
 */
class Pos_Restaurant_Api extends API_Base {

	/**
	 * The set api base is generated by appsbd
	 *
	 * @return mixed|string
	 */
	public function set_api_base() {
		return 'restaurant';
	}

	/**
	 * The routes is generated by appsbd
	 *
	 * @return mixed|void
	 */
	public function routes() {
		$this->register_rest_route( 'POST', 'send-to-kitchen', array( $this, 'send_to_kitchen' ) );
		$this->register_rest_route( 'POST', 'start-preparing', array( $this, 'start_preparing' ) );
		$this->register_rest_route( 'POST', 'make-served', array( $this, 'make_served' ) );
		$this->register_rest_route( 'POST', 'deny-order', array( $this, 'deny_order' ) );
		$this->register_rest_route( 'POST', 'cancel-order', array( $this, 'cancel_order' ) );
		$this->register_rest_route( 'POST', 'cancel-order-request', array( $this, 'cancel_request' ) );
		$this->register_rest_route( 'POST', 'cancel-request-ans', array( $this, 'cancel_request_ans' ) );
		$this->register_rest_route( 'POST', 'add-kitchen-note', array( $this, 'add_kitchen_msg' ) );
		$this->register_rest_route( 'POST', 'complete-preparing', array( $this, 'complete_preparing' ) );
		$this->register_rest_route( 'POST', 'restaurant-payment', array( $this, 'make_payment' ) );
		$this->register_rest_route( 'POST', 'sync-offline-order', array( $this, 'sync_offline_payment' ) );
		$this->register_rest_route( 'POST', 'waiter-order-list', array( $this, 'waiter_order_list' ) );
		$this->register_rest_route( 'POST', 'online-list', array( $this, 'online_order_list' ) );
		$this->register_rest_route( 'POST', 'kitchen-order-list', array( $this, 'kitchen_order_list' ) );
		$this->register_rest_route( 'POST', 'served-list', array( $this, 'served_list' ) );
		$this->register_rest_route( 'POST', 'canned-message', array( $this, 'canned_messages' ) );
		$this->register_rest_route( 'POST', 'sync-order-list', array( $this, 'sync_order_list' ) );
		$this->register_rest_route( 'POST', 'change-status', array( $this, 'change_status' ) );
		$this->register_rest_route( 'POST', 'change-table', array( $this, 'change_order_table' ) );
		$this->register_rest_route( 'GET', 'details/(?P<id>\d+)', array( $this, 'order_details' ) );
		$this->register_rest_route( 'GET', 'cashier-details/(?P<id>\d+)', array( $this, 'cashier_details' ) );
	}

	/**
	 * The set route permission is generated by appsbd
	 *
	 * @param \VitePos\Libs\any $route Its string.
	 *
	 * @return bool
	 */
	public function set_route_permission( $route ) {
		switch ( $route ) {
			case ( 'restaurant-payment' ):
				return current_user_can( 'pos-menu' ) || current_user_can( 'cashier-menu' );
		case ( 'canned-message' ):
				return current_user_can( 'pos-menu' ) || current_user_can( 'cashier-menu' )|| current_user_can( 'waiter-menu' )|| current_user_can( 'kitchen-menu' );
			case ( 'send-to-kitchen' ):
				return current_user_can( 'waiter-to-kitchen' ) || current_user_can( 'pos-menu' ) || current_user_can( 'cashier-menu' );
			case ( 'start-preparing' ):
				return current_user_can( 'start-preparing' );
			case ( 'deny-order' ):
				return current_user_can( 'deny-order' );
			case ( 'cancel-order' ):
				return current_user_can( 'cancel-order' )|| current_user_can('cancel-waiter-order');
			case ( 'cancel-request-ans' ):
				return current_user_can( 'accept-cancel' ) || current_user_can( 'deny-cancel' );
			case ( 'complete-preparing' ):
				return current_user_can( 'ready-order' );
			case 'order-list':
				return current_user_can( 'order-list' );
			case 'order_details':
				return current_user_can( 'order-details' );
			default:
				return POS_Settings::is_pos_user();
		}

		return parent::set_route_permission( $route );
	}

	/**
	 * The make payment is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function make_payment() {
		try {
			return $this->make_order_payment( false );
		} catch ( Exception $e ) {
			$this->add_error( $e->getMessage() );
			$this->response->set_response( false );
		} catch ( \WC_Data_Exception $e ) {
			$this->add_error( $e->getMessage() );
			$this->response->set_response( false );
		}
		return $this->response;
	}
	/**
	 * The make payment is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function sync_offline_payment() {
		$this->add_error( 'No Offline order for restaurent' );
		$this->response->set_response( false );
		return $this->response->get_response();
	}
	/**
	 * The make order payment is generated by appsbd
	 *
	 * @param false $is_offline Is offline or not.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 * @throws \WC_Data_Exception Throw data exception.
	 */
	private function make_order_payment( $is_offline = false ) {
		self::set_vite_pos_request();
		$order_id = $this->get_payload( 'order_id' );
		$payment  = new POS_Payment( $this->payload, $this->get_outlet_id(), $this->get_counter_id() );
		if ( $payment->restaurant_checkout( $order_id ) ) {
			$this->response->set_response( true, '', $payment->get_order_details() );
		} else {
			$this->response->set_response( false, '' );
		}
		return $this->response->get_response();
	}
	/**
	 * The make order payment is generated by appsbd
	 *
	 * @param false $is_offline Is offline or not.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 * @throws \WC_Data_Exception Throw data exception.
	 */
	private function make_order_payment2( $is_offline = false ) {
		self::set_vite_pos_request();

		
		$order_id = $this->get_payload( 'order_id' );

		if ( ! POS_Settings::is_admin_user() ) {
			if ( ! current_user_can( 'pos-discount' ) && ( ! empty( $this->payload['discounts'] ) && is_array( $this->payload['discounts'] ) ) ) {
				$this->response->set_response( false, 'You do not have permission to give discount' );
				return $this->response->get_response();
			}
			$current_user  = get_user_by( 'id', $this->get_current_user_id() );
			$user_discount = Mapbd_Pos_Role::get_discount_percentage( $current_user );
			if ( ! $this->check_discount_limit( $this->payload['sub_total'], $user_discount, $this->payload['discounts'] ) ) {
				$this->response->set_response( false, 'You can not give this much discount' );
				return $this->response->get_response();
			}
		}
		if ( ! current_user_can( 'pos-fee' ) && ( ! empty( $this->payload['fees'] ) && is_array( $this->payload['fees'] ) ) ) {
			$this->response->set_response( false, 'You do not have permission to have fees' );
			return $this->response->get_response();
		}
		$outlet_obj   = $this->get_outlet_obj();
		$given_amount = (float) $this->get_payload( 'given_amount', 0.0 );
		$grand_total  = (float) $this->get_payload( 'grand_total', 0.0 );

		
		if ( ! empty( $order_id ) ) {
			$order = new \WC_Order( $order_id );			$stat  = $order->get_status();
			if ( ! empty( $order ) && 'vt_served' == $stat ) {
				$billing_address = array(
					'first_name' => $outlet_obj->name,
					'last_name'  => '',
					'email'      => $outlet_obj->email,
					'phone'      => $outlet_obj->phone,
					'address_1'  => 'Y' == $outlet_obj->main_branch ? 'Main Branch' : '',
					'city'       => $outlet_obj->city,
					'state'      => $outlet_obj->state,
					'postcode'   => $outlet_obj->zip_code,
					'country'    => $outlet_obj->country,
				);
				$customer_id     = $order->get_customer_id();
				if ( empty( $customer_id ) ) {
					$customer_id = $this->get_payload( 'customer', POS_Settings::get_module_option( 'pos_customer', null ) );
					$order->set_customer_id( $customer_id );
					/**
					 * Its for check is there any change before process
					 *
					 * @param $billing_address Object
					 * @param $order \WC_Order Object
					 * @param $order_arg customer data
					 * @since 1.0
					 */
					$billing_address = apply_filters( 'vitepos/filter/billing-address', $billing_address, $order, $customer_id );
										$order->set_address( $billing_address, 'billing' );
				}

				
				$total_amount = 0.0;
				$total_tax    = 0.0;

				$order->calculate_totals( true );
				$total_amount = $order->get_subtotal();
								$fee_total = 0.0;
				if ( ! empty( $this->payload['fees'] ) && is_array( $this->payload['fees'] ) ) {
					foreach ( $this->payload['fees'] as $item ) {
						if ( ! empty( $item['type'] ) && ! empty( $item['val'] ) ) {
							$item_val = floatval( $item['val'] );
							$title    = POS_Settings::get_module_instance()->__( 'Fee' );
							if ( $item_val > 0 ) {
								if ( strtoupper( $item['type'] ) == 'P' ) {
									$item_amount = $total_amount * ( $item_val / 100 );
									$title      .= '(' . $item['val'] . '%)';
								} else {
									$item_amount = $item_val;
								}
								$fee_total += $item_amount;
								vitepos_order_add_fee_on_order(
									$order,
									$title,
									$item_amount,
									array(
										'_vtp_cal_type' => $item['type'],
										'_vtp_cal_val'  => $item['val'],
									)
								);
							}
						}
					}
				}
				if ( ! $is_offline && ! POS_Settings::is_admin_user() ) {
					if ( ! $this->check_discount_limit( $total_amount, $user_discount, $this->payload['discounts'] ) ) {
						$order->delete( true );
						$this->response->set_response( false, 'You can not give this much discount' );

						return $this->response->get_response();
					}
				}
				$discount_total = 0.0;
				$discount       = 0.0;
				if ( ! empty( $this->payload['discounts'] ) && is_array( $this->payload['discounts'] ) ) {
					foreach ( $this->payload['discounts'] as $item ) {
						if ( ! empty( $item['type'] ) && ! empty( $item['val'] ) ) {
							$item_val = floatval( $item['val'] );
							$title    = POS_Settings::get_module_instance()->__( 'Discount' );
							if ( $item_val > 0 ) {
								if ( strtoupper( $item['type'] ) == 'P' ) {
									$item_amount = $total_amount * ( $item_val / 100 );
									$title      .= '(' . $item['val'] . '%)';
								} else {
									$item_amount = $item_val;
								}
								$discount += $item_amount;
								vitepos_order_add_discount_on_order(
									$order,
									$title,
									$item_amount,
									array(
										'_vtp_cal_type' => $item['type'],
										'_vtp_cal_val'  => $item['val'],
									)
								);
							}
						}
					}
				}

				try {
					if ( $total_tax > 0 ) {
						$order->set_cart_tax( $total_tax );
					}
				} catch ( Exception $e ) {
					$this->add_error( $e->getMessage() );
				}

				$order->calculate_totals( false );
				$rounding_factor = null;
				if ( $order->get_total() != $grand_total ) {
					try {
						$order->add_meta_data( '_vtp_miss_total', ( - 1 ) * ( $order->get_total() - $grand_total ) );
						$order->set_total( $grand_total );
					} catch ( Exception $e ) {
						$order->calculate_totals( false );
					}
				}

				$order->update_meta_data( '_vtp_fee_total', - $fee_total );
				$order->update_meta_data( '_vtp_discount_total', - $discount_total );

				$order->update_meta_data( '_vtp_order_note', $this->get_payload( 'note', '' ) );
				$order->update_meta_data( '_vtp_payment_note', $this->get_payload( 'payment_note', '' ) );
				$order->update_meta_data( '_vtp_payment_method', $this->get_payload( 'payment_method', '' ) );
				$order->add_meta_data( '_vtp_tendered_amount', $this->get_payload( 'given_amount', 0.0 ) );
				$change_amount = $this->get_payload( 'returned_amount', 0.0 );
				$order->add_meta_data( '_vtp_change_amount', $change_amount );
				$payment_list = $this->get_payload( 'payment_list', array() );
				$processed_by = $this->get_current_user_id();
				if ( ! $is_offline ) {
					$outlet_id  = $this->get_outlet_id();
					$counter_id = $this->get_counter_id();
				}
				$order->add_meta_data( '_vtp_payment_list', $payment_list );

				$cashdrawer = Mapbd_Pos_Cash_Drawer::get_by_counter( $outlet_id, $counter_id, $processed_by );

				$order->add_meta_data( '_vtp_processed_by', $processed_by );
				if ( ! empty( $cashdrawer ) ) {
					$order->add_meta_data( '_vtp_cash_drawer_id', $cashdrawer->id );
					$cash_found = false;
					foreach ( $payment_list as $payment ) {
						if ( 'C' == $payment['type'] ) {
							$cash_found = true;
							$amount     = doubleval( $payment['amount'] ) - doubleval( $change_amount );
							if ( $amount > 0.0 ) {
								Mapbd_Pos_Cash_Drawer::add_order(
									$this->get_current_user_id(),
									$amount,
									$order->get_id(),
									$outlet_id,
									$counter_id
								);
							} else {
								Mapbd_Pos_Cash_Drawer::add_order(
									$this->get_current_user_id(),
									doubleval( $payment['amount'] ),
									$order->get_id(),
									$outlet_id,
									$counter_id
								);
								Mapbd_Pos_Cash_Drawer::add_change_log(
									$this->get_current_user_id(),
									doubleval( $change_amount ),
									$order->get_id(),
									$outlet_id,
									$counter_id
								);
							}
						}
						Mapbd_Pos_Cash_Drawer_Types::AddLog(
							$cashdrawer->id,
							$this->get_current_user_id(),
							$order->get_id(),
							$payment['type'],
							$payment['amount']
						);
					}
					if ( $change_amount > 0 ) {
						if ( ! $cash_found ) {
							Mapbd_Pos_Cash_Drawer::add_change_log(
								$this->get_current_user_id(),
								$change_amount,
								$order->get_id(),
								$outlet_id,
								$counter_id
							);
						}
						Mapbd_Pos_Cash_Drawer_Types::AddLog(
							$cashdrawer->id,
							$this->get_current_user_id(),
							$order->get_id(),
							'_',
							$change_amount
						);
					}
				}
				if ( $order->update_status( 'completed', 'Imported order', true ) ) {
					$msg  = POS_Order::add_resto_order_msg( $order->get_id(), 'Order has been completed' );
					$data = POS_Order::get_from_woo_order_restro_by_id( $order_id, false, true );
					$this->response->set_response( true, 'Order successfully completed', $data );
				} else {
					$this->response->set_response( false, 'Failed', null );
				}
			} else {
				$this->response->set_response( false, 'Invalid order info ', null );
			}
		} else {
			$this->response->set_response( false, 'Empty order param ', null );
		}
		return $this->response->get_response();
	}

	/**
	 * The start preparing is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function start_preparing() {
						$order_id = $this->get_payload( 'order_id' );
		if ( ! empty( $order_id ) ) {
			$order = new \WC_Order( $order_id );
			if ( $order->get_status() == 'vt_in_kitchen' ) {
				if ( $order->update_status( 'vt_preparing', 'Order preparing in kitchen', true ) ) {
					$this->add_time_by_status( $order, 'vt_preparing' );
					$msg           = POS_Order::add_resto_order_msg( $order_id, 'Order preparing in kitchen' );
					$updated_order = POS_Order::get_from_woo_order_restro_by_id( $order_id, false, true );
					/**
					 * Its for check is there any change before process
					 *
					 * @since 2.0
					 */
					do_action('vitepos/action/send-order-push',$updated_order);
					$this->response->set_response( true, 'Order stated cooking', $updated_order );
					return $this->response->get_response();
				}
			}
		}
		$this->response->set_response( false, 'Cancel does not possible', null );

		return $this->response->get_response();
	}
	/**
	 * The start preparing is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function make_served() {
						$order_id = $this->get_payload( 'order_id' );
		if ( ! empty( $order_id ) ) {
			$order = new \WC_Order( $order_id );
			if ( $order->get_status() == 'vt_ready_to_srv' ) {
				if ( $order->update_status( 'vt_served', 'Order has been served', true ) ) {
					$this->add_time_by_status( $order, 'vt_served' );
					$msg           = POS_Order::add_resto_order_msg( $order_id, 'Order has been served' );
					$updated_order = POS_Order::get_from_woo_order_restro_by_id( $order_id, false, true );
					/**
					 * Its for check is there any change before process
					 *
					 * @since 2.0
					 */
					do_action('vitepos/action/send-order-push',$updated_order);
					$this->response->set_response( true, 'Order has been served', $updated_order );
					return $this->response->get_response();
				}
			}
		}
		$this->response->set_response( false, 'Cancel does not possible', null );

		return $this->response->get_response();
	}

	/**
	 * The complete preparing is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function complete_preparing() {
		$order_id = $this->get_payload( 'order_id' );
		if ( ! empty( $order_id ) ) {
			$order = new \WC_Order( $order_id );
			if ( $order->get_status() == 'vt_preparing' ) {
				if ( $order->update_status( 'vt_ready_to_srv', 'Order is ready to serve', true ) ) {					$this->add_time_by_status( $order, 'vt_ready_to_srv' );
					$msg           = POS_Order::add_resto_order_msg( $order_id, 'Order is ready to serve' );
					$updated_order = POS_Order::get_from_woo_order_restro_by_id( $order_id, false, true );
					/**
					 * Its for check is there any change before process
					 *
					 * @since 2.0
					 */
					do_action('vitepos/action/send-order-push',$updated_order);
					$this->response->set_response( true, 'Order is ready to serve', $updated_order );
					return $this->response->get_response();
				}
			} else {
				$this->response->set_response( false, 'You can not change this order to ready', null );
				return $this->response->get_response();
			}
		}
		$this->response->set_response( false, 'Order status change failed', null );
		return $this->response->get_response();
	}
	/**
	 * The complete preparing is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function cancel_request() {
		$order_id = $this->get_payload( 'order_id' );
		if ( ! empty( $order_id ) ) {
			$order = new \WC_Order( $order_id );
			if ( $order->get_status() == 'vt_preparing' ) {
				if ( $order->update_status( 'vt_cancel_request', 'Cancel requested', true ) ) {					$this->add_time_by_status( $order, 'vt_cancel_request' );
					$msg           = POS_Order::add_resto_order_msg( $order_id, 'Please cancel this order' );
					$updated_order = POS_Order::get_from_woo_order_restro_by_id( $order_id, false, true );
					/**
					 * Its for check is there any change before process
					 *
					 * @since 2.0
					 */
					do_action('vitepos/action/send-order-push',$updated_order);
					$this->response->set_response( true, 'Cancel request sent success', $updated_order );
					return $this->response->get_response();
				}
			} else {
				$this->response->set_response( false, 'Cancel request is not possible for this order', null );
				return $this->response->get_response();
			}
		}
		$this->response->set_response( false, 'Order status change failed', null );
		return $this->response->get_response();
	}
	/**
	 * The complete preparing is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function cancel_request_ans() {
		$order_id = $this->get_payload( 'order_id' );
		$answer   = strtoupper( $this->get_payload( 'ans', 'N' ) );
		if ( ! empty( $order_id ) && ! empty( $answer ) ) {
			$order = new \WC_Order( $order_id );
			if ( $order->get_status() == 'vt_cancel_request' ) {
				if ( 'Y' == $answer ) {
					if ( $order->update_status( 'cancelled', 'Cancel requested accepted', true ) ) {						$this->add_time_by_status( $order, 'cancelled' );
						$msg           = POS_Order::add_resto_order_msg( $order_id, 'Cancel requested accepted' );
						$updated_order = POS_Order::get_from_woo_order_restro_by_id( $order_id, false, true );
						/**
						 * Its for check is there any change before process
						 *
						 * @since 2.0
						 */
						do_action('vitepos/action/send-order-push',$updated_order);
						$this->response->set_response( true, 'Cancel request sent success', $updated_order );
						return $this->response->get_response();
					}
				} else {
					if ( $order->update_status( 'vt_preparing', 'Cancel request denied', true ) ) {						update_post_meta( $order_id, '_vt_can_cancel', 'N' );
						$this->add_time_by_status( $order, 'vt_preparing' );
						$msg           = POS_Order::add_resto_order_msg( $order_id, 'Cancel is not possible' );
						$updated_order = POS_Order::get_from_woo_order_restro_by_id( $order_id, false, true );
						/**
						 * Its for check is there any change before process
						 *
						 * @since 2.0
						 */
						do_action('vitepos/action/send-order-push',$updated_order,'N');
						$this->response->set_response( true, 'Cancel request denied', $updated_order );
						return $this->response->get_response();
					}
				}
			} else {
				$this->response->set_response( false, 'Cancel request is not possible for this order', null );

				return $this->response->get_response();
			}
		}
		$this->response->set_response( false, 'Action failed try again', null );
		return $this->response->get_response();
	}
	/**
	 * The deny order is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function cancel_order() {
						$order_id = $this->get_payload( 'order_id' );
		if ( ! empty( $order_id ) ) {
			$order  = new \WC_Order( $order_id );
			$status = $order->get_status();
			if ( in_array( $status, array( 'vt_kitchen_deny', 'vt_in_kitchen' ) ) ) {
								if ( $order->update_status( 'cancelled', 'Order Cancel', true ) ) {					$this->add_time_by_status( $order, 'vt_kitchen_deny' );
					$msg           = POS_Order::add_resto_order_msg( $order_id, 'Order canceled' );
					$updated_order = POS_Order::get_from_woo_order_restro_by_id( $order_id, false, true );
					/**
					 * Its for check is there any change before process
					 *
					 * @since 2.0
					 */
					do_action('vitepos/action/send-order-push',$updated_order);
					$this->response->set_response( true, 'Order canceled successfully', $updated_order );
					return $this->response->get_response();
				}
			}
		}
		$this->response->set_response( false, 'Cancel does not possible', null );

		return $this->response->get_response();
	}
	/**
	 * The deny order is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function deny_order() {
		$order_id  = $this->get_payload( 'order_id' );
		$reason_id = $this->get_payload( 'reason_id' );
		if (empty($reason_id))
		{
			$this->response->set_response( false, 'Deny reason is required', null );
			return $this->response->get_response();
		}
		if ( ! empty( $order_id ) ) {
			$order = new \WC_Order( $order_id );
			if ( $order->update_status( 'vt_kitchen_deny', 'Deny from kitchen', true ) ) {
				$this->add_time_by_status( $order, 'vt_kitchen_deny' );
				$msg_obj = new Mapbd_Pos_Message();
				$msg_obj->id( $reason_id );
				if ( $msg_obj->select( 'msg' ) ) {
					$msg = POS_Order::add_resto_order_msg( $order_id, $msg_obj->msg );
				}
				$updated_order = POS_Order::get_from_woo_order_restro_by_id( $order_id, false, true );
				/**
				 * Its for check is there any change before process
				 *
				 * @since 2.0
				 */
				do_action('vitepos/action/send-order-push',$updated_order);
				$this->response->set_response( true, 'Order denied success', $updated_order );
				return $this->response->get_response();
			}
		}
		$this->response->set_response( false, 'Order deny failed', null );
		return $this->response->get_response();
	}
	/**
	 * The deny order is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function add_kitchen_msg() {
						$order_id = $this->get_payload( 'order_id', '' );
		$msg      = $this->get_payload( 'msg', '' );
		if ( ! empty( $order_id ) && ! empty( $msg ) ) {
			$msgs = POS_Order::add_resto_order_msg( $order_id, $msg );
			if ( false !== $msgs ) {
				$updated_order = POS_Order::get_from_woo_order_restro_by_id( $order_id, false, true );
				/**
				 * Its for check is there any change before process
				 *
				 * @since 2.0
				 */
				do_action('vitepos/action/send-order-push',$updated_order);
				$this->response->set_response( true, 'Successfully message added', $updated_order );
				return $this->response->get_response();
			}
		}
		$this->response->set_response( false, 'Message add failed', null );
		return $this->response->get_response();
	}

	/**
	 * The send to kitchen is generated by appsbd
	 *
	 * @param false $is_offline Its offline param.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function send_to_kitchen( $is_offline = false ) {
		self::set_vite_pos_request();
		$payment = new POS_Payment( $this->payload, $this->get_outlet_id(), $this->get_counter_id() );
		if ( $payment->send_to_kitchen() ) {
			$this->response->set_response( true, '', $payment->get_order_details() );
		} else {
			$this->response->set_response( false, '' );
		}
		return $this->response->get_response();
	}

	/**
	 * The check discount limit is generated by appsbd
	 *
	 * @param any   $subtotal Its subtotal param.
	 * @param any   $user_discount Its user discount param.
	 * @param array $discounts Its discount param.
	 *
	 * @return bool
	 */
	public function check_discount_limit( $subtotal, $user_discount, $discounts = array() ) {
		$user_max_discount = 0.00;
		if ( ! empty( $subtotal ) && $user_discount > 0 ) {
			$user_max_discount = $user_max_discount + ( floatval( $subtotal ) ) * ( floatval( $user_discount / 100 ) );
		}
		$discount_payload = 0.00;
		if ( $user_discount > 0 && ( ! empty( $discounts ) && is_array( $discounts ) ) ) {
			foreach ( $discounts as $item ) {
				if ( strtoupper( $item['type'] ) == 'P' ) {
					$discount_payload = $discount_payload + ( floatval( $subtotal ) ) * ( floatval( $item['val'] / 100 ) );
				} else {
					$discount_payload = $discount_payload + ( floatval( $item['val'] ) );
				}
			}
		}
		if ( $user_max_discount < $discount_payload ) {
			return false;
		}
		return true;
	}
	/**
	 * The order list is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function waiter_order_list() {
		$response_data = new API_Data_Response();
		$outlet_id     = $this->get_outlet_id();
		$order_by      = $this->get_current_user_id();
		$args          = array(
			'status'        => array( 'vt_in_kitchen', 'vt_preparing', 'vt_served', 'vt_kitchen_deny', 'vt_ready_to_srv', 'vt_cancel_request' ),
						'page'          => $this->get_payload( 'page', 1 ),
			'orderby'       => 'date',
			'order'         => 'DESC',
			'paginate'      => true,
			'vt_meta_query' => array(
				array(
					'key'     => '_is_vitepos',
					'value'   => 'Y',
					'compare' => '=',
				),
				array(
					'key'     => '_vtp_outlet_id',
					'value'   => $outlet_id,
					'compare' => '=',
				),
				array(
					'key'     => '_vtp_order_by',
					'value'   => $order_by,
					'compare' => '=',
				),
				array(
					'key'     => '_vtp_is_resto',
					'value'   => 'Y',
					'compare' => '=',
				),
			),
		);
		if ( ! POS_Settings::is_admin_user() && ! current_user_can( 'can-see-any-outlet-orders' ) ) {
			$outlets = get_user_meta( $this->get_current_user_id(), 'outlet_id', true );
			if ( is_array( $outlets ) ) {
				$args['vt_meta_query'][] = array(
					'key'     => '_vtp_outlet_id',
					'value'   => $outlets,
					'compare' => 'IN',
				);
			} else {
				$this->add_error( "You don't have permission to view details of this outlet" );
				$response_data->set_total_records( 0 );
				$this->response->set_response( false, '', $response_data );
				return $this->response->get_response();
			}
		}

			$src_props     = $this->get_payload( 'src_by', array() );
			$sort_by_props = $this->get_payload( 'sort_by', array() );
			POS_Order::order_search_props( $args, $src_props );
			POS_Order::order_sort_param( $sort_by_props, $args );
			$orders = wc_get_orders( $args );

			$orderlist = array();
		if ( ! empty( $orders->orders ) && is_array( $orders->orders ) ) {
			$is_with_items = $this->get_payload( 'with_items', 'N' ) == 'Y';
			foreach ( $orders->orders as $order ) {
				$order_data  = POS_Order::get_from_woo_order( $order, false, $is_with_items );
				$orderlist[] = $order_data;
			}
		}

			$response_data->limit = $this->get_payload( 'limit', 10 );
			$response_data->page  = $this->get_payload( 'page', 1 );
		if ( $response_data->set_total_records( $orders->total ) ) {
			$response_data->rowdata = $orderlist;
		}

			$this->response->set_response( true, 'Order found', $response_data );

			return $this->response;
	}

	/**
	 * The waiter order list is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function kitchen_order_list() {
		$response_data = new API_Data_Response();
		$args          = array(
			'status'        => array( 'vt_in_kitchen', 'vt_preparing', 'vt_served', 'vt_kitchen_deny', 'vt_ready_to_srv', 'vt_cancel_request' ),
			'limit'         => $this->get_payload( 'limit', 10 ),
			'page'          => $this->get_payload( 'page', 1 ),
			'orderby'       => 'date',
			'order'         => 'DESC',
			'paginate'      => true,
			'vt_meta_query' => array(
				array(
					'key'     => '_is_vitepos',
					'value'   => 'Y',
					'compare' => '=',
				),
				array(
					'key'     => '_vtp_outlet_id',
					'value'   => $this->get_outlet_id(),
					'compare' => '=',
				),
				array(
					'key'     => '_vtp_is_resto',
					'value'   => 'Y',
					'compare' => '=',
				),
			),
		);

		

		$src_props     = $this->get_payload( 'src_by', array() );
		$sort_by_props = $this->get_payload( 'sort_by', array() );
		POS_Order::order_search_props( $args, $src_props );
		POS_Order::order_sort_param( $sort_by_props, $args );
		$orders = wc_get_orders( $args );

		$orderlist = array();
		if ( ! empty( $orders->orders ) && is_array( $orders->orders ) ) {
			foreach ( $orders->orders as $order ) {
				$order_data  = POS_Order::get_from_woo_order( $order, false, true );
				$orderlist[] = $order_data;
			}
		}

		$response_data->limit = $this->get_payload( 'limit', 10 );
		$response_data->page  = $this->get_payload( 'page', 1 );
		if ( $response_data->set_total_records( $orders->total ) ) {
			$response_data->rowdata = $orderlist;
		}

		$this->response->set_response( true, 'Order found', $response_data );
		return $this->response;
	}

	/**
	 * The order list is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function online_order_list() {
		$response_data = new API_Data_Response();
		$args          = array(
			'limit'    => $this->get_payload( 'limit', 10 ),
			'page'     => $this->get_payload( 'page', 1 ),
			'orderby'  => 'date',
			'order'    => 'DESC',
			'paginate' => true,

		);
		$src_props     = $this->get_payload( 'src_by', array() );
		$sort_by_props = $this->get_payload( 'sort_by', array() );
		POS_Order::order_search_props( $args, $src_props );
		POS_Order::order_sort_param( $sort_by_props, $args );
		$args['vt_meta_query'][] = array(
			'key'     => '_is_vitepos',
			'value'   => 'Y',
			'compare' => 'NOT EXISTS',
		);
		$orders                  = wc_get_orders( $args );
		$orderlist               = array();
		if ( ! empty( $orders->orders ) && is_array( $orders->orders ) ) {
			foreach ( $orders->orders as $order ) {
				$order_data  = POS_Order::get_from_woo_order( $order );
				$orderlist[] = $order_data;
			}
		}

		$response_data->limit = $this->get_payload( 'limit', 10 );
		$response_data->page  = $this->get_payload( 'page', 1 );
		if ( $response_data->set_total_records( $orders->total ) ) {
			$response_data->rowdata = $orderlist;
		}

		$this->response->set_response( true, 'Order found', $response_data );
		return $this->response;
	}

	/**
	 * The order list is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function served_list() {
		$response_data = new API_Data_Response();
		$outlet_id     = $this->get_outlet_id();
		$order_by      = $this->get_current_user_id();
		$args          = array(
			'status'        => array( 'vt_in_kitchen', 'vt_preparing' ),
			'limit'         => $this->get_payload( 'limit', 10 ),
			'page'          => $this->get_payload( 'page', 1 ),
			'orderby'       => 'date',
			'order'         => 'DESC',
			'paginate'      => true,
			'vt_meta_query' => array(
				array(
					'key'     => '_is_vitepos',
					'value'   => 'Y',
					'compare' => '=',
				),
				array(
					'key'     => '_vtp_outlet_id',
					'value'   => $outlet_id,
					'compare' => '=',
				),
				array(
					'key'     => '_vtp_order_by',
					'value'   => $order_by,
					'compare' => '=',
				),
				array(
					'key'     => '_vtp_is_resto',
					'value'   => 'Y',
					'compare' => '=',
				),
			),
		);

		

		$src_props     = $this->get_payload( 'src_by', array() );
		$sort_by_props = $this->get_payload( 'sort_by', array() );
		POS_Order::order_search_props( $args, $src_props );
		POS_Order::order_sort_param( $sort_by_props, $args );
		$orders = wc_get_orders( $args );

		$orderlist = array();
		if ( ! empty( $orders->orders ) && is_array( $orders->orders ) ) {
			foreach ( $orders->orders as $order ) {
				$order_data = POS_Order::get_from_woo_order( $order );
				if ( true || $this->get_payload( 'with_items', 'N' ) == 'Y' ) {
										$order_data->items = array();
					POS_Order::set_items_to_order( $order_data, $order );

				}
				$orderlist[] = $order_data;
			}
		}

		$response_data->limit = $this->get_payload( 'limit', 10 );
		$response_data->page  = $this->get_payload( 'page', 1 );
		if ( $response_data->set_total_records( $orders->total ) ) {
			$response_data->rowdata = $orderlist;
		}

		$this->response->set_response( true, 'Order found', $response_data );

		return $this->response;
	}
	/**
	 * The order list is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function canned_messages() {
		$response_data = new API_Data_Response();
		$type          = $this->payload['type'];
		$mainobj       = new Mapbd_Pos_Message();
		
		$mainobj->msg_panel( "in ('A','{$type}')", true );
				$mainobj->status( 'A' );
		$response_data->rowdata = $mainobj->select_all_grid_data( '', 'created_at', 'DESC' );
		$this->response->set_response( true, 'Order found', $response_data->rowdata );
		return $this->response;
	}
	/**
	 * The order list is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function sync_order_list() {
		$response_data = new API_Data_Response();
		if(!POS_Settings::is_restaurant_mode()){
			$this->response->set_response( true, 'Order found', [] );
			return $this->response->get_response();
		}
		$args          = array(
			'status'        => array( 'vt_in_kitchen', 'vt_preparing', 'cancelled', 'vt_served', 'completed', 'vt_kitchen_deny', 'vt_ready_to_srv', 'vt_cancel_request','pending' ),
			'limit'         => $this->get_payload( 'limit', 1000 ),
			'page'          => $this->get_payload( 'page', 1 ),
			'orderby'       => 'date',
			'order'         => 'DESC',
			'paginate'      => true,
			'vt_meta_query' => array(
				array(
					'key'     => '_is_vitepos',
					'value'   => 'Y',
					'compare' => '=',
				),

				array(
					'key'     => '_vtp_is_resto',
					'value'   => 'Y',
					'compare' => '=',
				),
				array(
					'key'     => '_vtp_outlet_id',
					'value'   => $this->get_outlet_id(),
					'compare' => '=',
				),
			),
		);
		$src_props     = $this->get_payload( 'src_by', array() );
		$sort_by_props = $this->get_payload( 'sort_by', array() );
		POS_Order::order_search_props( $args, $src_props );
		POS_Order::order_sort_param( $sort_by_props, $args );
		$orders = wc_get_orders( $args );

		$orderlist = array();
		if ( ! empty( $orders->orders ) && is_array( $orders->orders ) ) {
			foreach ( $orders->orders as $order ) {
				$order_data  = POS_Order::get_from_woo_order( $order, false, true );
				$orderlist[] = $order_data;
			}
		}

		$response_data->limit = $this->get_payload( 'limit', 1000 );
		$response_data->page  = $this->get_payload( 'page', 1 );
		if ( $response_data->set_total_records( $orders->total ) ) {
			$response_data->rowdata = $orderlist;
		}

		$this->response->set_response( true, 'Order found', $response_data );
		return $this->response->get_response();
	}

	/**
	 * The order details is generated by appsbd
	 *
	 * @param any $data Its string.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function order_details( $data ) {
		if ( ! empty( $data['id'] ) ) {
			$id    = intval( $data['id'] );
			$order = wc_get_order( $id );
			if ( ! empty( $order ) ) {
				$order_data         = POS_Order::get_from_woo_order( $order, false, true );
				$order_data->status = $order->get_status();
				$this->response->set_response( true, 'Order Found', $order_data );
				return $this->response;
			} else {
				$this->response->set_response( false, 'Order is empty', null );
				return $this->response;
			}
		} else {
			$this->response->set_response( false, 'request id not found', null );
			return $this->response;
		}
	}
	/**
	 * The order details is generated by appsbd
	 *
	 * @param any $data Its string.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function cashier_details( $data ) {
		if ( ! empty( $data['id'] ) ) {
			$id    = intval( $data['id'] );
			$order = wc_get_order( $id );
			if ( ! empty( $order ) ) {
				$order_data         = POS_Order::get_from_woo_order( $order, false, true );
				$order_data->status = $order->get_status();
				$this->response->set_response( true, 'Order Found', $order_data );
				return $this->response;
			} else {
				$this->response->set_response( false, 'Order is empty', null );
				return $this->response;
			}
		} else {
			$this->response->set_response( false, 'request id not found', null );
			return $this->response;
		}
	}
	/**
	 * The order details is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function change_status() {
		$id    = intval( $this->get_payload( 'id' ) );
		$order = new \WC_Order( $id );
		if ( $order ) {

			$processed_by = get_current_user_id();
			$outlet_obj   = $this->get_outlet_obj();
			$order->add_order_note( 'Order completed from ' . "{$outlet_obj->name}" );
			$order->update_meta_data( '_vtp_processed_by', $processed_by );
			$order->update_meta_data( '_vtp_outlet_id', $this->get_outlet_id() );
			if ( $order->update_status( $this->get_payload( 'status' ) ) ) {
				$data               = new \stdClass();
				$order_data         = POS_Order::get_from_woo_order_details( $order );
				$data->processed_by = $order_data->processed_by;
				$data->outlet_info  = $order_data->outlet_info;
				/**
				 * Its for check is there any change before process
				 *
				 * @since 2.0
				 */
				do_action('vitepos/action/send-order-push',$order_data);
				$this->response->set_response( true, 'Updated Successfully', $data );
				return $this->response;
			} else {
				$this->response->set_response( false, 'Not updated', null );
				return $this->response;
			}
		} else {
			$this->response->set_response( false, 'Not order found', null );
			return $this->response;
		}
	}
	/**
	 * The order details is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function change_order_table() {
		$id    = intval( $this->get_payload( 'order_id' ) );
		$order = new \WC_Order( $id );
		if ( $order ) {
			$tables=$this->get_payload('table_id',null);
			$person = $this->get_payload('persons',0);
			if(!empty($tables)) {
				if ( update_post_meta($id,'_vtp_tables', $tables) && update_post_meta($id,'_vtp_persons', $person) ) {
					$msg           = POS_Order::add_resto_order_msg( $id, 'Table and person updated' );
					$order = new \WC_Order( $id );
					$order_data = POS_Order::get_from_woo_order_details( $order );
					/**
					 * Its for check is there any change before process
					 *
					 * @since 2.0
					 */
					do_action( 'vitepos/action/send-order-push', $order_data );
					$this->response->set_response( true, 'Updated Successfully', $order_data );

					return $this->response;
				} else {
					$this->response->set_response( false, 'Not updated', null );

					return $this->response;
				}
			}else{
				$this->response->set_response( false, 'Table is empty', null );
				return $this->response;
			}
		} else {
			$this->response->set_response( false, 'Not order found', null );
			return $this->response;
		}
	}

	/**
	 * The add time by status is generated by appsbd
	 *
	 * @param any $order Its order param.
	 * @param any $status Its status param.
	 *
	 * @return bool
	 */
	public function add_time_by_status( $order, $status ) {
		 $time_meta_key = '_vt_time_log';
		$time_logs      = $order->get_meta( $time_meta_key );
		if ( ! is_array( $time_logs ) ) {
				$time_logs = array();
		}
			$time_obj         = new \stdClass();
			$time_obj->status = $status;
			$time_obj->time   = gmdate( 'Y-m-d H:i:s' );
			$time_logs[]      = $time_obj;
		if ( update_post_meta( $order->get_id(), $time_meta_key, $time_logs ) ) {
			return true;
		}
		return false;
	}
}
