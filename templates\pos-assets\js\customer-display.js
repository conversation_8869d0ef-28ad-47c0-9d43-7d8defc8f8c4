/**
 * Customer Display JavaScript
 * Handles customer-facing order status display and queue management
 */

class CustomerDisplay {
    constructor() {
        this.orders = [];
        this.currentOrder = null;
        this.refreshInterval = null;
        this.autoScroll = customerConfig.autoScroll;
        this.showWelcomeMessage = customerConfig.showWelcomeMessage;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startClock();
        this.loadOrders();
        this.startAutoRefresh();

        if (this.showWelcomeMessage) {
            this.showWelcomeAnimation();
        }
    }

    setupEventListeners() {
        // Handle window focus/blur for auto-refresh
        window.addEventListener('focus', () => {
            this.loadOrders();
        });

        // Handle keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F5') {
                e.preventDefault();
                this.loadOrders();
            }
        });
    }

    async loadOrders() {
        try {
            const response = await fetch(customerConfig.urls.served_list, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    limit: 20,
                    page: 1
                })
            });

            const data = await response.json();

            if (data.success) {
                this.orders = data.data.orders || [];
                this.updateDisplay();
            } else {
                console.error('Failed to load orders:', data.message);
            }
        } catch (error) {
            console.error('Error loading orders:', error);
        }
    }

    updateDisplay() {
        this.updateCurrentOrder();
        this.updateStatusCards();
        this.updateQueue();
    }

    updateCurrentOrder() {
        const currentOrderContainer = document.getElementById('current-order-content');

        // Find the most recent order being prepared or ready
        const activeOrder = this.orders.find(order =>
            order.status === 'vt_preparing' || order.status === 'vt_ready_to_srv'
        );

        if (activeOrder && activeOrder !== this.currentOrder) {
            this.currentOrder = activeOrder;
            this.displayCurrentOrder(activeOrder);
            this.playNotificationSound();
        } else if (!activeOrder) {
            this.currentOrder = null;
            this.displayNoOrder();
        }
    }

    displayCurrentOrder(order) {
        const container = document.getElementById('current-order-content');

        container.innerHTML = `
            <div class="order-details">
                <div class="order-number">#${order.id}</div>

                <div class="order-items">
                    ${order.line_items.map(item => `
                        <div class="order-item">
                            <div class="item-name">${item.name}</div>
                            <div class="item-quantity">${item.quantity}</div>
                        </div>
                    `).join('')}
                </div>

                <div class="order-status-display">
                    <div class="status-badge ${this.getStatusClass(order.status)}">
                        <i class="fas ${this.getStatusIcon(order.status)}"></i>
                        ${this.getStatusText(order.status)}
                    </div>
                </div>

                ${order.total ? `
                    <div class="order-total">
                        المجموع: ${order.total} ريال
                    </div>
                ` : ''}
            </div>
        `;

        // Add animation
        container.classList.add('animate__animated', 'animate__fadeInUp');
        setTimeout(() => {
            container.classList.remove('animate__animated', 'animate__fadeInUp');
        }, 1000);
    }

    displayNoOrder() {
        const container = document.getElementById('current-order-content');
        container.innerHTML = `
            <div class="no-order-message">
                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                <p>لا يوجد طلب حالياً</p>
            </div>
        `;
    }

    updateStatusCards() {
        const preparingOrders = this.orders.filter(order => order.status === 'vt_preparing');
        const readyOrders = this.orders.filter(order => order.status === 'vt_ready_to_srv');
        const servedOrders = this.orders.filter(order => order.status === 'vt_served');

        this.updateOrderNumbers('preparing-orders', preparingOrders);
        this.updateOrderNumbers('ready-orders', readyOrders);
        this.updateOrderNumbers('served-orders', servedOrders.slice(0, 5)); // Show last 5 served orders
    }

    updateOrderNumbers(containerId, orders) {
        const container = document.getElementById(containerId);

        if (orders.length === 0) {
            container.innerHTML = '<p class="text-muted">لا توجد طلبات</p>';
            return;
        }

        container.innerHTML = orders.map(order => `
            <span class="order-number-badge ${this.shouldHighlight(order) ? 'highlight' : ''}"
                  data-order-id="${order.id}">
                #${order.id}
            </span>
        `).join('');
    }

    updateQueue() {
        const queueContainer = document.getElementById('queue-display');
        const queueOrders = this.orders
            .filter(order => ['vt_in_kitchen', 'vt_preparing', 'vt_ready_to_srv'].includes(order.status))
            .sort((a, b) => new Date(a.date_created) - new Date(b.date_created));

        if (queueOrders.length === 0) {
            queueContainer.innerHTML = `
                <div class="text-center text-muted p-4">
                    <i class="fas fa-list-ol fa-2x mb-2"></i>
                    <p>قائمة الانتظار فارغة</p>
                </div>
            `;
            return;
        }

        queueContainer.innerHTML = queueOrders.map((order, index) => `
            <div class="queue-item ${index === 0 ? 'current' : ''}" data-order-id="${order.id}">
                <div class="queue-order-number">#${order.id}</div>
                <div class="queue-status ${this.getStatusClass(order.status)}">
                    ${this.getStatusText(order.status)}
                </div>
            </div>
        `).join('');

        // Auto-scroll to show new items
        if (this.autoScroll) {
            queueContainer.scrollTop = queueContainer.scrollHeight;
        }
    }

    shouldHighlight(order) {
        // Highlight orders that are ready or just completed
        return order.status === 'vt_ready_to_srv' ||
               (order.status === 'vt_served' && this.isRecentlyServed(order));
    }

    isRecentlyServed(order) {
        const now = new Date();
        const served = new Date(order.date_modified || order.date_created);
        const diffInMinutes = (now - served) / (1000 * 60);
        return diffInMinutes < 5; // Highlight for 5 minutes after serving
    }

    getStatusClass(status) {
        switch (status) {
            case 'vt_in_kitchen': return 'status-waiting';
            case 'vt_preparing': return 'status-preparing';
            case 'vt_ready_to_srv': return 'status-ready';
            case 'vt_served': return 'status-served';
            default: return '';
        }
    }

    getStatusIcon(status) {
        switch (status) {
            case 'vt_in_kitchen': return 'fa-clock';
            case 'vt_preparing': return 'fa-fire';
            case 'vt_ready_to_srv': return 'fa-check-circle';
            case 'vt_served': return 'fa-utensils';
            default: return 'fa-question';
        }
    }

    getStatusText(status) {
        switch (status) {
            case 'vt_in_kitchen': return 'في الانتظار';
            case 'vt_preparing': return 'قيد التحضير';
            case 'vt_ready_to_srv': return 'جاهز للاستلام';
            case 'vt_served': return 'تم التقديم';
            default: return 'غير معروف';
        }
    }

    startClock() {
        const updateClock = () => {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const dateString = now.toLocaleDateString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            document.getElementById('current-time').innerHTML = `
                <div class="time">${timeString}</div>
                <div class="date" style="font-size: 0.9rem; opacity: 0.8;">${dateString}</div>
            `;
        };

        updateClock();
        setInterval(updateClock, 1000);
    }

    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.loadOrders();
        }, customerConfig.refreshInterval);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    showWelcomeAnimation() {
        // Add welcome animation classes
        const header = document.querySelector('.customer-header');
        header.classList.add('animate__animated', 'animate__fadeInDown');

        setTimeout(() => {
            const orderSection = document.querySelector('.current-order-section');
            orderSection.classList.add('animate__animated', 'animate__fadeInUp');
        }, 500);

        setTimeout(() => {
            const statusSection = document.querySelector('.status-section');
            statusSection.classList.add('animate__animated', 'animate__fadeInLeft');
        }, 1000);
    }

    playNotificationSound() {
        try {
            const audio = document.getElementById('notification-sound');
            if (audio) {
                audio.currentTime = 0;
                audio.play().catch(e => console.log('Could not play sound:', e));
            }
        } catch (error) {
            console.log('Sound playback error:', error);
        }
    }

    // Method to manually refresh (can be called from external sources)
    refresh() {
        this.loadOrders();
    }

    // Method to highlight specific order
    highlightOrder(orderId) {
        const orderElements = document.querySelectorAll(`[data-order-id="${orderId}"]`);
        orderElements.forEach(element => {
            element.classList.add('highlight');
            setTimeout(() => {
                element.classList.remove('highlight');
            }, 5000);
        });
    }

    // Method to show custom message
    showCustomMessage(message, duration = 5000) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'custom-message';
        messageDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            font-size: 1.5rem;
            text-align: center;
            z-index: 9999;
            animation: fadeInOut ${duration}ms ease-in-out;
        `;
        messageDiv.textContent = message;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
            messageDiv.remove();
        }, duration);
    }
}

// Initialize customer display when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.customerDisplay = new CustomerDisplay();

    // Set up POS integration if available
    if (window.posIntegration) {
        // Listen for new orders
        window.posIntegration.onOrderCreated((order) => {
            console.log('New order received in customer display:', order);
            window.customerDisplay.loadOrders(); // Refresh the display
        });

        // Listen for order updates
        window.posIntegration.onOrderUpdated((order) => {
            console.log('Order updated in customer display:', order);
            window.customerDisplay.loadOrders(); // Refresh the display
        });

        // Listen for status changes
        window.posIntegration.onOrderStatusChanged((data) => {
            console.log('Order status changed in customer display:', data);
            window.customerDisplay.loadOrders(); // Refresh the display

            if (data.newStatus === 'vt_ready_to_srv') {
                window.customerDisplay.showOrderReadyNotification(data.order);
            }
        });
    }
});

// Handle page visibility change
document.addEventListener('visibilitychange', () => {
    if (window.customerDisplay) {
        if (document.hidden) {
            window.customerDisplay.stopAutoRefresh();
        } else {
            window.customerDisplay.startAutoRefresh();
            window.customerDisplay.loadOrders();
        }
    }
});

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInOut {
        0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
        10% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        90% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    }

    .order-number-badge.highlight {
        animation: pulse 2s infinite;
    }

    .queue-item.current {
        animation: pulse 2s infinite;
    }

    .status-badge {
        padding: 1rem 2rem;
        border-radius: 25px;
        font-size: 1.2rem;
        font-weight: 700;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .status-badge.status-waiting {
        background: #ffc107;
        color: #212529;
    }

    .status-badge.status-preparing {
        background: #fd7e14;
        color: white;
    }

    .status-badge.status-ready {
        background: #28a745;
        color: white;
        animation: pulse 2s infinite;
    }

    .status-badge.status-served {
        background: #6c5ce7;
        color: white;
    }
`;
document.head.appendChild(style);
