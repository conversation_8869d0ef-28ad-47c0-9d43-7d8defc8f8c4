/**
 * POS Integration for Kitchen Display Screens
 * Handles real-time communication between POS and display screens
 */

class POSIntegration {
    constructor() {
        this.pusher = null;
        this.channel = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 3000;
        this.heartbeatInterval = null;
        this.lastHeartbeat = null;
        
        this.callbacks = {
            orderCreated: [],
            orderUpdated: [],
            orderStatusChanged: [],
            newMessage: [],
            connectionChanged: []
        };
        
        this.init();
    }

    init() {
        this.loadPusherConfig();
        this.setupEventListeners();
        this.startHeartbeat();
    }

    async loadPusherConfig() {
        try {
            const response = await fetch(vitePosBase + 'config/pusher-settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            const data = await response.json();
            
            if (data.success && data.data.pusher && data.data.pusher.is_enable === 'Y') {
                this.initializePusher(data.data.pusher);
            } else {
                console.log('Pusher not enabled, falling back to polling');
                this.startPolling();
            }
        } catch (error) {
            console.error('Failed to load Pusher config:', error);
            this.startPolling();
        }
    }

    initializePusher(config) {
        try {
            // Load Pusher library if not already loaded
            if (typeof Pusher === 'undefined') {
                this.loadPusherLibrary().then(() => {
                    this.connectPusher(config);
                });
            } else {
                this.connectPusher(config);
            }
        } catch (error) {
            console.error('Pusher initialization failed:', error);
            this.startPolling();
        }
    }

    loadPusherLibrary() {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://js.pusher.com/7.2/pusher.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    connectPusher(config) {
        try {
            this.pusher = new Pusher(config.pushser_key, {
                cluster: config.pushser_cluster,
                encrypted: true,
                authEndpoint: vitePosBase + 'config/pusher-auth',
                auth: {
                    headers: {
                        'X-WP-Nonce': window.wpApiSettings?.nonce || ''
                    }
                }
            });

            // Get outlet ID for channel subscription
            this.getOutletId().then(outletId => {
                const channelName = outletId ? `_vtpos_info` : '_vtpos_info';
                this.channel = this.pusher.subscribe(channelName);
                
                this.setupPusherEvents();
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.notifyConnectionChange(true);
            });

        } catch (error) {
            console.error('Pusher connection failed:', error);
            this.startPolling();
        }
    }

    setupPusherEvents() {
        if (!this.channel) return;

        // Listen for outlet-specific events
        this.getOutletId().then(outletId => {
            const eventName = outletId ? `vtoutlet_${outletId}` : 'vtoutlet';
            
            this.channel.bind(eventName, (data) => {
                this.handlePusherMessage(data);
            });
        });

        // Connection state events
        this.pusher.connection.bind('connected', () => {
            console.log('Pusher connected');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.notifyConnectionChange(true);
        });

        this.pusher.connection.bind('disconnected', () => {
            console.log('Pusher disconnected');
            this.isConnected = false;
            this.notifyConnectionChange(false);
            this.attemptReconnect();
        });

        this.pusher.connection.bind('error', (error) => {
            console.error('Pusher error:', error);
            this.isConnected = false;
            this.notifyConnectionChange(false);
        });
    }

    handlePusherMessage(data) {
        console.log('Received Pusher message:', data);
        
        if (!data || !data.data) return;

        const messageData = data.data;
        const messageType = data.t || 'unknown';

        switch (messageType) {
            case 'order_created':
                this.triggerCallbacks('orderCreated', messageData);
                break;
            case 'order_updated':
                this.triggerCallbacks('orderUpdated', messageData);
                break;
            case 'status_changed':
                this.triggerCallbacks('orderStatusChanged', messageData);
                break;
            case 'new_message':
                this.triggerCallbacks('newMessage', messageData);
                break;
            default:
                // Handle generic order updates
                if (messageData.id) {
                    this.triggerCallbacks('orderUpdated', messageData);
                }
        }
    }

    async getOutletId() {
        try {
            const response = await fetch(vitePosBase + 'config/current-outlet', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            const data = await response.json();
            return data.success ? data.data.outlet_id : null;
        } catch (error) {
            console.error('Failed to get outlet ID:', error);
            return null;
        }
    }

    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('Max reconnection attempts reached, falling back to polling');
            this.startPolling();
            return;
        }

        this.reconnectAttempts++;
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            if (this.pusher) {
                this.pusher.connect();
            }
        }, this.reconnectDelay * this.reconnectAttempts);
    }

    startPolling() {
        console.log('Starting polling mode');
        this.stopPolling(); // Stop any existing polling
        
        this.pollingInterval = setInterval(() => {
            this.pollForUpdates();
        }, 5000); // Poll every 5 seconds
    }

    stopPolling() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
    }

    async pollForUpdates() {
        try {
            const response = await fetch(vitePosBase + 'restaurant/kitchen-order-list', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    limit: 50,
                    page: 1,
                    last_update: this.lastUpdate || null
                })
            });

            const data = await response.json();
            
            if (data.success && data.data.orders) {
                // Check for new or updated orders
                this.checkForOrderUpdates(data.data.orders);
                this.lastUpdate = new Date().toISOString();
            }
        } catch (error) {
            console.error('Polling failed:', error);
        }
    }

    checkForOrderUpdates(orders) {
        if (!this.lastOrders) {
            this.lastOrders = orders;
            return;
        }

        const lastOrdersMap = new Map(this.lastOrders.map(o => [o.id, o]));
        
        orders.forEach(order => {
            const lastOrder = lastOrdersMap.get(order.id);
            
            if (!lastOrder) {
                // New order
                this.triggerCallbacks('orderCreated', order);
            } else if (lastOrder.status !== order.status || 
                      lastOrder.date_modified !== order.date_modified) {
                // Updated order
                this.triggerCallbacks('orderUpdated', order);
                
                if (lastOrder.status !== order.status) {
                    this.triggerCallbacks('orderStatusChanged', {
                        order: order,
                        oldStatus: lastOrder.status,
                        newStatus: order.status
                    });
                }
            }
        });

        this.lastOrders = orders;
    }

    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            this.sendHeartbeat();
        }, 30000); // Send heartbeat every 30 seconds
    }

    async sendHeartbeat() {
        try {
            const response = await fetch(vitePosBase + 'heartbit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            this.lastHeartbeat = new Date();
        } catch (error) {
            console.error('Heartbeat failed:', error);
        }
    }

    // Event subscription methods
    onOrderCreated(callback) {
        this.callbacks.orderCreated.push(callback);
    }

    onOrderUpdated(callback) {
        this.callbacks.orderUpdated.push(callback);
    }

    onOrderStatusChanged(callback) {
        this.callbacks.orderStatusChanged.push(callback);
    }

    onNewMessage(callback) {
        this.callbacks.newMessage.push(callback);
    }

    onConnectionChanged(callback) {
        this.callbacks.connectionChanged.push(callback);
    }

    triggerCallbacks(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in ${event} callback:`, error);
                }
            });
        }
    }

    notifyConnectionChange(isConnected) {
        this.triggerCallbacks('connectionChanged', { isConnected });
    }

    // Utility methods for displays
    playNotificationSound(type = 'default') {
        const soundConfig = VitePosDisplayConfig?.sounds?.[type];
        if (!soundConfig || !soundConfig.enabled) return;

        try {
            const audio = new Audio(`/wp-content/plugins/vitepos/templates/pos-assets/${soundConfig.file}`);
            audio.volume = soundConfig.volume || 0.7;
            audio.play().catch(e => console.log('Could not play sound:', e));
        } catch (error) {
            console.log('Sound playback error:', error);
        }
    }

    showNotification(title, message, type = 'info') {
        // Check if browser supports notifications
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, {
                body: message,
                icon: '/wp-content/plugins/vitepos/templates/pos-assets/logo.png',
                tag: 'vitepos-' + type
            });
        }
        
        // Also show in-app notification
        if (VitePosDisplayConfig?.utils?.showToast) {
            VitePosDisplayConfig.utils.showToast(message, type);
        }
    }

    requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }

    // Cleanup method
    destroy() {
        if (this.pusher) {
            this.pusher.disconnect();
        }
        
        this.stopPolling();
        
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }
    }

    // Status check method
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            connectionType: this.pusher ? 'pusher' : 'polling',
            lastHeartbeat: this.lastHeartbeat,
            reconnectAttempts: this.reconnectAttempts
        };
    }
}

// Global instance
window.posIntegration = null;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.posIntegration = new POSIntegration();
    
    // Request notification permission
    window.posIntegration.requestNotificationPermission();
    
    // Global event handlers for all displays
    window.posIntegration.onOrderCreated((order) => {
        console.log('New order created:', order);
        window.posIntegration.playNotificationSound('newOrder');
        window.posIntegration.showNotification(
            'طلب جديد',
            `طلب رقم #${order.id} تم إنشاؤه`,
            'info'
        );
    });

    window.posIntegration.onOrderStatusChanged((data) => {
        console.log('Order status changed:', data);
        
        if (data.newStatus === 'vt_ready_to_srv') {
            window.posIntegration.playNotificationSound('orderReady');
            window.posIntegration.showNotification(
                'طلب جاهز',
                `طلب رقم #${data.order.id} جاهز للتقديم`,
                'success'
            );
        }
    });

    window.posIntegration.onConnectionChanged((status) => {
        console.log('Connection status changed:', status);
        
        // Update connection indicator if it exists
        const indicator = document.getElementById('connection-status');
        if (indicator) {
            if (status.isConnected) {
                indicator.textContent = 'متصل';
                indicator.className = 'badge bg-success';
            } else {
                indicator.textContent = 'غير متصل';
                indicator.className = 'badge bg-danger';
            }
        }
    });
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (window.posIntegration) {
        window.posIntegration.destroy();
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = POSIntegration;
}
