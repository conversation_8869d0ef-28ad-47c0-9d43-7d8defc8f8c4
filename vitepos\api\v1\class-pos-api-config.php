<?php
/**
 * Its api for config
 *
 * @since: 12/07/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package VitePos\Api\V1
 */

namespace VitePos\Api\V1;

use VitePos\Libs\API_Base;
use Vitepos\Models\Mcustom_Page;
use VitePos\Modules\POS_Settings;

/**
 * Class Pos_Api_Config
 *
 * @package VitePos\Api\V1
 */
class Pos_Api_Config extends API_Base {

	/**
	 * The set api base is generated by appsbd
	 *
	 * @return mixed|string
	 */
	public function set_api_base() {
		return 'basic';
	}

	/**
	 * The routes is generated by appsbd
	 *
	 * @return mixed|void
	 */
	public function routes() {
		$this->register_rest_route( 'GET', 'settings', array( $this, 'basic_settings' ) );
		$this->register_rest_route( 'POST', 'add-custom-page', array( $this, 'add_custom_page' ) );
		$this->register_rest_route( 'POST', 'edit-custom-page', array( $this, 'edit_custom_page' ) );
		$this->register_rest_route( 'POST', 'delete-custom-page', array( $this, 'delete_custom_page' ) );
		$this->register_rest_route( 'GET', 'countries', array( $this, 'country_list' ) );
		$this->register_rest_route( 'GET', 'get-custom-page', array( $this, 'get_custom_page' ) );
	}
	/**
	 * The set route permission is generated by appsbd
	 *
	 * @param \VitePos\Libs\any $route Its string.
	 *
	 * @return bool
	 */
	public function set_route_permission( $route ) {
		if ( 'delete-custom-page' == $route || 'add-custom-page' == $route || 'edit-custom-page' == $route ) {
			return current_user_can( 'manage-page-style' );
		}
		if ( 'settings' == $route ) {
			return true;
		}
		return parent::set_route_permission( $route );
	}

	/**
	 * The basic settings is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function basic_settings() {
		$resdata           = new \stdClass();
		$resdata->settings = POS_Settings::get_settings();
		$this->response->set_response( true, '', $resdata );
		return $this->response->get_response();
	}

	/**
	 * The basic settings is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function country_list() {
		$this->response->set_response( true, '', POS_Settings::get_module_instance()->get_countries( true ) );
		return $this->response;
	}
	/**
	 * The basic settings is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function add_custom_page() {
		if ( Mcustom_Page::add_page( $this->payload ) ) {
			$data = Mcustom_Page::get_data();
			$this->response->set_response( true, 'Data Added', $data );
			return $this->response;
		} else {
			$data = Mcustom_Page::get_data();
			$this->response->set_response( false, 'Data not Added', $data );
			return $this->response;
		}
	}
	/**
	 * The basic settings is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function edit_custom_page() {
		if ( Mcustom_Page::edit_page( $this->payload['id'], $this->payload ) ) {
			$data = Mcustom_Page::get_data();
			$this->response->set_response( true, 'Data Updated', $data );
		} else {
			$this->add_error( 'Not Updated' );
			$this->response->set_response( false );
		}
		return $this->response;
	}
	/**
	 * The basic settings is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function delete_custom_page() {
		if ( Mcustom_Page::delete_page( $this->payload['id'] ) ) {
			$data = Mcustom_Page::get_data();
			$this->response->set_response( true, 'Deleted successfully', $data );
		} else {
			$data = Mcustom_Page::get_data();
			$this->response->set_response( false, 'Data delete failed', $data );
		}

		return $this->response;
	}
	/**
	 * The basic settings is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function get_custom_page() {
		$data = Mcustom_Page::get_data();
		$this->response->set_response( true, 'Data found', $data );
		return $this->response;
	}

}
