<?php
/**
 * Its used for Client Language
 *
 * @since: 21/09/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package VitePos\Libs
 */

namespace VitePos\Libs;

/**
 * Class WC_Stock_Manage
 *
 * @package VitePos\Libs
 */
class WC_Stock_Manage {
	/**
	 * WC_Stock_Manage constructor.
	 */
	public function __construct() {
		add_action( 'woocommerce_reduce_order_stock', array( $this, 'order_reduce_stock' ), 10, 1 );
		add_action( 'woocommerce_restore_order_stock', array( $this, 'order_restore_stock' ), 10, 1 );
			}

	/**
	 * The order reduce stock is generated by appsbd
	 *
	 * @param \WC_Order $order Its order object.
	 */
	public function order_reduce_stock(  $order ) {
		/**
		 * Its for check is there any change before process
		 *
		 * @since 2.0.2
		 */
		do_action('vitepos/action/wc-default-stock-log-add',$order,'R');
	}
	/**
	 * The order reduce stock is generated by appsbd
	 *
	 * @param \WC_Order $order Its order object.
	 */
	public function order_restore_stock(  $order ) {
		/**
		 * Its for check is there any change before process
		 *
		 * @since 2.0.2
		 */
		do_action('vitepos/action/wc-default-stock-log-add',$order,'I');
	}
}
