<?php
/**
 * POS Display API for Kitchen and Customer Screens
 * 
 * @package VitePos
 */

namespace VitePos\Api\V1;

use Appsbd\V1\libs\API_Response;
use VitePos\Libs\POS_Order;
use VitePos\Modules\POS_Settings;

/**
 * Class Pos_Display_Api
 */
class Pos_Display_Api extends \Appsbd\V1\Core\BaseApi {

    /**
     * Register API routes
     */
    public function routes() {
        $this->register_rest_route( 'POST', 'display/kitchen-orders', array( $this, 'get_kitchen_orders' ) );
        $this->register_rest_route( 'POST', 'display/customer-orders', array( $this, 'get_customer_orders' ) );
        $this->register_rest_route( 'POST', 'display/order-updates', array( $this, 'get_order_updates' ) );
        $this->register_rest_route( 'POST', 'display/send-notification', array( $this, 'send_display_notification' ) );
        $this->register_rest_route( 'POST', 'display/update-order-status', array( $this, 'update_order_status' ) );
        $this->register_rest_route( 'GET', 'display/connection-status', array( $this, 'get_connection_status' ) );
    }

    /**
     * Set route permissions
     */
    public function set_route_permission( $route ) {
        switch ( $route ) {
            case 'get_kitchen_orders':
            case 'get_customer_orders':
            case 'get_order_updates':
            case 'get_connection_status':
                return true; // Allow access for display screens
            case 'send_display_notification':
            case 'update_order_status':
                return POS_Settings::is_pos_user();
            default:
                return parent::set_route_permission( $route );
        }
    }

    /**
     * Get kitchen orders with real-time updates
     */
    public function get_kitchen_orders() {
        try {
            $limit = $this->get_payload( 'limit', 50 );
            $page = $this->get_payload( 'page', 1 );
            $last_update = $this->get_payload( 'last_update', null );
            $status_filter = $this->get_payload( 'status', 'all' );

            $args = array(
                'limit' => $limit,
                'page' => $page,
                'orderby' => 'date',
                'order' => 'DESC',
                'paginate' => true,
                'meta_query' => array(
                    array(
                        'key' => '_vtp_is_resto',
                        'value' => 'Y',
                        'compare' => '='
                    )
                )
            );

            // Filter by status if specified
            if ( $status_filter !== 'all' ) {
                $args['status'] = array( $status_filter );
            } else {
                $args['status'] = array( 'vt_in_kitchen', 'vt_preparing', 'vt_ready_to_srv' );
            }

            // Filter by last update time if provided
            if ( $last_update ) {
                $args['date_query'] = array(
                    array(
                        'column' => 'post_modified',
                        'after' => $last_update,
                        'inclusive' => false
                    )
                );
            }

            $orders = wc_get_orders( $args );
            $order_data = array();

            foreach ( $orders->orders as $order ) {
                $order_details = POS_Order::get_from_woo_order_restro_by_id( $order->get_id(), false, true );
                if ( $order_details ) {
                    // Add additional display-specific data
                    $order_details->time_elapsed = $this->calculate_time_elapsed( $order->get_date_created() );
                    $order_details->is_urgent = $order_details->time_elapsed > 30;
                    $order_details->priority = $this->get_order_priority( $order_details->time_elapsed );
                    $order_details->table_info = $this->get_table_info( $order );
                    $order_details->kitchen_notes = $this->get_kitchen_notes( $order->get_id() );
                    
                    $order_data[] = $order_details;
                }
            }

            $response_data = array(
                'orders' => $order_data,
                'total' => $orders->total,
                'pages' => $orders->max_num_pages,
                'current_page' => $page,
                'last_update' => current_time( 'mysql' ),
                'counts' => $this->get_order_counts()
            );

            $this->response->set_response( true, 'Orders retrieved successfully', $response_data );

        } catch ( \Exception $e ) {
            $this->response->set_response( false, 'Error retrieving orders: ' . $e->getMessage() );
        }

        return $this->response->get_response();
    }

    /**
     * Get customer display orders
     */
    public function get_customer_orders() {
        try {
            $limit = $this->get_payload( 'limit', 20 );
            $show_served = $this->get_payload( 'show_served', true );

            $statuses = array( 'vt_preparing', 'vt_ready_to_srv' );
            if ( $show_served ) {
                $statuses[] = 'vt_served';
            }

            $args = array(
                'limit' => $limit,
                'orderby' => 'date',
                'order' => 'DESC',
                'status' => $statuses,
                'meta_query' => array(
                    array(
                        'key' => '_vtp_is_resto',
                        'value' => 'Y',
                        'compare' => '='
                    )
                )
            );

            $orders = wc_get_orders( $args );
            $order_data = array();

            foreach ( $orders as $order ) {
                $order_details = POS_Order::get_from_woo_order_restro_by_id( $order->get_id(), false, true );
                if ( $order_details ) {
                    // Add customer display specific data
                    $order_details->display_number = $this->get_display_number( $order );
                    $order_details->estimated_time = $this->get_estimated_completion_time( $order );
                    $order_details->is_current = $this->is_current_order( $order );
                    
                    $order_data[] = $order_details;
                }
            }

            $response_data = array(
                'orders' => $order_data,
                'current_order' => $this->get_current_order(),
                'queue_info' => $this->get_queue_info(),
                'last_update' => current_time( 'mysql' )
            );

            $this->response->set_response( true, 'Customer orders retrieved successfully', $response_data );

        } catch ( \Exception $e ) {
            $this->response->set_response( false, 'Error retrieving customer orders: ' . $e->getMessage() );
        }

        return $this->response->get_response();
    }

    /**
     * Get order updates since last check
     */
    public function get_order_updates() {
        try {
            $last_check = $this->get_payload( 'last_check', null );
            $order_ids = $this->get_payload( 'order_ids', array() );

            if ( empty( $last_check ) ) {
                $this->response->set_response( false, 'Last check time is required' );
                return $this->response->get_response();
            }

            $args = array(
                'limit' => -1,
                'date_query' => array(
                    array(
                        'column' => 'post_modified',
                        'after' => $last_check,
                        'inclusive' => false
                    )
                ),
                'meta_query' => array(
                    array(
                        'key' => '_vtp_is_resto',
                        'value' => 'Y',
                        'compare' => '='
                    )
                )
            );

            // If specific order IDs provided, filter by them
            if ( !empty( $order_ids ) ) {
                $args['include'] = $order_ids;
            }

            $orders = wc_get_orders( $args );
            $updates = array();

            foreach ( $orders as $order ) {
                $order_details = POS_Order::get_from_woo_order_restro_by_id( $order->get_id(), false, true );
                if ( $order_details ) {
                    $updates[] = array(
                        'order_id' => $order->get_id(),
                        'status' => $order->get_status(),
                        'modified_date' => $order->get_date_modified()->format( 'Y-m-d H:i:s' ),
                        'data' => $order_details
                    );
                }
            }

            $response_data = array(
                'updates' => $updates,
                'check_time' => current_time( 'mysql' ),
                'has_updates' => !empty( $updates )
            );

            $this->response->set_response( true, 'Updates retrieved successfully', $response_data );

        } catch ( \Exception $e ) {
            $this->response->set_response( false, 'Error retrieving updates: ' . $e->getMessage() );
        }

        return $this->response->get_response();
    }

    /**
     * Send notification to display screens
     */
    public function send_display_notification() {
        try {
            $message = $this->get_payload( 'message', '' );
            $type = $this->get_payload( 'type', 'info' );
            $target_displays = $this->get_payload( 'displays', array( 'kitchen', 'customer', 'preparation' ) );
            $order_id = $this->get_payload( 'order_id', null );

            if ( empty( $message ) ) {
                $this->response->set_response( false, 'Message is required' );
                return $this->response->get_response();
            }

            $notification_data = array(
                'message' => $message,
                'type' => $type,
                'timestamp' => current_time( 'mysql' ),
                'order_id' => $order_id,
                'displays' => $target_displays
            );

            // Send via Pusher if available
            $this->send_pusher_notification( $notification_data );

            // Store notification for polling clients
            $this->store_notification( $notification_data );

            $this->response->set_response( true, 'Notification sent successfully', $notification_data );

        } catch ( \Exception $e ) {
            $this->response->set_response( false, 'Error sending notification: ' . $e->getMessage() );
        }

        return $this->response->get_response();
    }

    /**
     * Update order status from display screens
     */
    public function update_order_status() {
        try {
            $order_id = $this->get_payload( 'order_id' );
            $new_status = $this->get_payload( 'status' );
            $note = $this->get_payload( 'note', '' );

            if ( empty( $order_id ) || empty( $new_status ) ) {
                $this->response->set_response( false, 'Order ID and status are required' );
                return $this->response->get_response();
            }

            $order = wc_get_order( $order_id );
            if ( !$order ) {
                $this->response->set_response( false, 'Order not found' );
                return $this->response->get_response();
            }

            // Validate status transition
            if ( !$this->is_valid_status_transition( $order->get_status(), $new_status ) ) {
                $this->response->set_response( false, 'Invalid status transition' );
                return $this->response->get_response();
            }

            // Update order status
            $order->update_status( $new_status, $note );
            $order->update_meta_data( '_vtp_processed_by', get_current_user_id() );
            $order->save();

            // Add time tracking
            $this->add_time_by_status( $order, $new_status );

            // Get updated order details
            $updated_order = POS_Order::get_from_woo_order_restro_by_id( $order_id, false, true );

            // Send notification to displays
            $this->send_status_change_notification( $updated_order, $new_status );

            $this->response->set_response( true, 'Order status updated successfully', $updated_order );

        } catch ( \Exception $e ) {
            $this->response->set_response( false, 'Error updating order status: ' . $e->getMessage() );
        }

        return $this->response->get_response();
    }

    /**
     * Get connection status
     */
    public function get_connection_status() {
        $status = array(
            'server_time' => current_time( 'mysql' ),
            'pusher_enabled' => $this->is_pusher_enabled(),
            'api_version' => '1.0',
            'display_screens' => array(
                'kitchen' => true,
                'customer' => true,
                'preparation' => true
            )
        );

        $this->response->set_response( true, 'Connection status retrieved', $status );
        return $this->response->get_response();
    }

    /**
     * Helper methods
     */

    private function calculate_time_elapsed( $date_created ) {
        $now = new \DateTime();
        $created = new \DateTime( $date_created );
        $diff = $now->diff( $created );
        return ( $diff->h * 60 ) + $diff->i;
    }

    private function get_order_priority( $time_elapsed ) {
        if ( $time_elapsed > 45 ) return 'high';
        if ( $time_elapsed > 30 ) return 'medium';
        return 'low';
    }

    private function get_table_info( $order ) {
        $tables = $order->get_meta( '_vtp_tables' );
        if ( !empty( $tables ) && is_array( $tables ) ) {
            return implode( ', ', $tables );
        }
        return '';
    }

    private function get_kitchen_notes( $order_id ) {
        $notes = get_post_meta( $order_id, '_vtp_kitchen_notes', true );
        return !empty( $notes ) ? $notes : array();
    }

    private function get_order_counts() {
        $counts = array();
        $statuses = array( 'vt_in_kitchen', 'vt_preparing', 'vt_ready_to_srv', 'vt_served' );

        foreach ( $statuses as $status ) {
            $args = array(
                'limit' => -1,
                'status' => $status,
                'meta_query' => array(
                    array(
                        'key' => '_vtp_is_resto',
                        'value' => 'Y',
                        'compare' => '='
                    )
                )
            );
            $orders = wc_get_orders( $args );
            $counts[$status] = count( $orders );
        }

        return $counts;
    }

    private function get_display_number( $order ) {
        // Generate a display-friendly number for customers
        return str_pad( $order->get_id() % 1000, 3, '0', STR_PAD_LEFT );
    }

    private function get_estimated_completion_time( $order ) {
        // Calculate estimated completion time based on order items and current queue
        $base_time = 15; // Base 15 minutes
        $item_count = $order->get_item_count();
        $additional_time = $item_count * 2; // 2 minutes per item
        
        return $base_time + $additional_time;
    }

    private function is_current_order( $order ) {
        // Determine if this is the current order being prepared
        return $order->get_status() === 'vt_preparing';
    }

    private function get_current_order() {
        $args = array(
            'limit' => 1,
            'status' => 'vt_preparing',
            'orderby' => 'date',
            'order' => 'ASC',
            'meta_query' => array(
                array(
                    'key' => '_vtp_is_resto',
                    'value' => 'Y',
                    'compare' => '='
                )
            )
        );

        $orders = wc_get_orders( $args );
        if ( !empty( $orders ) ) {
            return POS_Order::get_from_woo_order_restro_by_id( $orders[0]->get_id(), false, true );
        }

        return null;
    }

    private function get_queue_info() {
        $queue_counts = $this->get_order_counts();
        return array(
            'waiting' => $queue_counts['vt_in_kitchen'] ?? 0,
            'preparing' => $queue_counts['vt_preparing'] ?? 0,
            'ready' => $queue_counts['vt_ready_to_srv'] ?? 0,
            'total_active' => ( $queue_counts['vt_in_kitchen'] ?? 0 ) + 
                            ( $queue_counts['vt_preparing'] ?? 0 ) + 
                            ( $queue_counts['vt_ready_to_srv'] ?? 0 )
        );
    }

    private function is_valid_status_transition( $current_status, $new_status ) {
        $valid_transitions = array(
            'vt_in_kitchen' => array( 'vt_preparing', 'cancelled' ),
            'vt_preparing' => array( 'vt_ready_to_srv', 'cancelled' ),
            'vt_ready_to_srv' => array( 'vt_served', 'cancelled' ),
            'vt_served' => array( 'completed' )
        );

        return isset( $valid_transitions[$current_status] ) && 
               in_array( $new_status, $valid_transitions[$current_status] );
    }

    private function add_time_by_status( $order, $status ) {
        $time_key = '_vtp_time_' . $status;
        $order->update_meta_data( $time_key, current_time( 'mysql' ) );
        $order->save();
    }

    private function send_pusher_notification( $data ) {
        $pos_settings = POS_Settings::get_module_instance();
        if ( method_exists( $pos_settings, 'send_push_message' ) ) {
            $pos_settings->send_push_message( $data, 'display_notification' );
        }
    }

    private function store_notification( $data ) {
        // Store notification in database for polling clients
        $notifications = get_option( 'vitepos_display_notifications', array() );
        $notifications[] = $data;
        
        // Keep only last 50 notifications
        if ( count( $notifications ) > 50 ) {
            $notifications = array_slice( $notifications, -50 );
        }
        
        update_option( 'vitepos_display_notifications', $notifications );
    }

    private function send_status_change_notification( $order, $new_status ) {
        $message = '';
        switch ( $new_status ) {
            case 'vt_preparing':
                $message = "طلب رقم #{$order->id} بدأ التحضير";
                break;
            case 'vt_ready_to_srv':
                $message = "طلب رقم #{$order->id} جاهز للتقديم";
                break;
            case 'vt_served':
                $message = "طلب رقم #{$order->id} تم تقديمه";
                break;
        }

        if ( !empty( $message ) ) {
            $notification_data = array(
                'message' => $message,
                'type' => 'status_change',
                'order_id' => $order->id,
                'new_status' => $new_status,
                'order_data' => $order
            );

            $this->send_pusher_notification( $notification_data );
        }
    }

    private function is_pusher_enabled() {
        $pos_settings = POS_Settings::get_module_instance();
        $push_settings = $pos_settings->get_push_settings();
        return !empty( $push_settings->pusher ) && $push_settings->pusher['is_enable'] === 'Y';
    }
}
