<?php
/**
 * Its pos customer model.
 *
 * @since: 21/09/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package VitePos\Libs
 */

namespace VitePos\Libs;

use Appsbd\V1\Core\BaseModel;
use Vitepos\Models\Database\Mapbd_Pos_Role;
use VitePos\Modules\POS_Role;

/**
 * Class POS Customer
 *
 * @package VitePos\Libs
 */
class POS_Customer extends BaseModel {
	/**
	 * Its property id
	 *
	 * @var int Its integer.
	 */
	public $id;
	/**
	 * Its property first_name
	 *
	 * @var string Its string.
	 */
	public $first_name;
	/**
	 * Its property last_name
	 *
	 * @var string Its string.
	 */
	public $last_name;
	/**
	 * Its property username
	 *
	 * @var string Its string.
	 */
	public $username;
	/**
	 * Its property email
	 *
	 * @var string Its string.
	 */
	public $email;
	/**
	 * Its property contact_no
	 *
	 * @var string Its string.
	 */
	public $contact_no;
	/**
	 * Its property city
	 *
	 * @var string Its string.
	 */
	public $city;
	/**
	 * Its property street
	 *
	 * @var string Its string.
	 */
	public $street;
	/**
	 * Its property status
	 *
	 * @var bool Its bool.
	 */
	public $status;
	/**
	 * Its property postcode
	 *
	 * @var string Its string.
	 */
	public $postcode;
	/**
	 * Its property country
	 *
	 * @var string Its string.
	 */
	public $country;
	/**
	 * Its property state
	 *
	 * @var string Its string.
	 */
	public $state;
	/**
	 * Its property outlet_id
	 *
	 * @var int Its integer.
	 */
	public $outlet_id;
	/**
	 * Its property password
	 *
	 * @var string Its string.
	 */
	public $password;
	/**
	 * Its property role
	 *
	 * @var string Its string.
	 */
	public $role;
	/**
	 * Its property designation
	 *
	 * @var string Its string.
	 */
	public $designation;
	/**
	 * Its property website_url
	 *
	 * @var string Its string.
	 */
	public $website_url;
	/**
	 * Its property added_by
	 *
	 * @var string Its string.
	 */
	public $added_by;
	/**
	 * Its property added_by
	 *
	 * @var object Its Custom field object.
	 */
	public $custom_field;

	/**
	 * POS_Customer constructor.
	 */
	public function __construct() {
		parent::__construct();
		$this->set_validation();
	}

	/**
	 * The set validation is generated by appsbd
	 */
	public function set_validation() {
		$this->validations = array(
			'id'         => array(
				'Text' => 'Id',
				'Rule' => 'max_length[11]|integer',
			),
			'first_name' => array(
				'Text' => 'First Name',
				'Rule' => 'required|max_length[255]',
			),
			'last_name'  => array(
				'Text' => 'Last Name',
				'Rule' => 'required|max_length[255]',
			),
			'username'   => array(
				'Text' => 'Username',
				'Rule' => 'max_length[60]|required',
			),
			'email'      => array(
				'Text' => 'Status',
				'Rule' => 'required|max_length[100]',
			),
			'contact_no' => array(
				'Text' => 'Contact No',
				'Rule' => 'required|max_length[20]',
			),
			'password'   => array(
				'Text' => 'Password',
				'Rule' => 'max_length[100]',
			),
			'city'       => array(
				'Text' => 'City',
				'Rule' => 'max_length[100]',
			),
			'street'     => array(
				'Text' => 'Street',
				'Rule' => 'max_length[100]',
			),
			'postcode'   => array(
				'Text' => 'Post Code',
				'Rule' => 'max_length[20]',
			),
			'country'    => array(
				'Text' => 'Country',
				'Rule' => 'max_length[2]',
			),
			'state'      => array(
				'Text' => 'State',
				'Rule' => 'max_length[100]',
			),
			'role'       => array(
				'Text' => 'Role',
				'Rule' => 'max_length[100]',
			),
			'added_by'   => array(
				'Text' => 'Added By',
				'Rule' => 'max_length[11]|integer',
			),
		);
	}

	/**
	 * The contact exists is generated by appsbd.
	 *
	 * @param Mixed $contact_no Its contact number.
	 *
	 * @return bool
	 */
	public static function contact_exists( $contact_no ) {
		$user = get_users(
			array(
				'meta_key'   => 'billing_phone',
				'meta_value' => $contact_no,
			)
		);
		if ( $user ) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * The save is generated by appsbd
	 *
	 * @return bool Its bool.
	 */
	public function save() {
		if ( empty( $this->password ) ) {
			$this->password = wp_generate_password();
		}
		$this->status = false;
		$customer     = new \WC_Customer();
		if ( email_exists( $this->email ) ) {
			$this->add_error( 'Email already exist.' );

			return false;
		}
		$customer->set_email( $this->email );
		if ( username_exists( $this->username ) ) {
			$this->add_error( 'Username already exist.' );

			return false;
		} else {
			$customer->set_username( $this->username );
		}

		$customer->set_first_name( $this->first_name );
		$customer->set_last_name( $this->last_name );
		if ( self::contact_exists( $this->contact_no ) ) {
			$this->add_error( 'Contact no already exist.' );

			return false;
		} else {
			$customer->set_billing_phone( $this->contact_no );
		}
		$customer->set_billing_address( $this->street );
		$customer->add_meta_data( 'added_by', $this->added_by );
		$customer->add_meta_data( '_vtpos_outlet_id', $this->outlet_id );
		$customer->add_meta_data( 'custom_field', $this->custom_field );
		$customer->set_password( $this->password );
		$customer->set_billing_city( $this->city );
		$customer->set_billing_postcode( $this->postcode );
		$customer->set_billing_state( $this->state );
		$customer->set_billing_country( $this->country );
		$user_id = $customer->save();
		if ( empty( $this->id ) && ! empty( $user_id ) ) {
			$this->id = $user_id;
		}

		return ! empty( $user_id );
	}

	/**
	 * The update is generated by appsbd
	 *
	 * @param false $not_limit Its bool.
	 * @param bool  $is_show_msg Its bool.
	 * @param bool  $dont_process_id_where_not_set Its bool.
	 *
	 * @return bool Its bool.
	 */
	public function update( $not_limit = false, $is_show_msg = true, $dont_process_id_where_not_set = true ) {
		$customer = new \WC_Customer( $this->id );
		if ( $customer->user_email != $this->email && $this->email ) {
			if ( email_exists( $this->email ) == true ) {
				$this->add_error( 'This email already exist' );

				return false;
			}
			if ( $this->email ) {
				$customer->set_email( $this->email );
			}
		}
		if ( $customer->user_nicename != $this->username && $this->username ) {
			if ( username_exists( $this->username ) != null ) {
				$this->add_error( 'This username already exist' );

				return false;
			}
			if ( $this->username ) {
				$customer->set_username( $this->username );
			}
		}
		if ( $customer->get_billing_phone() != $this->contact_no && $this->contact_no ) {
			if ( self::contact_exists( $this->contact_no ) ) {
				$this->add_error( 'This mobile number is already exist' );

				return false;
			}
			if ( $this->contact_no ) {
				$customer->update_meta_data( 'billing_phone', $this->contact_no );
			}
		}

		if ( $this->first_name ) {
			$customer->set_first_name( $this->first_name );
		}
		if ( $this->city ) {
			$customer->set_city( $this->city );
		}
		if ( $this->last_name ) {
			$customer->set_last_name( $this->last_name );
		}
		if ( $this->postcode ) {
			$customer->set_billing_postcode( $this->postcode );
		}
		if ( $this->country ) {
			$customer->set_billing_country( $this->country );
		}
		if ( $this->state ) {
			$customer->set_billing_state( $this->state );
		}

		if ( $this->street ) {
			if ( $customer->meta_exists( 'billing_address_1' ) ) {
				$customer->update_meta_data( 'billing_address_1', $this->street );
			} else {
				$customer->add_meta_data( 'billing_address_1', $this->street );
			}
		}
		if ( $this->custom_field ) {
			if ( $customer->meta_exists( 'custom_field' ) ) {
				$customer->update_meta_data( 'custom_field', $this->custom_field );
			} else {
				$customer->add_meta_data( 'custom_field', $this->custom_field );
			}
		}
		if ( $this->outlet_id ) {
			if ( $customer->meta_exists( '_vtpos_outlet_id' ) ) {
				$customer->update_meta_data( '_vtpos_outlet_id', $this->outlet_id );
			} else {
				$customer->add_meta_data( '_vtpos_outlet_id', $this->outlet_id );
			}
		}
		$user_id = $customer->save();
		if ( empty( $this->id ) && ! empty( $user_id ) ) {
			$this->id = $user_id;
		}

		return ! empty( $user_id );
	}

	/**
	 * The save user is generated by appsbd
	 *
	 * @return bool Its bool.
	 */
	public function save_user() {
		$this->password = wp_generate_password();
		
		$user = new \WP_User();
		if ( self::contact_exists( $this->contact_no ) ) {
			$this->add_error( 'Contact no already exist.' );

			return false;
		}
		$user->first_name = $this->first_name;
		$user->last_name  = $this->last_name;
		if ( email_exists( $this->email ) ) {
			$this->add_error( 'Email already exist.' );
			return false;
		}
		$user->user_email = $this->email;
		if ( username_exists( $this->username ) ) {
			$this->add_error( 'Username already exist.' );

			return false;
		}
		$user->user_nicename = $this->username;
		$user->user_login    = $this->username;
		if ( is_string( $this->outlet_id ) ) {
			$this->outlet_id = unserialize( $this->outlet_id );
		}
		$user->user_status = true;
		$user->user_pass   = $this->password;
		$user_id           = wp_insert_user( $user );
		$user_get          = new \WP_User( $user_id );
		$user_get->set_role( $this->role );
		if ( self::contact_exists( $this->contact_no ) ) {
			$this->add_error( 'Contact no already exist.' );

			return false;
		} else {
			add_user_meta( $user_id, 'billing_phone', $this->contact_no );
		}
		add_user_meta( $user_id, 'custom_field', $this->custom_field );
		add_user_meta( $user_id, 'billing_address_1', $this->street );
		add_user_meta( $user_id, 'added_by', $this->added_by );
		add_user_meta( $user_id, 'outlet_id', $this->outlet_id );
		add_user_meta( $user_id, 'billing_city', $this->city );
		add_user_meta( $user_id, 'billing_state', $this->state );
		add_user_meta( $user_id, 'billing_country', $this->country );
		add_user_meta( $user_id, 'billing_postcode', $this->postcode );
		add_user_meta( $user_id, 'force_pw_change', 'Y' );
				if ( empty( $this->id ) && ! empty( $user_id ) ) {
			$this->id = $user_id;
		}

		return ! empty( $user_id );
	}

	/**
	 * The update user is generated by appsbd
	 *
	 * @param false $not_limit Its bool.
	 * @param bool  $is_show_msg Its bool.
	 * @param bool  $dont_process_id_where_not_set Its bool.
	 * @param bool  $is_update Its bool.
	 *
	 * @return bool Its bool.
	 */
	public function update_user( $not_limit = false, $is_show_msg = true, $dont_process_id_where_not_set = true, $is_update = true ) {
		$user = new \WP_User( $this->id );
		if ( $user->user_email != $this->email && $this->email ) {
			if ( email_exists( $this->email ) == true ) {
				$this->add_error( 'Email already exist' );

				return false;
			}
			if ( $this->email ) {
				$user->user_email = $this->email;
			}
		}
		if ( $user->user_nicename != $this->username && $this->username ) {
			if ( username_exists( $this->username ) != null ) {
				$this->add_error( 'Username already exist' );

				return false;
			}
			if ( $this->username ) {
				$user->user_nicename = $this->username;
			}
		}

		if ( $this->first_name ) {
			$user->first_name = $this->first_name;
		}
		if ( $this->last_name ) {
			$user->last_name = $this->last_name;
		}
		if ( !empty($this->role )) {
			if(POS_Role::is_exists_role($this->role)){
				$user->remove_role( $user->roles[0] );
				$user->add_role( $this->role );
			}else{
				$this->add_error( 'Role does not exists' );
				return false;
			}

		}
		if ( get_user_meta( $user->ID, 'billing_phone', true ) != $this->contact_no && ! empty( $this->contact_no ) ) {
			if ( self::contact_exists( $this->contact_no ) ) {
				$this->add_error( 'This mobile number is already exist' );
				return false;
			} else {
				if ( $this->contact_no ) {
					update_user_meta( $this->id, 'billing_phone', $this->contact_no );
				}
			}
		}
		$user_id = wp_update_user( $user );
		if ( is_string( $this->outlet_id ) ) {
			$this->outlet_id = unserialize( $this->outlet_id );
		}
		if ( $this->street ) {
			update_user_meta( $user_id, 'billing_address_1', $this->street );
		}

		if ( $this->outlet_id ) {
			if ( metadata_exists( 'user', $user_id, 'outlet_id' ) ) {
				update_user_meta( $user_id, 'outlet_id', $this->outlet_id );
			} else {
				add_user_meta( $user_id, 'outlet_id', $this->outlet_id );
			}
		}

		if ( $this->city ) {
			update_user_meta( $user_id, 'billing_city', $this->city );
		}
		if ( $this->custom_field ) {
			if ( metadata_exists( 'user', $user_id, 'custom_field' ) ) {
				update_user_meta( $user_id, 'custom_field', $this->custom_field );
			} else {
				add_user_meta( $user_id, 'custom_field', $this->custom_field );
			}
		}
		if ( $this->state ) {
			update_user_meta( $user_id, 'billing_state', $this->state );
		}
		if ( $this->postcode ) {
			update_user_meta( $user_id, 'billing_postcode', $this->postcode );
		}
		if ( $this->country ) {
			update_user_meta( $user_id, 'billing_country', $this->country );
		}

		if ( empty( $this->id ) && ! empty( $user_id ) ) {
			$this->id = $user_id;
		}

		return ! empty( $user_id );
	}
	/**
	 * The get user object is generated by appsbd
	 *
	 * @param any $user Its string.
	 *
	 * @return \stdClass
	 */
	public static function get_user_object( $user ) {
		$users_obj              = new \stdClass();
		$users_obj->id          = $user->ID;
		$users_obj->first_name  = $user->first_name;
		$users_obj->last_name   = $user->last_name;
		$users_obj->username    = $user->user_nicename;
		$users_obj->email       = $user->user_email;
		$users_obj->city        = get_user_meta( $user->ID, 'billing_city', true );
		$users_obj->state       = get_user_meta( $user->ID, 'billing_state', true );
		$users_obj->contact_no  = get_user_meta( $user->ID, 'billing_phone', true );
		$users_obj->street      = get_user_meta( $user->ID, 'billing_address_1', true );
		$users_obj->country     = get_user_meta( $user->ID, 'billing_country', true );
		$users_obj->postcode    = get_user_meta( $user->ID, 'billing_postcode', true );
		$users_obj->designation = get_user_meta( $user->ID, 'designation', true );
		$users_obj->outlet_id   = get_user_meta( $user->ID, 'outlet_id', true );
		$users_obj->role        = array_shift( $user->roles );
		if ( '' == $users_obj->outlet_id ) {
			$users_obj->outlet_id = array();
		}
		$users_obj->custom_field = (object) get_user_meta( $user->ID, 'custom_field', true );
		return $users_obj;
	}
	/**
	 * The delete is generated by appsbd
	 *
	 * @param false $not_limit Its bool.
	 * @param bool  $is_show_msg Its bool.
	 * @param bool  $dont_process_id_where_not_set Its bool.
	 *
	 * @return bool Its bool.
	 */
	public function delete( $not_limit = false, $is_show_msg = true, $dont_process_id_where_not_set = true ) {
		return false;
	}

	/**
	 * The set from wp user is generated by appsbd
	 *
	 * @param any $wp_user Its string.
	 */
	public function set_from_wp_user( $wp_user ) {
		$this->first_name = $wp_user->first_name;
		$this->last_name  = $wp_user->last_name;
		$this->username   = $wp_user->user_nicename;
		$this->city       = get_user_meta( $wp_user->ID, 'billing_city', true );
	}

	/**
	 * The user meta or search is generated by appsbd
	 *
	 * @param \WP_User_Query $q Its query object of user.
	 */
	public static function user_meta_or_search( $q ) {
		$search = $q->get( '_meta_or_search' );
		if ( ! empty( $search ) ) {
			add_filter(
				'get_meta_sql',
				function ( $sql ) use ( $search ) {
					$appdb = self::get_db_object();
										static $nr = 0;
					if ( 0 != $nr ++ ) {
						return $sql;
					}
					$where        = sprintf(
						' AND ( %s OR %s OR %s ) ',
						$appdb->prepare( "{$appdb->users}.user_nicename like '%%%s%%'", $search ),
						$appdb->prepare( "{$appdb->users}.user_email like '%%%s%%'", $search ),
						mb_substr( $sql['where'], 5, mb_strlen( $sql['where'] ) )
					);
					$sql['where'] = $where;
					return $sql;
				}
			);
		}
	}
	/**
	 * The set search param is generated by appsbd
	 *
	 * @param mixed $src_props Its request props.
	 * @param mixed $filter_param Its user search param.
	 */
	public static function set_search_param( $src_props, &$filter_param ) {
		foreach ( $src_props as $src_prop ) {
			if ( ! empty( $src_prop['prop'] ) && isset( $src_prop['val'] ) ) {
				if ( ! empty( $src_prop['val'] ) && is_string( $src_prop['val'] ) ) {
					$src_prop['val'] = trim( $src_prop['val'] );
				}
				if ( ! empty( $src_prop['prop'] ) && is_string( $src_prop['prop'] ) ) {
					$src_prop['prop'] = trim( $src_prop['prop'] );
				}
				if ( 'username' == $src_prop['prop'] ) {
					$filter_param['search']         = $src_prop['val'];
					$filter_param['search_columns'] = array( 'user_login', 'user_email' );

				} elseif ( '*' == $src_prop['prop'] ) {
					add_action( 'pre_get_users', '\VitePos\Libs\POS_Customer::user_meta_or_search', 9999 );
					$filter_param['_meta_or_search'] = esc_attr( $src_prop['val'] );
					$filter_param['search_columns']  = array( 'user_login', 'user_email', 'first_name', 'last_name' );

					$first_name = explode( ' ', $src_prop['val'] );
					if ( ! empty( $first_name[0] ) ) {
						$first_name = $first_name[0];
					} else {
						$first_name = $src_prop['val'];
					}

					$filter_param['meta_query'] = array(
						'relation' => 'OR',
						array(
							'key'     => 'billing_phone',
							'value'   => $src_prop['val'],
							'compare' => 'LIKE',
						),
						array(
							'key'     => 'first_name',
							'value'   => $first_name,
							'compare' => 'LIKE',
						),
						array(
							'key'     => 'last_name',
							'value'   => $src_prop['val'],
							'compare' => 'LIKE',
						),

					);
				} elseif ( 'email' == $src_prop['prop'] ) {
					$filter_param['search']         = $src_prop['val'];
					$filter_param['search_columns'] = array( 'user_email' );
				} elseif ( in_array( $src_prop['prop'], array( 'first_name', 'last_name' ) ) && ! empty( $src_prop['val'] ) ) {
					$filter_param['meta_key']     = $src_prop['prop'];
					$filter_param['meta_query'][] = array(
						'key'     => $src_prop['prop'],
						'value'   => $src_prop['val'],
						'compare' => 'LIKE',
					);
				} elseif ( 'outlet_id' == $src_prop['prop'] ) {
					$filter_param['meta_key']     = $src_prop['prop'];
					$filter_param['meta_query'][] = array(
						'key'     => $src_prop['prop'],
						'value'   => '"(' . $src_prop['val'] . ')"',
						'compare' => 'REGEXP',
					);
				}
			}
		}
	}

	/**
	 * The set sort param is generated by appsbd
	 *
	 * @param any $props Sorting property.
	 * @param any $sort_param Sorting param.
	 */
	public static function set_sort_param( $props, &$sort_param ) {
		foreach ( $props as $prop ) {
			if ( ! empty( $prop['prop'] ) ) {
				$prop['prop'] = strtolower( trim( $prop['prop'] ) );
				$prop['ord']  = strtolower( trim( $prop['ord'] ) );
				if ( in_array( $prop['ord'], array( 'asc', 'desc' ) ) ) {
						$sort_param['orderby'] = $prop['prop'];
						$sort_param['order']   = $prop['ord'];
				}
			}
		}
	}

}

