<?php
/**
 * Its used for kernel.
 *
 * @since: 21/09/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package Appsbd\V1\Core
 */

namespace Appsbd\V1\Core;

use Appsbd\V1\libs\AppInput;



if ( ! class_exists( __NAMESPACE__ . '\Kernel' ) ) {


	/**
	 * Its class kernel.
	 *
	 * @package Appsbd\V1\Core
	 */
	abstract class Kernel {
		/**
		 * Its property appsbd global js.
		 *
		 * @var array Its array.
		 */
		public static $appsbd_global_js = array();
		/**
		 * Its property appsbd global css.
		 *
		 * @var array Its array.
		 */
		public static $appsbd_global_css = array();

		/**
		 * Its property error message.
		 *
		 * @var array Its array.
		 */
		protected static $error_message = array();
		/**
		 * Its property warning message.
		 *
		 * @var array Its array.
		 */
		protected static $warning_message = array();
		/**
		 * Its property debug message.
		 *
		 * @var array Its array.
		 */
		protected static $debug_message = array();
		/**
		 * Its property info message.
		 *
		 * @var array Its array.
		 */
		protected static $info_message = array();
		/**
		 * Its property _self.
		 *
		 * @var self Its self.
		 */
		private static $_self;
		/**
		 * Its property plugin file.
		 *
		 * @var string Its string.
		 */
		public $plugin_file;
		/**
		 * Its property plugin file.
		 *
		 * @var string Its string.
		 */
		public $text_domain;
		/**
		 * Its property plugin base.
		 *
		 * @var string Its string.
		 */
		public $plugin_base;
		/**
		 * Its property plugin version.
		 *
		 * @var int|mixed Its integer.
		 */
		public $plugin_version;
		/**
		 * Its property plugin base prefix.
		 *
		 * @var string Its string.
		 */
		public $plugin_base_prefix;
		/**
		 * Its property menu label.
		 *
		 * @var string Its string.
		 */
		public $menu_label;
		/**
		 * Its property menu icon.
		 *
		 * @var string Its string.
		 */
		public $menu_icon;
		/**
		 * Its property set app properties.
		 *
		 * @var string Its string.
		 */
		public static $set_app_properties;
		/**
		 * Its property module list.
		 *
		 * @var array Its array.
		 */
		public $module_list;
		/**
		 * Its property app global var.
		 *
		 * @var array Its array.
		 */
		private static $app_global_var = array();
		/**
		 * Its property is_demo_mode
		 *
		 * @var bool
		 */
		private $is_demo_mode = false;

		/**
		 * Its property is_develop_mode
		 *
		 * @var bool
		 */
		private static $is_develop_mode = false;

		/**
		 * Its kernel constructor.
		 *
		 * @param any $plugin_file Its plugin_file param.
		 */
		final public function __construct( $plugin_file ) {
			self::$_self[ static::class ] =&$this;
			$this->plugin_file            = $plugin_file;
			$this->set_plugin_data();
			$this->initialize();
			$this->plugin_base_prefix = strtoupper( preg_replace( '/[^a-z0-9]/i', '', $this->plugin_base ) );
			spl_autoload_register( array( $this, '_myautoload_method' ) );
			$this->_register_module();
			AppInput::sanitize_all_input_data();
		}

		/**
		 * The getInstance is generated by appsbd
		 *
		 * @return static
		 */
		public static function &get_instance() {
			return self::$_self[ static::class ];
		}

		/**
		 * The get_current_version is generated by appsbd
		 */
		protected function set_plugin_data() {
			if ( ! function_exists( 'get_plugin_data' ) ) {
				require_once ABSPATH . 'wp-admin/includes/plugin.php';
			}
			$data = get_plugin_data( $this->plugin_file );
			if ( isset( $data['Version'] ) ) {
				$this->plugin_version = $data['Version'];
			}
			if ( isset( $data['TextDomain'] ) ) {
				$this->text_domain = $data['TextDomain'];
			}
		}


		/**
		 * The is_main_option_page is generated by appsbd
		 *
		 * @return bool
		 */
		public static function is_main_option_page() {
			$server = AppInput::get_server_array();
			$file   = basename( $server['SCRIPT_FILENAME'] );
			if ( 'plugins.php' == $file ) {
				if ( empty( AppInput::request_value( 'page' ) ) ) {
					return true;
				}
			}

			return false;
		}


		/**
		 * The check_admin_page is generated by appsbd
		 *
		 * @return bool
		 */
		public function check_admin_page() {
			$page = AppInput::request_value( 'page' );
			$page = trim( $page );
			if ( ! empty( $page ) ) {
				if ( $page == $this->plugin_base ) {
					return true;
				}
				foreach ( $this->module_list as $module_object ) {
					if ( $module_object->is_page_check( $page ) ) {
						return true;
					}
				}
			}

			return false;

		}


		/**
		 * The add_style is generated by appsbd
		 *
		 * @param any    $style_id Its style_id param.
		 * @param string $style_file_name Its style_file_name param.
		 * @param false  $is_from_root Its is_from_root param.
		 * @param array  $deps Its deps param.
		 */
		public function add_style( $style_id, $style_file_name = '', $is_from_root = false, $deps = array() ) {
			if ( $is_from_root ) {
				$start = '/';
			} else {
				$start = '/assets/css/';
			}

			if ( ! empty( $style_file_name ) ) {
				self::register_style( $style_id, plugins_url( $start . $style_file_name, $this->plugin_file ), $deps, $this->plugin_version );
			} else {
				self::RegisternStyle( $style_id );
			}

		}

		/**
		 * The plugin path is generated by appsbd
		 *
		 * @param mixed $file Its file param.
		 *
		 * @return string
		 */
		public function get_plugin_path( $file ) {
			return dirname( $this->plugin_file ) . DIRECTORY_SEPARATOR . $file;
		}

		/**
		 * The get plugin url is generated by appsbd
		 *
		 * @param mixed $file Its file param.
		 *
		 * @return string
		 */
		public function get_plugin_url( $file ) {
			return plugins_url( $file, $this->plugin_file );
		}

		/**
		 * The get plugin url is generated by appsbd
		 *
		 * @param mixed $action_name Its action_name param.
		 *
		 * @return string
		 */
		public function get_plugin_ajax_url( $action_name ) {
			if ( ! empty( $action_name ) ) {
				$action_name = '-' . $action_name;
			}
			$action_name = $this->get_action_prefix() . $action_name;
			return wp_nonce_url( admin_url( 'admin-ajax.php' ) . '?action=' . $action_name );
		}
		/**
		 * The on_init is generated by appsbd
		 */
		public function on_init() {
			load_plugin_textdomain( $this->text_domain, false, basename( dirname( $this->plugin_file ) ) . '/languages/' );
		}
		/**
		 * The AddAjaxAction is generated by appsbd
		 *
		 * @param any $action_name Its action_name param.
		 * @param callable $function_to_add Its function_to_add param.
		 */
		public function add_ajax_action( $action_name, $function_to_add ) {
			if ( ! empty( $action_name ) ) {
				$action_name = '-' . $action_name;
			}
			$action_name = $this->get_action_prefix() . $action_name;
			add_action( 'wp_ajax_' . $action_name, $function_to_add );
		}


		/**
		 * The AddAjaxNoPrivAction is generated by appsbd
		 *
		 * @param any      $action_name Its action_name param.
		 * @param callable $function_to_add Its function_to_add param.
		 */
		public function add_ajax_no_priv_action( $action_name, $function_to_add ) {
			if ( ! empty( $action_name ) ) {
				$action_name = '-' . $action_name;
			}
			$action_name = $this->get_action_prefix() . $action_name;
			add_action( 'wp_ajax_nopriv_' . $action_name, $function_to_add );
		}


		/**
		 * The AddAjaxBothAction is generated by appsbd
		 *
		 * @param any $action_name Its action_name param.
		 * @param any $function_to_add Its function_to_add param.
		 */
		public function add_ajax_both_action( $action_name, $function_to_add ) {
			$this->add_ajax_action( $action_name, $function_to_add );
			$this->add_ajax_no_priv_action( $action_name, $function_to_add );
		}

		/**
		 * The __ is generated by appsbd
		 *
		 * @param any  $string Its string param.
		 * @param null $parameter Its parameter param.
		 * @param null $_ Its _ param.
		 *
		 * @return mixed
		 */
		public function __( $string, $parameter = null, $_ = null ) {
			$args = func_get_args();
			array_splice( $args, 1, 0, array( $this->text_domain ) );
			return call_user_func_array( '__', $args );
		}


		/**
		 * The add_script is generated by appsbd
		 *
		 * @param any $script_id Its script_id param.
		 * @param string $script_file_name Its script_file_name param.
		 * @param false $is_from_root Its is_from_root param.
		 * @param array $deps Its deps param.
		 * @param bool $in_footer
		 */
		public function add_script( $script_id, $script_file_name = '', $is_from_root = false, $deps = array() ,$in_footer=false) {
			if ( $is_from_root ) {
				$start = '/';
			} else {
				$start = '/assets/js/';
			}
			if ( ! empty( $script_file_name ) ) {
				self::register_script( $script_id, plugins_url( $start . $script_file_name, $this->plugin_file ), $deps, $this->plugin_version ,$in_footer);
			} else {
				self::register_script( $script_id, '' );
			}
		}


		/**
		 * The register_style is generated by appsbd
		 *
		 * @param any    $handle Its handle param.
		 * @param string $src Its src param.
		 * @param array  $deps Its deps param.
		 * @param false  $ver Its ver param.
		 * @param false  $in_footer Its in_footer param.
		 */
		public static function register_style( $handle, $src = '', $deps = array(), $ver = false, $in_footer = false ) {
			self::$appsbd_global_css[] = $handle;
			if ( ! empty( $src ) ) {
				wp_register_style( $handle, $src, $deps, $ver, $in_footer );
			}
			wp_enqueue_style( $handle );
		}


		/**
		 * The register_script is generated by appsbd
		 *
		 * @param any    $handle Its handle param.
		 * @param string $src Its src param.
		 * @param array  $deps Its deps param.
		 * @param false  $ver Its ver param.
		 * @param false  $in_footer Its in_footer param.
		 */
		public static function register_script( $handle, $src = '', $deps = array(), $ver = false, $in_footer = false ) {
			self::$appsbd_global_js[] = $handle;
			if ( ! empty( $src ) ) {
				wp_deregister_script( $handle );
				wp_register_script( $handle, $src, $deps, $ver, $in_footer );
			}
			wp_enqueue_script( $handle );
		}


		/**
		 * The plugin_url is generated by appsbd
		 *
		 * @param any $path Its path param.
		 *
		 * @return string
		 */
		public function plugin_url( $path ) {
			return plugins_url( $path, $this->plugin_file );
		}

		/**
		 * The on_admin_main_option_styles is generated by appsbd
		 */
		public function on_admin_main_option_styles() {

		}

		/**
		 * The on_admin_global_styles is generated by appsbd
		 */
		public function on_admin_global_styles() {

		}

		/**
		 * The on_admin_styles is generated by appsbd
		 */
		public function on_admin_styles() {

		}


		/**
		 * The wp_admin_check_default_css_script is generated by appsbd
		 *
		 * @param any $src Its src param.
		 *
		 * @return bool
		 */
		public function wp_admin_check_default_css_script( $src ) {

			if ( empty( $src ) || 1 == $src || preg_match( '/\/uilib|\/css\/all-css.css|\/wp-admin\/|\/wp-includes\/|\/plugins\/woocommerce\/assets\/|\/plugins\/elementor\/assets\/css\/admin/', $src ) ) {
				return true;
			}
			if ( empty( $src ) || 1 == $src || preg_match( '/\/plugins\/query-monitor\//', $src ) ) {
				return true;
			}

			return false;
		}

		/**
		 * The on_admin_scripts is generated by appsbd
		 */
		public function on_admin_scripts() {

		}

		/**
		 * The on_admin_main_option_scripts is generated by appsbd
		 */
		public function on_admin_main_option_scripts() {

		}

		/**
		 * The on_admin_global_scripts is generated by appsbd
		 */
		public function on_admin_global_scripts() {

		}

		/**
		 * The on_client_scripts is generated by appsbd
		 */
		public function on_client_scripts() {

		}

		/**
		 * The on_client_style is generated by appsbd
		 */
		public function on_client_style() {

		}

		/**
		 * The on_active is generated by appsbd
		 */
		final public function on_active() {
			foreach ( $this->module_list as $module_object ) {
				$module_object->on_table_create();
				$module_object->on_active();
			}
		}


		/**
		 * The on_deactive is generated by appsbd
		 *
		 * @return bool
		 */
		final public function on_deactivate() {
			foreach ( $this->module_list as $module_object ) {
				if ( $module_object->on_deactivate() ) {
					return true;
				}
			}
		}

		/**
		 * The initialize is generated by appsbd
		 *
		 * @return mixed
		 */
		abstract public function initialize();


		/**
		 * The __callStatic is generated by appsbd
		 *
		 * @param any $func Its func param.
		 * @param any $args Its args param.
		 *
		 * @return mixed|void
		 */
		public static function __callStatic( $func, $args ) {
			if ( isset( self::$set_app_properties[ $func ] ) ) {
				return call_user_func_array( self::$set_app_properties[ $func ], $args );
			}
			return;
		}


		/**
		 * The set_property is generated by appsbd
		 *
		 * @param any $name Its name param.
		 * @param any $value Its value param.
		 */
		public static function set_property( $name, $value ) {
			self::$set_app_properties[ $name ] = $value;
		}


		/**
		 * The _myautoload_method is generated by appsbd
		 *
		 * @param any $class_name_space Its class_name_space param.
		 */
		public function _myautoload_method( $class_name_space ) {
			$path           = plugin_dir_path( $this->plugin_file );
			$class          = basename( $class_name_space );
			$dir            = strtolower( dirname( $class_name_space ) );
			$filename       = realpath( $path . $dir . '/' . $class . '.php' );
			$filename_class = strtolower( $path . $dir . '/class-' . str_replace( '_', '-', $class ) . '.php' );
			if ( ! empty( $filename_class ) && file_exists( $filename_class ) ) {
				require_once $filename_class;
				return;
			}
			if ( ! empty( $filename ) && file_exists( $filename ) ) {
				require_once $filename;
				return;
			}
		}

		/**
		 * The register_modules is generated by appsbd
		 *
		 * @return mixed
		 */
		abstract public function register_modules();

		/**
		 * The _register_module is generated by appsbd
		 */
		final public function _register_module() {
			$this->register_modules();
			/**
			 * Its for register module.
			 *
			 * @since 1.0
			 */
			do_action( $this->plugin_base . '/register-module', $this );
		}


		/**
		 * The add_module is generated by appsbd
		 *
		 * @param any $module_class_name Its module_class_name param.
		 */
		public function add_module( $module_class_name ) {
			$this->module_list[] = new $module_class_name( $this->plugin_base, $this );
		}

		/**
		 * The start_plugin is generated by appsbd
		 */
		final public function start_plugin() {
			if(is_callable("start_vitepos")){
			    start_vitepos();
			}
		}


		/**
		 * The add_error is generated by appsbd
		 *
		 * @param any $msg Its msg param.
		 */
		public static function add_error( $msg ) {
			if(is_string($msg) && empty($msg)){
				return;
			}
			if ( ! is_string( $msg ) ) {
				$msg = print_r( $msg, true );
			}
			self::$error_message[] = $msg;
		}


		/**
		 * The add_warning is generated by appsbd
		 *
		 * @param any $msg Its msg param.
		 */
		public static function add_warning( $msg ) {
			if ( ! is_string( $msg ) ) {
				$msg = print_r( $msg, true );
			}
			self::$warning_message[] = $msg;
		}

		/**
		 * The add debug is generated by appsbd
		 *
		 * @param any $msg Its msg param.
		 */
		public static function add_debug( $msg ) {
			if ( ! is_string( $msg ) ) {
				$msg = print_r( $msg, true );
			}
			self::$debug_message[] = $msg;
		}


		/**
		 * The add_info is generated by appsbd
		 *
		 * @param any $msg Its msg param.
		 */
		public static function add_info( $msg ) {
			if(is_string($msg) && empty($msg)){
				return;
			}
			if ( ! is_string( $msg ) ) {
				$msg = print_r( $msg, true );
			}
			self::$info_message[] = $msg;
		}

		/**
		 * The get error is generated by appsbd
		 *
		 * @return array
		 */
		public static function get_error() {
			return self::$error_message;
		}

		/**
		 * The get info is generated by appsbd
		 *
		 * @return array
		 */
		public static function get_info() {
			return self::$info_message;
		}

		/**
		 * The get warning is generated by appsbd
		 *
		 * @return array
		 */
		public static function get_warning() {
			return self::$warning_message;
		}

		/**
		 * The get msg for api is generated by appsbd
		 *
		 * @return \stdClass
		 */
		public static function get_msg_for_api() {
			$msg          = new \stdClass();
			$msg->info    = self::$info_message;
			$msg->error   = self::$error_message;
			$msg->warning = self::$warning_message;
			if ( static::is_develop_mode() ) {
				$msg->debug = self::$debug_message;
			}
			return $msg;
		}
		/**
		 * The Add Admin Notice is generated by appsbd
		 *
		 * @param string $msg Its msg param.
		 */
		public function add_admin_notice( $msg ) {

		}

		/**
		 * The get action prefix is generated by appsbd
		 *
		 * @return string
		 */
		public function get_action_prefix() {
			return str_replace( '_', '-', strtolower( $this->plugin_base ) );
		}
		/**
		 * The Add Top Menu is generated by appsbd
		 *
		 * @param string   $title Its title param.
		 * @param string   $icon Its icon param.
		 * @param callable $func Its func param.
		 * @param string   $class Its class param.
		 * @param bool     $is_tab Its isTab param.
		 * @param array    $attr Its attr param.
		 */
		public function add_top_menu( $title, $icon, $func, $class = '', $is_tab = true, $attr = array() ) {
			$n        = new \stdClass();
			$n->title = $title;
			$n->func  = $func;
			$n->icon  = $icon;
			$n->class = $class;
			$n->istab = $is_tab;
			$n->attr  = '';
			if ( count( $attr ) > 0 ) {
				foreach ( $attr as $ke => $v ) {
					$n->attr .= ' ' . $ke . '="' . $v . '" ';
				}
			}

			$this->_topmenu[] = $n;
		}

		/**
		 * The is Developmode is generated by appsbd
		 *
		 * @return mixed
		 */
		public static function is_develop_mode() {
			if ( ! defined( 'WP_DEBUG' ) || ! WP_DEBUG ) {
				return false;
			}
			return self::$is_develop_mode;
		}
		/**
		 * The set development mode is generated by appsbd
		 *
		 * @param bool $status Its status param.
		 */
		public static function set_development_mode( bool $status ) {

			self::$is_develop_mode = $status;
		}
		/**
		 * The set demo mode is generated by appsbd
		 *
		 * @param bool $status Its status param.
		 */
		public function set_demo_mode( bool $status ) {
			$this->is_demo_mode = $status;
		}


		/**
		 * The Add app global var is generated by appsbd
		 *
		 * @param any $key Its key param.
		 * @param any $value Its value param.
		 */
		public function add_app_global_var( $key, $value ) {
			self::$app_global_var[ $key ] = $this->__( $value );
		}
	}
}
