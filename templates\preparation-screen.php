<?php
/**
 * Preparation Screen Template - Category-based preparation management
 *
 * @var \VitePos\Modules\POS_Settings $this
 * @package vitepos
 */
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="<?php echo esc_html( $this->get_pos_color_code() ); ?>">
    <link rel="icon" href="<?php echo esc_url( $this->get_favicon() ); ?>">
    <title>شاشة التحضير - Preparation Screen</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Common Display CSS -->
    <link href="<?php echo esc_url( $this->get_plugin_url( 'templates/pos-assets/css/display-common.css' ) ); ?>" rel="stylesheet">
    <!-- Preparation Screen CSS -->
    <link href="<?php echo esc_url( $this->get_plugin_url( 'templates/pos-assets/css/preparation-screen.css' ) ); ?>" rel="stylesheet">

    <script>
        var vitePosBase = "<?php echo esc_url( get_rest_url( null, 'vitepos/v1' ) ); ?>/";
        var preparationConfig = {
            refreshInterval: 4000, // 4 seconds
            categoryFilter: true,
            timerEnabled: true,
            urls: {
                kitchen_order_list: vitePosBase + "restaurant/kitchen-order-list",
                start_preparing: vitePosBase + "restaurant/start-preparing",
                complete_preparing: vitePosBase + "restaurant/complete-preparing",
                category_list: vitePosBase + "product/categories",
                order_details: vitePosBase + "order/details"
            }
        };
    </script>
</head>
<body class="preparation-screen-body">
    <div class="container-fluid h-100">
        <!-- Header -->
        <div class="row preparation-header">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center p-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-tasks fa-2x text-success me-3"></i>
                        <h2 class="mb-0">شاشة التحضير</h2>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="filter-controls me-3">
                            <select class="form-select" id="category-filter">
                                <option value="all">جميع الفئات</option>
                                <!-- Categories will be loaded here -->
                            </select>
                        </div>
                        <div class="timer-display me-3" id="shift-timer">
                            <i class="fas fa-clock"></i>
                            <span id="timer-display">00:00:00</span>
                        </div>
                        <button class="btn btn-outline-primary" id="refresh-btn">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Row -->
        <div class="row stats-section">
            <div class="col-md-3">
                <div class="stat-card pending">
                    <div class="stat-icon">
                        <i class="fas fa-hourglass-start"></i>
                    </div>
                    <div class="stat-info">
                        <h4 id="pending-count">0</h4>
                        <p>في الانتظار</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card preparing">
                    <div class="stat-icon">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="stat-info">
                        <h4 id="preparing-count">0</h4>
                        <p>قيد التحضير</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card ready">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h4 id="ready-count">0</h4>
                        <p>جاهز</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card completed">
                    <div class="stat-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="stat-info">
                        <h4 id="completed-count">0</h4>
                        <p>مكتمل اليوم</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Category Sections -->
        <div class="row category-sections">
            <div class="col-md-4">
                <div class="category-section pending-section">
                    <div class="section-header">
                        <h5><i class="fas fa-hourglass-start"></i> في الانتظار</h5>
                        <span class="badge bg-warning" id="pending-badge">0</span>
                    </div>
                    <div class="orders-container" id="pending-orders">
                        <!-- Pending orders will be displayed here -->
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="category-section preparing-section">
                    <div class="section-header">
                        <h5><i class="fas fa-fire"></i> قيد التحضير</h5>
                        <span class="badge bg-info" id="preparing-badge">0</span>
                    </div>
                    <div class="orders-container" id="preparing-orders">
                        <!-- Preparing orders will be displayed here -->
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="category-section ready-section">
                    <div class="section-header">
                        <h5><i class="fas fa-check-circle"></i> جاهز للتقديم</h5>
                        <span class="badge bg-success" id="ready-badge">0</span>
                    </div>
                    <div class="orders-container" id="ready-orders">
                        <!-- Ready orders will be displayed here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Card Template -->
    <template id="order-card-template">
        <div class="order-card" data-order-id="">
            <div class="order-header">
                <div class="order-number">
                    <strong>#<span class="order-id"></span></span>
                </div>
                <div class="order-time">
                    <i class="fas fa-clock"></i>
                    <span class="time-elapsed"></span>
                </div>
            </div>
            <div class="order-items">
                <!-- Order items will be populated here -->
            </div>
            <div class="order-actions">
                <!-- Action buttons will be populated based on status -->
            </div>
            <div class="order-category">
                <span class="category-badge"></span>
            </div>
        </div>
    </template>

    <!-- Order Details Modal -->
    <div class="modal fade" id="orderDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل الطلب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="order-details-content">
                    <!-- Order details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد العملية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="confirmation-message">
                    <!-- Confirmation message will be displayed here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="confirm-action">تأكيد</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo esc_url( $this->get_plugin_url( 'templates/pos-assets/js/display-config.js' ) ); ?>"></script>
    <script src="<?php echo esc_url( $this->get_plugin_url( 'templates/pos-assets/js/preparation-screen.js' ) ); ?>"></script>
</body>
</html>
