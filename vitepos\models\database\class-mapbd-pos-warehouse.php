<?php
/**
 * Pos Warehouse Database Model
 *
 * @package Vitepos\Models\Database
 */

namespace Vitepos\Models\Database;

use VitePos\Core\ViteposModel;

/**
 * Class Mapbd_pos_warehouse
 *
 * @properties id,name,email,phone,country,state,city,street,zip_code,status
 */
class Mapbd_Pos_Warehouse extends ViteposModel {
	/**
	 * Its property id
	 *
	 * @var int
	 */
	public $id;
	/**
	 * Its property name
	 *
	 * @var string
	 */
	public $name;
	/**
	 * Its property email
	 *
	 * @var string
	 */
	public $email;
	/**
	 * Its property phone
	 *
	 * @var int
	 */
	public $phone;
	/**
	 * Its property country
	 *
	 * @var string
	 */
	public $country;
	/**
	 * Its property state
	 *
	 * @var string
	 */
	public $state;
	/**
	 * Its property city
	 *
	 * @var string
	 */
	public $city;
	/**
	 * Its property street
	 *
	 * @var string
	 */
	public $street;
	/**
	 * Its property zip_code
	 *
	 * @var int
	 */
	public $zip_code;
	/**
	 * Its property wh_timezone
	 *
	 * @var Time
	 */
	public $wh_timezone;
	/**
	 * Its property main_branch
	 *
	 * @var string
	 */
	public $main_branch;
	/**
	 * Its property allowed_ip
	 *
	 * @var string
	 */
	public $allowed_ip;
	/**
	 * Its property status
	 *
	 * @var bool
	 */
	public $status;


	/**
	 * Mapbd_pos_warehouse constructor.
	 */
	public function __construct() {
		parent::__construct();
		$this->set_validation();
		$this->table_name     = 'apbd_pos_warehouse';
		$this->primary_key    = 'id';
		$this->unique_key     = array();
		$this->multi_key      = array();
		$this->auto_inc_field = array( 'id' );
		$this->app_base_name  = 'apbd-elite-pos';

	}


	/**
	 * The set validation is generated by appsbd
	 */
	public function set_validation() {
		$this->validations = array(
			'id'          => array(
				'Text' => 'Id',
				'Rule' => 'max_length[11]|integer',
			),
			'name'        => array(
				'Text' => 'Name',
				'Rule' => 'max_length[100]',
			),
			'email'       => array(
				'Text' => 'Email',
				'Rule' => 'max_length[150]|valid_email',
			),
			'phone'       => array(
				'Text' => 'Phone',
				'Rule' => 'max_length[150]',
			),
			'country'     => array(
				'Text' => 'Country',
				'Rule' => 'max_length[100]',
			),
			'state'       => array(
				'Text' => 'State',
				'Rule' => 'max_length[100]',
			),
			'city'        => array(
				'Text' => 'City',
				'Rule' => 'max_length[100]',
			),
			'street'      => array(
				'Text' => 'Street',
				'Rule' => 'max_length[100]',
			),
			'zip_code'    => array(
				'Text' => 'Zip Code',
				'Rule' => 'max_length[100]',
			),
			'wh_timezone' => array(
				'Text' => 'Timezone',
				'Rule' => 'max_length[50]',
			),
			'allowed_ip'  => array(
				'Text' => 'Allowed Ip',
				'Rule' => 'max_length[255]',
			),
			'main_branch' => array(
				'Text' => 'Main Branch',
				'Rule' => 'max_length[1]',
			),
			'status'      => array(
				'Text' => 'Status',
				'Rule' => 'max_length[1]',
			),

		);
	}

	/**
	 * The get property raw options is generated by appsbd
	 *
	 * @param \Appsbd\V1\Core\any $property Its string.
	 * @param false               $is_with_select Its bool.
	 *
	 * @return array|string[]
	 */
	public function get_property_raw_options( $property, $is_with_select = false ) {
		$return_obj = array();
		switch ( $property ) {
			case 'status':
				$return_obj = array(
					'A' => 'Active',
					'I' => 'Inactive',
				);
				break;
			case 'main_branch':
				$return_obj = array(
					'Y' => 'Yes',
					'N' => 'No',
				);
				break;
			default:
		}
		if ( $is_with_select ) {
			return array_merge( array( '' => 'Select' ), $return_obj );
		}
		return $return_obj;

	}

	/**
	 * The get property options color is generated by appsbd
	 *
	 * @param \Appsbd\V1\Core\any $property Its string.
	 *
	 * @return array|string[]
	 */
	public function get_property_options_color( $property ) {
		$return_obj = array();
		switch ( $property ) {
			case 'status':
				$return_obj = array(
					'A' => 'success',
					'I' => 'danger',
				);
				break;
			case 'main_branch':
				$return_obj = array(
					'Y' => 'success',
					'N' => 'danger',
				);
				break;
			default:
		}
		return $return_obj;

	}

	/**
	 * The get outlet details is generated by appsbd
	 *
	 * @param WP_user $user Its string.
	 *
	 * @param bool    $has_any Its a boolean.
	 *
	 * @return array|false
	 */
	public static function get_outlet_details( $user, $has_any = false ) {

		if ( $has_any || ( is_object( $user ) && ( $user instanceof \WP_User ) ) ) {

			if ( ! $has_any && ( empty( $user ) || empty( $user->roles ) ) ) {
				return array();
			}
			$has_multi_outlet_access = $has_any;
			if ( ! $has_multi_outlet_access && in_array( 'administrator', $user->roles ) ) {
				$has_multi_outlet_access = true;
			}

			$outlet_obj = new self();
			$outlet_obj->status( 'A' );
			$in_str = '';
			if ( ! $has_multi_outlet_access ) {
				$user_outlets = get_user_meta( $user->ID, 'outlet_id', true );
				if ( empty( $user_outlets ) ) {
					return array();
				}
				$user_outlets = array_map(
					function( $value ) {
						return intval( $value );
					},
					$user_outlets
				);
				$in_str       = '(' . implode( ',', $user_outlets ) . ')';
				$outlet_obj->id( "in {$in_str}", true );
			}

			$outlets = $outlet_obj->select_all_grid_data( 'id,name,country,state,city,street,zip_code,status' );
			if ( ! empty( $outlets ) ) {
				$cash_counter = new Mapbd_pos_counter();
				if ( ! $has_multi_outlet_access ) {
					$cash_counter->outlet_id( "in {$in_str}", true );
				}
				$cash_counters = $cash_counter->select_all_grid_data();
				$outlet_ids    = array();
				foreach ( $cash_counters as $cash_counter ) {
					$outlet_id = $cash_counter->outlet_id;
					if ( ! isset( $outlet_ids[ $outlet_id ] ) ) {
						$outlet_ids[ $outlet_id ] = array();
					}
					unset( $cash_counter->outlet_id );
					$outlet_ids[ $outlet_id ][] = $cash_counter;
				}
				foreach ( $outlets as &$outlet ) {
					$outlet->counters = ! empty( $outlet_ids[ $outlet->id ] ) ? $outlet_ids[ $outlet->id ] : array();
				}
			}
			return $outlets;
		}
		return array();
	}

	/**
	 * The transfer online to outlet is generated by appsbd
	 *
	 * @param mixed $outlet_id
	 */
	public static function transfer_online_to_outlet($outlet_id,&$stock_info=null){
		if(is_null($stock_info)){
			$stock_info=new \stdClass();
		}

		$outlet=new self();
		$stock_info->products=0;
		$stock_info->stocks=0;
		if(!empty($outlet_id)){
			$outlet->id($outlet_id);
			if($outlet->select()){
				$wp_postmeta= $outlet->db->prefix."postmeta";
				$product_items=$outlet->select_query("select post_id as product_id,meta_value as stock from {$wp_postmeta} where meta_key='_stock' and meta_value > 0");
				$user=wp_get_current_user();
				$ref_value='';
				if(!empty($user->ID)){
					$ref_value=$user->ID;
				}
				foreach ( $product_items as $item ) {
					$stock_info->products ++;
					$stock_info->stockts += (int)$item->stock;
					if ( self::increase_stock_for_outlet( $item->product_id, $outlet_id, $item->stock,
						"Transfer from woocommerce online", $ref_value, "OT" ) ) {
						update_post_meta( $item->product_id, "_stock", 0 );
					}
				}
				return true;
			}else{
				$outlet->add_error("No outlet found");
			}
		}else{
			$outlet->add_error("Outlet param missing");
		}
		return false;
	}
	/**
	 * The create db table is generated by appsbd
	 */
	public static function create_db_table() {
		$this_obj = new static();
		$table    = $this_obj->db->prefix . $this_obj->table_name;
		if ( $this_obj->db->get_var( "show tables like '{$table}'" ) != $table ) {
			$sql = "CREATE TABLE `{$table}` (
					  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
					  `name` char(100) NOT NULL DEFAULT '',
					  `email` char(150) NOT NULL DEFAULT '',
					  `phone` char(150) NOT NULL DEFAULT '',
					  `country` char(100) NOT NULL,
					  `state` char(100) NOT NULL DEFAULT '',
					  `city` char(100) NOT NULL DEFAULT '',
					  `street` char(100) NOT NULL DEFAULT '',
					  `zip_code` char(100) NOT NULL DEFAULT '',
					  `wh_timezone` char(50) NOT NULL,
					  `main_branch` char(1) NOT NULL DEFAULT 'N' COMMENT 'bool(Y=Yes,N=No)',
                      `allowed_ip` char(255) NOT NULL DEFAULT '',
					  `status` char(1) NOT NULL DEFAULT 'A' COMMENT 'bool(A=Active,I=Inactive)',
					  PRIMARY KEY (`id`)
					) ";
			require_once ABSPATH . 'wp-admin/includes/upgrade.php';
			dbDelta( $sql );
		}
	}

	/**
	 * The DeleteById is generated by appsbd
	 *
	 * @param any $id Its integer.
	 *
	 * @return bool
	 */
	public static function delete_by_id( $id ) {
		return self::delete_by_key_value( 'id', $id );
	}

	/**
	 * The increase stock for outlet is generated by appsbd
	 *
	 * @param any $product Its product param.
	 * @param any $outlet_id Its outlet id param.
	 * @param any $quantity Its quantity param.
	 * @param string $msg Its message param.
	 * @param string $ref_val Its ref value param.
	 * @param string $ref_type Its ref type param.
	 * @param bool $add_log
	 *
	 * @return bool
	 */
	public static function increase_stock_for_outlet( $product, $outlet_id, $quantity, $msg = '', $ref_val = '', $ref_type = '',$add_log=true ) {
		if ( is_string( $product ) || is_int( $product ) ) {
			$product_id = $product;
			$product    = wc_get_product( $product_id );
		}
		$obj = new self();
		if ( $product instanceof \WC_Product ) {
			$product_id       = $product->get_id();
			$outlet_stock_key = '_vt_stocks';
			if ( $product->meta_exists( $outlet_stock_key ) ) {
				$outlet_stocks = (array) $product->get_meta( $outlet_stock_key );
			} else {
				$outlet_stocks = array( $outlet_id => 0 );
			}
			if ( !is_array( $outlet_stocks ) ) {
				$outlet_stocks = array();
			}
			if ( ! isset( $outlet_stocks[ $outlet_id ] ) ) {
				$outlet_stocks[ $outlet_id ] = 0;
			}
			$pre_stock                   = $outlet_stocks[ $outlet_id ];
			$outlet_stocks[ $outlet_id ] = $outlet_stocks[ $outlet_id ] + $quantity;
			if ( update_post_meta( $product_id, $outlet_stock_key, $outlet_stocks ) ) {
				if ( empty( $msg ) ) {
					$msg = $obj->__( 'Stock increased' );
				}
				if($add_log) {
					Mapbd_Pos_Stock_Log::AddLog( 'I', $outlet_id, $product_id, $pre_stock, $quantity, $msg, $ref_val,
						$ref_type );
				}

				return true;
			} else {
				$obj->add_error( 'Failed to update stock' );
			}
		}

		return false;
	}

	/**
	 * The reduce stock for outlet is generated by appsbd
	 *
	 * @param any    $product its product param.
	 * @param any    $outlet_id its outlet id param.
	 * @param any    $quantity its quantity param.
	 * @param string $msg its message param.
	 * @param string $ref_val its ref value param.
	 * @param string $ref_type its red type param.
	 *
	 * @return bool
	 */
	public static function reduce_stock_for_outlet( $product, $outlet_id, $quantity, $msg = '', $ref_val = '', $ref_type = '' ,$is_offline=false) {
		if ( is_string( $product ) || is_int( $product ) ) {
			$product_id = $product;
			$product    = wc_get_product( $product_id );
		}
		$obj = new self();
		if ( $product instanceof \WC_Product ) {
			$product_id       = $product->get_id();
			$outlet_stock_key = '_vt_stocks';
			if ( $product->meta_exists( $outlet_stock_key ) ) {
				$outlet_stocks = (array) $product->get_meta( $outlet_stock_key );
			} else {
				$outlet_stocks = array( $outlet_id => 0 );
			}
			if ( ! is_array( $outlet_stocks ) ) {
				$outlet_stocks = array();
			}
			if ( ! isset( $outlet_stocks[ $outlet_id ] ) ) {
				$outlet_stocks[ $outlet_id ] = 0;
			}
			if(!$is_offline) {
				if ( $outlet_stocks[ $outlet_id ] < $quantity || $outlet_stocks[ $outlet_id ] <= 0 ) {
					$obj->add_error( 'Failed to update stock' );

					return false;
				}
			}
			$pre_stock                   = $outlet_stocks[ $outlet_id ];
			$outlet_stocks[ $outlet_id ] = $outlet_stocks[ $outlet_id ] - $quantity;
			if ( update_post_meta( $product->get_id(), $outlet_stock_key, $outlet_stocks ) ) {
				if ( empty( $msg ) ) {
					$msg = $obj->__( 'Stock reduced' );
				}
				if($is_offline){
					$msg.='(Offline)';
				}
				Mapbd_Pos_Stock_Log::AddLog(
					'O',
					$outlet_id,
					$product_id,
					$pre_stock,
					$quantity,
					$msg,
					$ref_val,
					$ref_type
				);
				return true;
			} else {
				$obj->add_error( 'Failed to update stock' );
			}
		}

		return false;
	}
}
