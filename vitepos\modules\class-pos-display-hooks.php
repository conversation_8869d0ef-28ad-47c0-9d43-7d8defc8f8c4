<?php
/**
 * POS Display Hooks for Kitchen and Customer Screens Integration
 * 
 * @package VitePos
 */

namespace VitePos\Modules;

use Appsbd\V1\Core\BaseModule;
use VitePos\Libs\POS_Order;

/**
 * Class POS_Display_Hooks
 */
class POS_Display_Hooks extends BaseModule {

    /**
     * Initialize hooks
     */
    public function initialize() {
        // Hook into order status changes
        add_action( 'woocommerce_order_status_changed', array( $this, 'on_order_status_changed' ), 10, 4 );
        
        // Hook into VitePos specific actions
        add_action( 'vitepos/action/send-order-push', array( $this, 'on_order_push' ), 10, 1 );
        
        // Hook into order creation
        add_action( 'woocommerce_new_order', array( $this, 'on_new_order' ), 10, 1 );
        
        // Hook into order updates
        add_action( 'woocommerce_update_order', array( $this, 'on_order_updated' ), 10, 1 );
        
        // Hook into payment completion
        add_action( 'woocommerce_payment_complete', array( $this, 'on_payment_complete' ), 10, 1 );
        
        // Hook into order notes
        add_action( 'woocommerce_order_note_added', array( $this, 'on_order_note_added' ), 10, 2 );
        
        // Custom VitePos hooks
        add_action( 'vitepos_order_sent_to_kitchen', array( $this, 'on_order_sent_to_kitchen' ), 10, 1 );
        add_action( 'vitepos_order_preparing_started', array( $this, 'on_order_preparing_started' ), 10, 1 );
        add_action( 'vitepos_order_ready_to_serve', array( $this, 'on_order_ready_to_serve' ), 10, 1 );
        add_action( 'vitepos_order_served', array( $this, 'on_order_served' ), 10, 1 );
        
        // AJAX hooks for real-time updates
        add_action( 'wp_ajax_vitepos_display_heartbeat', array( $this, 'handle_display_heartbeat' ) );
        add_action( 'wp_ajax_nopriv_vitepos_display_heartbeat', array( $this, 'handle_display_heartbeat' ) );
        
        // WebSocket/Pusher integration
        add_action( 'init', array( $this, 'init_pusher_integration' ) );
    }

    /**
     * Handle order status changes
     */
    public function on_order_status_changed( $order_id, $old_status, $new_status, $order ) {
        // Only handle restaurant orders
        if ( $order->get_meta( '_vtp_is_resto' ) !== 'Y' ) {
            return;
        }

        $order_data = POS_Order::get_from_woo_order_restro_by_id( $order_id, false, true );
        if ( !$order_data ) {
            return;
        }

        // Add status change timestamp
        $order->update_meta_data( '_vtp_status_changed_' . $new_status, current_time( 'mysql' ) );
        $order->save();

        // Prepare notification data
        $notification_data = array(
            'type' => 'status_change',
            'order_id' => $order_id,
            'old_status' => $old_status,
            'new_status' => $new_status,
            'order_data' => $order_data,
            'timestamp' => current_time( 'mysql' ),
            'message' => $this->get_status_change_message( $order_id, $old_status, $new_status )
        );

        // Send to displays
        $this->send_to_displays( $notification_data );

        // Trigger specific status hooks
        switch ( $new_status ) {
            case 'vt_in_kitchen':
                do_action( 'vitepos_order_sent_to_kitchen', $order_data );
                break;
            case 'vt_preparing':
                do_action( 'vitepos_order_preparing_started', $order_data );
                break;
            case 'vt_ready_to_srv':
                do_action( 'vitepos_order_ready_to_serve', $order_data );
                break;
            case 'vt_served':
                do_action( 'vitepos_order_served', $order_data );
                break;
        }
    }

    /**
     * Handle VitePos order push action
     */
    public function on_order_push( $order_data ) {
        if ( !$order_data ) {
            return;
        }

        $notification_data = array(
            'type' => 'order_update',
            'order_id' => $order_data->id,
            'order_data' => $order_data,
            'timestamp' => current_time( 'mysql' ),
            'message' => "تم تحديث طلب رقم #{$order_data->id}"
        );

        $this->send_to_displays( $notification_data );
    }

    /**
     * Handle new order creation
     */
    public function on_new_order( $order_id ) {
        $order = wc_get_order( $order_id );
        
        // Only handle restaurant orders
        if ( $order->get_meta( '_vtp_is_resto' ) !== 'Y' ) {
            return;
        }

        $order_data = POS_Order::get_from_woo_order_restro_by_id( $order_id, false, true );
        if ( !$order_data ) {
            return;
        }

        $notification_data = array(
            'type' => 'new_order',
            'order_id' => $order_id,
            'order_data' => $order_data,
            'timestamp' => current_time( 'mysql' ),
            'message' => "طلب جديد رقم #{$order_id}",
            'sound' => 'newOrder'
        );

        $this->send_to_displays( $notification_data );
    }

    /**
     * Handle order updates
     */
    public function on_order_updated( $order_id ) {
        $order = wc_get_order( $order_id );
        
        // Only handle restaurant orders
        if ( $order->get_meta( '_vtp_is_resto' ) !== 'Y' ) {
            return;
        }

        $order_data = POS_Order::get_from_woo_order_restro_by_id( $order_id, false, true );
        if ( !$order_data ) {
            return;
        }

        $notification_data = array(
            'type' => 'order_update',
            'order_id' => $order_id,
            'order_data' => $order_data,
            'timestamp' => current_time( 'mysql' ),
            'message' => "تم تحديث طلب رقم #{$order_id}"
        );

        $this->send_to_displays( $notification_data );
    }

    /**
     * Handle payment completion
     */
    public function on_payment_complete( $order_id ) {
        $order = wc_get_order( $order_id );
        
        // Only handle restaurant orders
        if ( $order->get_meta( '_vtp_is_resto' ) !== 'Y' ) {
            return;
        }

        $notification_data = array(
            'type' => 'payment_complete',
            'order_id' => $order_id,
            'timestamp' => current_time( 'mysql' ),
            'message' => "تم دفع طلب رقم #{$order_id}",
            'sound' => 'success'
        );

        $this->send_to_displays( $notification_data );
    }

    /**
     * Handle order note addition
     */
    public function on_order_note_added( $note_id, $order ) {
        // Only handle restaurant orders
        if ( $order->get_meta( '_vtp_is_resto' ) !== 'Y' ) {
            return;
        }

        $note = wc_get_order_note( $note_id );
        if ( !$note ) {
            return;
        }

        $notification_data = array(
            'type' => 'order_note',
            'order_id' => $order->get_id(),
            'note_id' => $note_id,
            'note' => $note,
            'timestamp' => current_time( 'mysql' ),
            'message' => "ملاحظة جديدة على طلب رقم #{$order->get_id()}"
        );

        $this->send_to_displays( $notification_data );
    }

    /**
     * Handle order sent to kitchen
     */
    public function on_order_sent_to_kitchen( $order_data ) {
        $notification_data = array(
            'type' => 'sent_to_kitchen',
            'order_id' => $order_data->id,
            'order_data' => $order_data,
            'timestamp' => current_time( 'mysql' ),
            'message' => "طلب رقم #{$order_data->id} تم إرساله للمطبخ",
            'sound' => 'newOrder',
            'priority' => 'high'
        );

        $this->send_to_displays( $notification_data, array( 'kitchen', 'preparation' ) );
    }

    /**
     * Handle order preparing started
     */
    public function on_order_preparing_started( $order_data ) {
        $notification_data = array(
            'type' => 'preparing_started',
            'order_id' => $order_data->id,
            'order_data' => $order_data,
            'timestamp' => current_time( 'mysql' ),
            'message' => "بدأ تحضير طلب رقم #{$order_data->id}",
            'sound' => 'success'
        );

        $this->send_to_displays( $notification_data );
    }

    /**
     * Handle order ready to serve
     */
    public function on_order_ready_to_serve( $order_data ) {
        $notification_data = array(
            'type' => 'ready_to_serve',
            'order_id' => $order_data->id,
            'order_data' => $order_data,
            'timestamp' => current_time( 'mysql' ),
            'message' => "طلب رقم #{$order_data->id} جاهز للتقديم",
            'sound' => 'orderReady',
            'priority' => 'high'
        );

        $this->send_to_displays( $notification_data );
    }

    /**
     * Handle order served
     */
    public function on_order_served( $order_data ) {
        $notification_data = array(
            'type' => 'order_served',
            'order_id' => $order_data->id,
            'order_data' => $order_data,
            'timestamp' => current_time( 'mysql' ),
            'message' => "تم تقديم طلب رقم #{$order_data->id}",
            'sound' => 'success'
        );

        $this->send_to_displays( $notification_data );
    }

    /**
     * Handle display heartbeat AJAX
     */
    public function handle_display_heartbeat() {
        $display_type = sanitize_text_field( $_POST['display_type'] ?? '' );
        $last_update = sanitize_text_field( $_POST['last_update'] ?? '' );

        $response = array(
            'success' => true,
            'timestamp' => current_time( 'mysql' ),
            'display_type' => $display_type,
            'has_updates' => false,
            'updates' => array()
        );

        // Check for updates since last heartbeat
        if ( !empty( $last_update ) ) {
            $updates = $this->get_updates_since( $last_update, $display_type );
            if ( !empty( $updates ) ) {
                $response['has_updates'] = true;
                $response['updates'] = $updates;
            }
        }

        wp_send_json( $response );
    }

    /**
     * Initialize Pusher integration
     */
    public function init_pusher_integration() {
        // Check if Pusher is enabled
        $pos_settings = POS_Settings::get_module_instance();
        $push_settings = $pos_settings->get_push_settings();
        
        if ( empty( $push_settings->pusher ) || $push_settings->pusher['is_enable'] !== 'Y' ) {
            return;
        }

        // Add Pusher configuration endpoint
        add_action( 'wp_ajax_vitepos_pusher_config', array( $this, 'get_pusher_config' ) );
        add_action( 'wp_ajax_nopriv_vitepos_pusher_config', array( $this, 'get_pusher_config' ) );
    }

    /**
     * Get Pusher configuration
     */
    public function get_pusher_config() {
        $pos_settings = POS_Settings::get_module_instance();
        $push_settings = $pos_settings->get_push_settings();
        
        if ( empty( $push_settings->pusher ) || $push_settings->pusher['is_enable'] !== 'Y' ) {
            wp_send_json_error( 'Pusher not enabled' );
            return;
        }

        $config = array(
            'pusher' => array(
                'is_enable' => $push_settings->pusher['is_enable'],
                'pushser_key' => $push_settings->pusher['pushser_key'],
                'pushser_cluster' => $push_settings->pusher['pushser_cluster']
            )
        );

        wp_send_json_success( $config );
    }

    /**
     * Send notification to displays
     */
    private function send_to_displays( $notification_data, $target_displays = array( 'kitchen', 'customer', 'preparation' ) ) {
        // Add target displays to notification
        $notification_data['target_displays'] = $target_displays;

        // Send via Pusher if available
        $this->send_via_pusher( $notification_data );

        // Store for polling clients
        $this->store_notification( $notification_data );

        // Log for debugging
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( 'VitePos Display Notification: ' . json_encode( $notification_data ) );
        }
    }

    /**
     * Send notification via Pusher
     */
    private function send_via_pusher( $data ) {
        $pos_settings = POS_Settings::get_module_instance();
        
        if ( method_exists( $pos_settings, 'send_push_message' ) ) {
            $pos_settings->send_push_message( $data, 'display_notification' );
        }
    }

    /**
     * Store notification for polling clients
     */
    private function store_notification( $data ) {
        $notifications = get_option( 'vitepos_display_notifications', array() );
        
        // Add timestamp if not present
        if ( !isset( $data['timestamp'] ) ) {
            $data['timestamp'] = current_time( 'mysql' );
        }
        
        $notifications[] = $data;
        
        // Keep only last 100 notifications
        if ( count( $notifications ) > 100 ) {
            $notifications = array_slice( $notifications, -100 );
        }
        
        update_option( 'vitepos_display_notifications', $notifications );
    }

    /**
     * Get updates since timestamp
     */
    private function get_updates_since( $timestamp, $display_type = '' ) {
        $notifications = get_option( 'vitepos_display_notifications', array() );
        $updates = array();

        foreach ( $notifications as $notification ) {
            if ( isset( $notification['timestamp'] ) && 
                 strtotime( $notification['timestamp'] ) > strtotime( $timestamp ) ) {
                
                // Filter by display type if specified
                if ( !empty( $display_type ) && 
                     isset( $notification['target_displays'] ) && 
                     !in_array( $display_type, $notification['target_displays'] ) ) {
                    continue;
                }
                
                $updates[] = $notification;
            }
        }

        return $updates;
    }

    /**
     * Get status change message
     */
    private function get_status_change_message( $order_id, $old_status, $new_status ) {
        $messages = array(
            'vt_in_kitchen' => "طلب رقم #{$order_id} تم إرساله للمطبخ",
            'vt_preparing' => "طلب رقم #{$order_id} قيد التحضير",
            'vt_ready_to_srv' => "طلب رقم #{$order_id} جاهز للتقديم",
            'vt_served' => "طلب رقم #{$order_id} تم تقديمه",
            'completed' => "طلب رقم #{$order_id} مكتمل",
            'cancelled' => "طلب رقم #{$order_id} تم إلغاؤه"
        );

        return isset( $messages[$new_status] ) ? $messages[$new_status] : "تم تحديث حالة طلب رقم #{$order_id}";
    }

    /**
     * Clean old notifications (run via cron)
     */
    public function clean_old_notifications() {
        $notifications = get_option( 'vitepos_display_notifications', array() );
        $cutoff_time = strtotime( '-24 hours' );
        
        $filtered_notifications = array_filter( $notifications, function( $notification ) use ( $cutoff_time ) {
            return isset( $notification['timestamp'] ) && 
                   strtotime( $notification['timestamp'] ) > $cutoff_time;
        });

        update_option( 'vitepos_display_notifications', array_values( $filtered_notifications ) );
    }
}

// Schedule cleanup
if ( !wp_next_scheduled( 'vitepos_clean_display_notifications' ) ) {
    wp_schedule_event( time(), 'hourly', 'vitepos_clean_display_notifications' );
}

add_action( 'vitepos_clean_display_notifications', function() {
    $hooks = new POS_Display_Hooks();
    $hooks->clean_old_notifications();
});
