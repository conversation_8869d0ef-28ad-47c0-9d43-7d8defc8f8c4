<?php
/**
 * Pos Counter Database Model
 *
 * @package Vitepos\Models\Database
 */

namespace Vitepos\Models\Database;

use VitePos\Core\ViteposModel;

/**
 * Class Mapbd_pos_counter
 *
 * @properties id,name,number,outlet_id
 */
class Mapbd_Pos_Counter extends ViteposModel {
	/**
	 * Its property id
	 *
	 * @var int
	 */
	public $id;
	/**
	 * Its property name
	 *
	 * @var string
	 */
	public $name;
	/**
	 * Its property counter_number
	 *
	 * @var int
	 */
	public $counter_number;
	/**
	 * Its property outlet_id
	 *
	 * @var int
	 */
	public $outlet_id;

	/**
	 * Mapbd_pos_counter constructor.
	 */
	public function __construct() {
		parent::__construct();
		$this->set_validation();
		$this->table_name     = 'apbd_pos_counter';
		$this->primary_key    = 'id';
		$this->unique_key     = array();
		$this->multi_key      = array();
		$this->auto_inc_field = array( 'id' );
		$this->app_base_name  = 'apbd-elite-pos';

	}


	/**
	 * The set validation is generated by appsbd
	 */
	public function set_validation() {
		$this->validations = array(
			'id'             => array(
				'Text' => 'Id',
				'Rule' => 'max_length[10]|integer',
			),
			'name'           => array(
				'Text' => 'Name',
				'Rule' => 'max_length[100]',
			),
			'counter_number' => array(
				'Text' => 'Counter Number',
				'Rule' => 'max_length[5]|integer',
			),
			'outlet_id'      => array(
				'Text' => 'Outlet Id',
				'Rule' => 'required|max_length[10]|integer',
			),

		);
	}

	/**
	 * The create db table is generated by appsbd
	 */
	public static function create_db_table() {
		$this_obj = new static();
		$table    = $this_obj->db->prefix . $this_obj->table_name;
		if ( $this_obj->db->get_var( "show tables like '{$table}'" ) != $table ) {
			$sql = "CREATE TABLE `{$table}` (
					  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
					  `name` char(100) NOT NULL DEFAULT '',
					  `counter_number` int(5) unsigned DEFAULT 0,
					  `outlet_id` int(10) unsigned NOT NULL COMMENT 'FK(wp_apbd_pos_warehouse,id,name)',
					  PRIMARY KEY (`id`)
					)";
			require_once ABSPATH . 'wp-admin/includes/upgrade.php';
			dbDelta( $sql );
		}
	}

	/**
	 * The get counters is generated by appsbd
	 *
	 * @return array
	 */
	public static function get_counters() {
		$result   = self::fetch_all( '', 'counter_number', 'ASC' );
		$response = array();
		foreach ( $result as $counter ) {
			if ( empty( $response[ $counter->outlet_id ] ) ) {
				$response[ $counter->outlet_id ] = array();
			}
			$response[ $counter->outlet_id ][] = $counter;
		}
		return $response;
	}

	/**
	 * The delete by id is generated by appsbd
	 *
	 * @param mixed $id Its the id of counter.
	 *
	 * @return bool
	 */
	public static function delete_by_id( $id ) {
		return parent::delete_by_key_value( 'id', $id );
	}

}
