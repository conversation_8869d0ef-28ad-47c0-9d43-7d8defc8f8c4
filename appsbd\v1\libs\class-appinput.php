<?php
/**
 * Its api for heartbit
 *
 * @since : 1
 * @author: appsbd
 * @package Appsbd\V1\libs
 */

namespace Appsbd\V1\libs;

if ( ! class_exists( __NAMESPACE__ . '\AppInput' ) ) {
	/**
	 * Class AppInput
	 *
	 * @package Appsbd\V1\libs
	 */
	class AppInput {

		/**
		 * Its property __posted_data
		 *
		 * @var null
		 */
		protected static $__posted_data = null;

		/**
		 * Its property __posted_data
		 *
		 * @var null
		 */
		protected static $__request_data = null;

		/** Its property __posted_data
		 *
		 * @var null
		 */
		protected static $__get_data = null;
		/**
		 * Its property _is_sanitized
		 *
		 * @var bool
		 */
		protected static $_is_sanitized = false;

		/**
		 * The sanitize all input data is generated by appsbd
		 */
		public static function sanitize_all_input_data() {
			/**
			 * Its for html field filter
			 *
			 * @since : 1.0
			 */
			$html_inputs = apply_filters( 'appsbd/input/html/fields', array() );
			if ( true || check_ajax_referer( 'ajax-nonce' ) ) {
				self::$__posted_data = self::sanitize_array( $_POST, $html_inputs );
			}
			self::$__get_data     = self::sanitize_array( $_GET, $html_inputs );
			self::$__request_data = self::sanitize_array( $_REQUEST, $html_inputs );

		}

		/**
		 * The GetPostedData is generated by appsbd
		 *
		 * @return array|null
		 */
		public static function get_posted_data() {
			if ( is_null( self::$__posted_data ) ) {
				return array();
			}
			return self::$__posted_data;
		}

		/**
		 * The get request data is generated by appsbd
		 *
		 * @return array
		 */
		public static function &get_request_data() {
			return $_REQUEST;
		}

		/**
		 * The post value is generated by appsbd
		 *
		 * @param any    $name Its name param.
		 * @param string $default Its default param.
		 *
		 * @return mixed|string
		 */
		public static function post_value( $name, $default = '' ) {
			if ( ! is_null( self::$__posted_data ) && isset( self::$__posted_data[ $name ] ) ) {
				return self::$__posted_data[ $name ];
			}
			return $default;
		}

		/**
		 * The get value is generated by appsbd
		 *
		 * @param any    $name Its name param.
		 * @param string $default Its default param.
		 */
		public static function get_value( $name, $default = '' ) {
			if ( ! is_null( self::$__get_data ) && isset( self::$__get_data[ $name ] ) ) {
				return self::$__get_data[ $name ];
			}
			return $default;
		}

		/**
		 * The get value is generated by appsbd
		 *
		 * @param mixed   $name Its name param.
		 * @param string  $default Its default param.
		 * @param boolean $xss_clean Its xss clean param.
		 *
		 * @return array|string
		 */
		public static function request_value( $name, $default = '', $xss_clean = true ) {
			if ( empty( $name ) || ! is_string( $name ) ) {
				return $default;
			}
			if ( $xss_clean ) {
				if ( ! empty( self::$__request_data ) && is_array( self::$__request_data ) && isset( self::$__request_data[ $name ] ) ) {
					return self::$__request_data[ $name ];
				}
			} else {
				$req = self::get_request_data();
				if ( isset( $req[ $name ] ) ) {
					return wp_unslash( $req[ $name ] );
				}
			}

			return $default;
		}

		/**
		 * The set bool value is generated by appsbd
		 *
		 * @param any    $name Its name.
		 * @param string $true_value Its for true value.
		 * @param string $false_value Its for false value.
		 */
		public static function set_bool_value( $name, $true_value = 'Y', $false_value = 'N' ) {
			if ( isset( self::$__posted_data[ $name ] ) ) {
				if ( json_decode( self::$__posted_data[ $name ] ) || self::$__posted_data[ $name ] === $true_value ) {
					self::$__posted_data[ $name ] = $true_value;
				} else {
					self::$__posted_data[ $name ] = $false_value;
				}
			}
			if ( isset( self::$__request_data[ $name ] ) ) {
				if ( json_decode( self::$__request_data[ $name ] ) || self::$__request_data[ $name ] === $true_value ) {
					self::$__request_data[ $name ] = $true_value;
				} else {
					self::$__request_data[ $name ] = $false_value;
				}
			}
		}

		/**
		 * The unset post value is generated by appsbd
		 *
		 * @param mixed $key Its key.
		 * @param mixed $data Its data.
		 */
		public static function update_post_value( $key, $data ) {
			elf::$__posted_data[ $key ] = $data;
		}

		/**
		 * The unset post value is generated by appsbd
		 *
		 * @param mixed $key Its key.
		 * @param mixed $data Its data.
		 */
		public static function update_request_value( $key, $data ) {
			elf::$__request_data[ $key ] = $data;
		}

		/**
		 * The unset post value is generated by appsbd
		 *
		 * @param mixed $key Its key.
		 */
		public static function unset_post_value( $key ) {
			if ( isset( self::$__posted_data[ $key ] ) ) {
				unset( self::$__posted_data[ $key ] );
			}
		}
		/**
		 * The unset post value is generated by appsbd
		 *
		 * @param mixed $key Its key.
		 */
		public static function unset_request_value( $key ) {
			if ( isset( self::$__request_data[ $key ] ) ) {
				unset( self::$__request_data[ $key ] );
			}
		}
		/**
		 * The unset post value is generated by appsbd
		 *
		 * @param mixed $key Its key.
		 */
		public static function unset_get_value( $key ) {
			if ( isset( $_GET[ $key ] ) ) {
				unset( $_GET[ $key ] );
			}
		}
		/**
		 * The sanitize_array is generated by appsbd
		 *
		 * @param any    $var Its var param.
		 * @param array  $html_inputs Its htmlInputs param.
		 * @param string $key Its key param.
		 *
		 * @return array|string
		 */
		public static function sanitize_array( $var, $html_inputs = array(), $key = '' ) {
			if ( is_array( $var ) ) {
				foreach ( $var as $key => &$item ) {
					$item = self::sanitize_array( $item, $html_inputs, $key );
				}
				return $var;
			} else {
				if ( is_scalar( $var ) ) {
					if ( ! empty( $key ) && in_array( $key, $html_inputs ) ) {
						return wp_kses_post( $var );
					} else {
						return sanitize_text_field( $var );
					}
				} else {
					return $var;
				}
			}

		}

		/**
		 * The get server array is generated by appsbd
		 *
		 * @return array
		 */
		public static function &get_server_array() {
			return $_SERVER;
		}

		/**
		 * The get server data is generated by appsbd
		 *
		 * @param any $index Its index param.
		 *
		 * @return mixed|null
		 */
		public static function get_server_data( $index ) {
			$server = self::get_server_array();
			if ( isset( $server[ $index ] ) ) {
				return $server[ $index ];
			}
			return null;
		}

		/**
		 * The is post back is generated by appsbd
		 *
		 * @return bool
		 */
		public static function is_post_back() {
			$method = self::get_server_data( 'REQUEST_METHOD' );
			return 'POST' == strtoupper( $method );
		}

		/**
		 * The getUploadedFiles is generated by appsbd
		 *
		 * @return array
		 */
		public static function &get_uploaded_files() {
			return $_FILES;
		}

	}
}
