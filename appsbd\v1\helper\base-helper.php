<?php
/**
 * It's base helper file
 *
 * @package appsbd
 */

if ( ! defined( 'APPSBD_IS_POST_BACK' ) ) {
	define( 'APPSBD_IS_POST_BACK', ! empty( $_SERVER['REQUEST_METHOD'] ) && strtoupper( sanitize_text_field( wp_unslash( $_SERVER['REQUEST_METHOD'] ) ) ) == 'POST' );
}

if ( ! function_exists( 'appsbd_add_error_v1' ) ) {
	/**
	 * The appsbd add error v1 is generated by appsbd
	 *
	 * @param string $msg Its msg string.
	 */
	function appsbd_add_error_v1( $msg ) {
		return \Appsbd\V1\Core\Kernel::add_error( $msg );
	}
}

if ( ! function_exists( 'appsbd_add_info_v1' ) ) {
	/**
	 * The appsbd add info v1 is generated by appsbd
	 *
	 * @param string $msg Its msg string.
	 */
	function appsbd_add_info_v1( $msg ) {
		return \Appsbd\V1\Core\Kernel::add_info( $msg );
	}
}

if ( ! function_exists( 'appsbd_add_warning_v1' ) ) {
	/**
	 * The appsbd add warning v1 is generated by appsbd
	 *
	 * @param string $msg Its msg string.
	 */
	function appsbd_add_warning_v1( $msg ) {
		return \Appsbd\V1\Core\Kernel::add_warning( $msg );
	}
}
if ( ! function_exists( 'appsbd_add_query_error_v1' ) ) {
	/**
	 * The appsbd_add_query_error_v1 is generated by appsbd
	 *
	 * @param string $msg Its message string.
	 */
	function appsbd_add_query_error_v1( $msg ) {
		if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
						return appsbd_add_error_v1( $msg );
					}
	}
}

if ( ! function_exists( 'appsbd_add_model_errors_code_v1' ) ) {
	/**
	 * The appsbd_add_model_errors_code_v1 is generated by appsbd
	 *
	 * @param string $msg Its message string.
	 */
	function appsbd_add_model_errors_code_v1( $msg ) {
		return appsbd_add_error_v1( 'Error Code:' . $msg );
	}
}
if ( ! function_exists( 'appsbd_add_debug_log_v1' ) ) {
	/**
	 * The appsbd add debug log v1 is generated by appsbd.
	 *
	 * @param string $msg Its msg string.
	 */
	function appsbd_add_debug_log_v1( $msg ) {
		return \Appsbd\V1\Core\Kernel::add_debug( $msg );
	}
}

if ( ! function_exists( 'appsbd_lan_v1' ) ) {
	/**
	 * Its for generate language
	 *
	 * @param string $str Its string to be translate.
	 * @param string $domain Its translation domain.
	 * @param null   $parameter Its extra param.
	 * @param null   $_ Its so on.
	 * @return mixed
	 */
	function appsbd_lan_v1( $str, $domain, $parameter = null, $_ = null ) {
		$args    = func_get_args();
		$args[0] = call_user_func( '__', $args[0], $domain );
		if ( isset( $args[1] ) ) {
			unset( $args[1] );
		}
		if ( count( $args ) > 1 ) {
			$msg = call_user_func_array( 'sprintf', $args );
		} else {
			$msg = $args[0];
		}
		return $msg;
	}
}

if ( ! function_exists( 'appsbd_get_msg_api' ) ) {
	/**
	 * The appsbd get msg api is generated by appsbd
	 *
	 * @return stdClass
	 */
	function appsbd_get_msg_api() {
		return \Appsbd\V1\Core\Kernel::get_msg_for_api();
	}
}

if ( ! function_exists( 'get_request_content_type' ) ) {
	/**
	 * The get request content type is generated by appsbd
	 *
	 * @return mixed
	 */
	function get_request_content_type() {
		if ( ! empty( $_SERVER['HTTP_CONTENT_TYPE'] ) ) {
			return sanitize_text_field( wp_unslash( $_SERVER['HTTP_CONTENT_TYPE'] ) );
		}
		if ( ! empty( $_SERVER['CONTENT_TYPE'] ) ) {
			return sanitize_text_field( wp_unslash( $_SERVER['CONTENT_TYPE'] ) );
		}
	}
}
if ( ! function_exists( 'appsbd_current_url' ) ) {
	/**
	 * The appsbd_current_url is generated by appsbd
	 *
	 * @param bool $is_with_param Set if you want with param too.
	 *
	 * @return string
	 */
	function appsbd_current_url( $is_with_param = true ) {
		$server_arr = \Appsbd\V1\libs\AppInput::get_server_array();
		if ( isset( $server_arr['HTTPS'] ) &&
			( 'on' == $server_arr['HTTPS'] || 1 == $server_arr['HTTPS'] ) ||
			isset( $server_arr['HTTP_X_FORWARDED_PROTO'] ) &&
			 'https' == $server_arr['HTTP_X_FORWARDED_PROTO'] ) {
			$protocol = 'https://';
		} else {
			$protocol = 'http://';
		}
		if ( $is_with_param ) {
			return $protocol . $server_arr['HTTP_HOST'] . $server_arr['REQUEST_URI'];
		} else {
			$url_parts = parse_url( $protocol . $server_arr['HTTP_HOST'] . $server_arr['REQUEST_URI'] );
			return $url_parts['scheme'] . '://' . $url_parts['host'] . $url_parts['path'];
		}
	}
}
if ( ! function_exists( 'appsbd_load_svg_icons' ) ) {
	/**
	 * The appsbd load svg icons is generated by appsbd
	 *
	 * @param mixed $path Its the path.
	 *
	 * @return string
	 */
	function appsbd_load_svg_icons( $path ) {
		$path = realpath( $path );
		if ( file_exists( $path ) ) {
			$data = strip_tags( appsbd_file_get_contents( $path ) );
			return $data;
		}
		return '';
	}
}
if ( ! function_exists( 'appsbd_get_std_class' ) ) {
	/**
	 * The appsbd get std class is generated by appsbd
	 *
	 * @return stdClass
	 */
	function &appsbd_get_std_class() {
		$obj = new stdClass();
		return $obj;
	}
}
if ( ! function_exists( 'appsbd_trim' ) ) {
	/**
	 * The appsbd_trim is generated by appsbd
	 *
	 * @param string $str Its trim string.
	 * @param string $charlist Its char list.
	 *
	 * @return string
	 */
	function appsbd_trim( $str, $charlist = " \t\n\r\0\x0B" ) {
		if ( empty( $str ) || ! is_string( $str ) ) {
			return $str;
		}
		return trim( $str, $charlist );
	}
}

if ( ! function_exists( 'appsbd_file_put_contents' ) ) {
	/**
	 * The appsbd file put contents is generated by appsbd
	 *
	 * @param mixed $filename Its filename param.
	 * @param mixed $data Its data param.
	 * @param int   $flags Its flags param.
	 * @param null  $context Its context param.
	 *
	 * @return bool
	 */
	function appsbd_file_put_contents( $filename, $data, $flags = 0, $context = null ) {
		if ( file_put_contents( $filename, $data, $flags, $context ) ) {
			return true;
		} else {
			require_once ABSPATH . 'wp-admin/includes/file.php';
			WP_Filesystem();
			global $wp_filesystem;

			return $wp_filesystem->put_contents(
				$filename,
				$data,
				FS_CHMOD_FILE 			);
		}
	}
}
if ( ! function_exists( 'appsbd_get_remote_ip' ) ) {
	/**
	 * The appsbd get remote ip is generated by appsbd
	 *
	 * @return mixed|string
	 */
	function appsbd_get_remote_ip() {
		$server_arr = \Appsbd\V1\libs\AppInput::get_server_array();
		if ( ! empty( $server_arr['HTTP_X_REAL_IP'] ) ) {
			return $server_arr['HTTP_X_REAL_IP'];
		} elseif ( ! empty( $server_arr['HTTP_CLIENT_IP'] ) ) {
			return $server_arr['HTTP_CLIENT_IP'];
		} elseif ( ! empty( $server_arr['HTTP_X_FORWARDED_FOR'] ) ) {
			return $server_arr['HTTP_X_FORWARDED_FOR'];
		} elseif ( ! empty( $server_arr['HTTP_CF_CONNECTING_IP'] ) ) {
			return $server_arr['HTTP_CF_CONNECTING_IP'];
		} else {
			return ! empty( $server_arr['REMOTE_ADDR'] ) ? $server_arr['REMOTE_ADDR'] : '-';
		}
	}
}
if ( ! function_exists( 'appsbd_get_file_system' ) ) {
	/**
	 * Its for wp file system
	 *
	 * @return WP_Filesystem_Direct
	 */
	function &appsbd_get_file_system() {
		global $wp_filesystem;
		require_once ABSPATH . 'wp-admin/includes/file.php';
		WP_Filesystem();
		return $wp_filesystem;
	}
}
if ( ! function_exists( 'appsbd_file_get_contents' ) ) {
	/**
	 * The appsbd file get contents is generated by appsbd
	 *
	 * @param mixed $filename Its filename param.
	 *
	 * @return false|string
	 */
	function appsbd_file_get_contents( $filename ) {
		require_once ABSPATH . 'wp-admin/includes/class-wp-filesystem-base.php';
		require_once ABSPATH . 'wp-admin/includes/class-wp-filesystem-direct.php';
		$wp_filesystem = new WP_Filesystem_Direct( false );
		return $wp_filesystem->get_contents( $filename );
	}
}


if ( ! function_exists( 'appsbd_get_text_by_key' ) ) {
	/**
	 * The appsbd get text by key is generated by appsbd
	 *
	 * @param mixed $key Its key param.
	 * @param array $data Its data param.
	 *
	 * @return mixed
	 */
	function appsbd_get_text_by_key( $key, $data = array() ) {
		return ! empty( $data[ $key ] ) ? $data[ $key ] : $key;
	}
}
if ( ! function_exists( 'appsbd_clean_request' ) ) {
	/**
	 * The appsbd clean request is generated by appsbd
	 */
	function appsbd_clean_request() {
		remove_all_filters( 'http_request_args', 9999 );
		remove_all_filters( 'pre_http_request', 9999 );
		remove_all_filters( 'http_api_debug', 9999 );
		remove_all_filters( 'requests-curl.before_request', 9999 );
		remove_all_filters( 'requests-curl.after_request', 9999 );
		remove_all_filters( 'requests-fsockopen.before_request', 9999 );
		remove_all_filters( 'requests-fsockopen.after_request', 9999 );
	}
}

if ( ! function_exists( 'appsbd_get_user_title_by_user' ) ) {
	/**
	 * Its for user title
	 *
	 * @param \WP_User $user Its user.
	 * @return string
	 */
	function appsbd_get_user_title_by_user( $user ) {
		$title = '';
		if ( $user instanceof \WP_User ) {
			$name = $user->user_firstname;
			if ( ! empty( $user->user_firstname ) ) {
				$title = $user->user_firstname . ' ' . $user->user_lastname;			} elseif ( empty( $title ) ) {
				$title = $user->user_nicename;
			}
		}
		return $title;
	}
}

if ( ! function_exists( 'appsbd_get_wp_time' ) ) {
	/**
	 * The appsbd get wp time is generated by appsbd
	 *
	 * @param string $timestr This is timestr.
	 * @param string $format This is format.
	 *
	 * @return int|string
	 */
	function appsbd_get_wp_time( $timestr = '', $format = '' ) {
		$timezone = get_option( 'timezone_string' );
		try {
			$apptimezone = date_default_timezone_get();
			if ( ! empty( $timestr ) ) {
				$date = new DateTime( $timestr, new DateTimeZone( $apptimezone ) );
			} else {
				$date = new DateTime();
			}
			if ( ! empty( $timezone ) && strtoupper( $apptimezone ) != strtolower( $timezone ) ) {
				$date->setTimezone( new DateTimeZone( $timezone ) );
			}

			if ( ! empty( $format ) ) {
				return $date->format( $format );
			} else {
				return $date->getTimestamp();
			}
		} catch ( Exception $e ) {
			return $e->getMessage();
		}
	}
}
if ( ! function_exists( 'appsbd_get_wp_datetime_with_format' ) ) {
	/**
	 * The appsbd get wp datetime with format is generated by appsbd
	 *
	 * @param string $timestr This is timestr.
	 *
	 * @return int|string
	 */
	function appsbd_get_wp_datetime_with_format( $timestr = '' ) {
		return appsbd_get_wp_time( $timestr, get_option( 'date_format' ) . ' ' . get_option( 'time_format' ) );
	}
}
if ( ! function_exists( 'appsbd_get_wp_date_with_format' ) ) {
	/**
	 * The appsbd get wp date with format is generated by appsbd
	 *
	 * @param string $timestr This is timestr.
	 *
	 * @return int|string
	 */
	function appsbd_get_wp_date_with_format( $timestr = '' ) {
		return appsbd_get_wp_time( $timestr, get_option( 'date_format' ) );
	}
}
if ( ! function_exists( 'appsbd_get_wp_time_with_format' ) ) {
	/**
	 * The appsbd get wp time with format is generated by appsbd
	 *
	 * @param string $timestr This is timestr.
	 *
	 * @return int|string
	 */
	function appsbd_get_wp_time_with_format( $timestr = '' ) {
		return appsbd_get_wp_time( $timestr, get_option( 'time_format' ) );
	}
}
if ( ! function_exists( 'appsbd_get_wptime_to_gmtime' ) ) {
	/**
	 * The appsbd get wptime to gmtime is generated by appsbd
	 *
	 * @param string $timestr This is timestr.
	 * @param string $format  This is format.
	 *
	 * @return false|int|string
	 */
	function appsbd_get_wptime_to_gmtime( $timestr = '', $format = '' ) {
		$apptimezone = wp_timezone_string();
		try {
			$timezone = date_default_timezone_get();
			if ( $apptimezone == $timezone ) {
				return gmdate( $format, strtotime( $timestr ) );
			}
			if ( ! empty( $timestr ) ) {
				$date = new DateTime( $timestr, new DateTimeZone( $apptimezone ) );
			} else {
				$date = new DateTime();
			}
			if ( ! empty( $timezone ) && strtoupper( $apptimezone ) != strtolower( $timezone ) ) {
				$date->setTimezone( new DateTimeZone( $timezone ) );
			}

			if ( ! empty( $format ) ) {
				return $date->format( $format );
			} else {
				return $date->getTimestamp();
			}
		} catch ( Exception $e ) {
			return $e->getMessage();
		}
	}
}
if ( ! function_exists( 'appsbd_gm_date' ) ) {
	/**
	 * The appsbd gm date is generated by appsbd
	 *
	 * @param string $timestr Its time str.
	 * @param string $format Its format.
	 *
	 * @return false|int|string
	 */
	function appsbd_gm_date( $timestr, $format = 'Y-m-d 00:00:00' ) {
		return appsbd_get_wptime_to_gmtime( gmdate( $format, strtotime( $timestr ) ), 'Y-m-d H:i:s' );
	}
}
if ( ! function_exists( 'appsbd_print' ) ) {
	/**
	 * The apbd print is generated by appsbd
	 *
	 * @param mixed $object Its object of print debug.
	 * @param false $is_return Set true if you want to return instead of print.
	 *
	 * @return string|true
	 */
	function appsbd_print( $object, $is_return = false ) {
		if ( $is_return ) {
			return print_r( $object, true );
		}
		echo wp_kses_post( '<pre>' . print_r( $object, true ) . '</pre>' );
	}
}
if ( ! function_exists( 'appsbd_is_rest' ) ) {
	/**
	 * Checks if the current request is a WP REST API request.
	 *
	 * Case #1: After WP_REST_Request initialisation
	 * Case #2: Support "plain" permalink settings and check if `rest_route` starts with `/`
	 * Case #3: It can happen that WP_Rewrite is not yet initialized,
	 *          so do this (wp-settings.php)
	 * Case #4: URL Path begins with wp-json/ (your REST prefix)
	 *          Also supports WP installations in subfolders
	 *
	 * @returns boolean
	 */
	function appsbd_is_rest() {
		$route = \Appsbd\V1\libs\AppInput::get_value( 'rest_route' );
		if ( defined( 'REST_REQUEST' ) && REST_REQUEST 			 || ! empty( $route ) 				&& strpos( $route, '/', 0 ) === 0 ) {
			return true;
		}

				$wprewrite = appsbd_get_wp_rewrite();
		if ( null === $wprewrite ) {
			$wprewrite = new WP_Rewrite();
		}

				$rest_url    = wp_parse_url( trailingslashit( rest_url() ) );
		$current_url = wp_parse_url( add_query_arg( array() ) );
		return strpos( ! empty( $current_url['path'] ) ? $current_url['path'] : '/', $rest_url['path'], 0 ) === 0;
	}
}
if ( ! function_exists( 'appsbd_get_wp_rewrite' ) ) {
	/**
	 * The get wp rewrite is generated by appsbd
	 */
	function &appsbd_get_wp_rewrite() {
		global $wp_rewrite;
		return $wp_rewrite;
	}
}

if ( ! function_exists( 'appsbd_is_activated_plugin' ) ) {
	/**
	 * The appsbd is activated plugin is generated by appsbd
	 *
	 * @param mixed $plugin_path Its the plugin path.
	 *
	 * @return bool
	 */
	function appsbd_is_activated_plugin( $plugin_path ) {
		/**
		 * Its for check is there any change before process
		 *
		 * @since 1.0
		 */
		$active_plugins         = apply_filters( 'active_plugins', get_option( 'active_plugins' ) );
		$network_active_plugins = array();
		if ( is_multisite() ) {
			$network_active_plugins = array_keys( get_site_option( 'active_sitewide_plugins' ) );
		}
		if ( in_array( $plugin_path, $active_plugins ) || in_array( $plugin_path, $network_active_plugins ) ) {
			return true;
		}
		return false;
	}
}
if ( ! function_exists( 'appsbd_get_alphanumeric' ) ) {
	/**
	 * The appsbd is activated plugin is generated by appsbd
	 *
	 * @param string $str The string to filter.
	 *
	 * @return bool
	 */
	function appsbd_get_alphanumeric( $str ) {
		return trim( preg_replace( '#[^a-z0-9 \-]#i', '', $str ) );
	}
}
