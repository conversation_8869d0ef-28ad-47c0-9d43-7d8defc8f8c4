<?php
/**
 * Pos Warehouse Database Model
 *
 * @package Vitepos\Models\Database
 */

namespace Vitepos\Models\Database;

use VitePos\Core\ViteposModel;


/**
 * Class Mapbd_pos_cash_drawer_log
 *
 * @package Vitepos\Models\Database
 */
class Mapbd_Pos_Stock_Log extends ViteposModel {
	/**
	 * Its property id
	 *
	 * @var int
	 */
	public $id;
	/**
	 * Its property cash_drawer_id
	 *
	 * @var int
	 */
	public $product_id;
	/**
	 * Its property ref_id
	 *
	 * @var int
	 */
	public $outlet_id;
	/**
	 * Its property ref_id
	 *
	 * @var string
	 */
	public $stock_type;
	/**
	 * Its property ref_id
	 *
	 * @var int
	 */
	public $stock_val;
	/**
	 * Its property ref_id
	 *
	 * @var int
	 */
	public $prev_stock;
	/**
	 * Its property ref_id
	 *
	 * @var int
	 */
	public $user_id;
	/**
	 * Its property ref_id
	 *
	 * @var String
	 */
	public $ref_val;
	/**
	 * Its property note
	 *
	 * @var String
	 */
	public $msg;
	/**
	 * Its property type
	 *
	 * @var String
	 */
	public $type;
	/**
	 * Its property ref_type
	 *
	 * @var String
	 */
	public $ref_type;
	/**
	 * Its property entry_time
	 *
	 * @var String
	 */
	public $entry_date;

	/**
	 * Its property ex param
	 *
	 * @var String
	 */
	public $ex1_param;

	/**
	 * Its property ex2 param
	 *
	 * @var String
	 */
	public $ex2_param;

	/**
	 * Mapbd_pos_cash_drawer_log constructor.
	 */
	public function __construct() {
		parent::__construct();
		$this->SetValidation();
		$this->table_name     = 'apbd_pos_stock_log';
		$this->primary_key    = 'id';
		$this->unique_key     = array();
		$this->multi_key      = array();
		$this->auto_inc_field = array( 'id' );
		$this->app_base_name  = 'apbd-vite-pos';

	}


	/**
	 * The set validation is generated by appsbd
	 */
	public function set_validation() {
		$this->validations = array(
			'id'         => array(
				'Text' => 'Id',
				'Rule' => 'max_length[10]|integer',
			),
			'product_id' => array(
				'Text' => 'Product Id',
				'Rule' => 'max_length[11]|integer',
			),
			'outlet_id'  => array(
				'Text' => 'Outlet Id',
				'Rule' => 'max_length[50]',
			),
			'stock_type'  => array(
				'Text' => 'Stock Type',
				'Rule' => 'max_length[1]',
			),
			'stock_val'  => array(
				'Text' => 'Outlet Id',
				'Rule' => 'max_length[11]|integer',
			),
			'prev_stock' => array(
				'Text' => 'Outlet Id',
				'Rule' => 'max_length[11]|integer',
			),
			'user_id'    => array(
				'Text' => 'User Id',
				'Rule' => 'max_length[50]',
			),
			'msg'        => array(
				'Text' => 'Note',
				'Rule' => 'max_length[255]',
			),
			'ref_val'    => array(
				'Text' => 'Note',
				'Rule' => 'max_length[100]',
			),
			'type'       => array(
				'Text' => 'Log Type',
				'Rule' => 'max_length[1]',
			),
			'ref_type'   => array(
				'Text' => 'Ref Type',
				'Rule' => 'max_length[2]',
			),
			'entry_date' => array(
				'Text' => 'Entry Time',
				'Rule' => 'max_length[20]',
			),


		);
	}

	/**
	 * The get property raw options is generated by appsbd
	 *
	 * @param mixed   $property Its the property.
	 * @param boolean $is_with_select False if with select.
	 *
	 * @return array|string[]
	 */
	public function get_property_raw_options( $property, $is_with_select = false ) {
		$return_obj = array();
		switch ( $property ) {
			case 'type':
				$return_obj = array(
					'I' => 'In',
					'O' => 'Out',
				);
				break;
			case 'ref_type':
				$return_obj = array(
					'PU' => 'Purchase',
					'OR' => 'Order',
					'TS' => 'Transfer Send',
					'TR' => 'Transfer Receive',
					'TC' => 'Transfer Cancelled',
					'TB' => 'Transfer Back',
					'OB' => 'Order Back',
					'PA' => 'Product Add',
					'PE' => 'Product Edit',
					'OT' => 'Online Transfer',
				);
				break;
			default:
		}
		if ( $is_with_select ) {
			return array_merge( array( '' => 'Select' ), $return_obj );
		}
		return $return_obj;

	}

	/**
	 * The AddLog is generated by appsbd
	 *
	 * @param any $type Its log_type param, I=In,O=Out.
	 * @param any $outlet_id Its Outlet param.
	 * @param any $product_id Its cash_drawe_id param.
	 * @param any $pre_stock Its pre stock param.
	 * @param any $stock_val Its stock value parem.
	 * @param any $msg Its message param.
	 * @param string $ref_val Its amount param.
	 * @param string $ref_type Its ref_type param.
	 *
	 * @param string $stock_type Its stock type.
	 *
	 * @return bool
	 */
	public static function AddLog( $type, $outlet_id, $product_id, $pre_stock, $stock_val, $msg, $ref_val = '', $ref_type = '' ,$stock_type='O',$ex1_param='',$ex2_param='') {
		$newobj = new self();

		$newobj->product_id( $product_id );
		$newobj->outlet_id( $outlet_id );
		$newobj->stock_type($stock_type);
		$newobj->user_id( get_current_user_id() );
		$newobj->stock_val( $stock_val );
		$newobj->prev_stock( $pre_stock );
		$newobj->msg( $msg );
		$newobj->ref_val( $ref_val );
		$newobj->ref_type( $ref_type );
		$newobj->type( $type );
		$newobj->entry_date( gmdate( 'Y-m-d' ) );
		$newobj->ex1_param($ex1_param);
		$newobj->ex2_param($ex2_param);
		return $newobj->save();
	}
	/**
	 * The create db table is generated by appsbd
	 */
	public static function create_db_table() {
		$this_obj = new static();
		$table    = $this_obj->db->prefix . $this_obj->table_name;
		if ( $this_obj->db->get_var( "show tables like '{$table}'" ) != $table ) {
			$sql = "CREATE TABLE `{$table}` (
					`id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  					`product_id` int(11) unsigned NOT NULL,
  					`outlet_id` int(11) unsigned NOT NULL,
  					`stock_type` char(1) NOT NULL DEFAULT 'O' COMMENT 'radio(O=Outlet Wise,W=WooCommerce Stock)',
  					`user_id` int(11) unsigned NOT NULL,
  					`ref_val` varchar(100)  NOT NULL DEFAULT '',
  					`ref_type` char(2) NOT NULL DEFAULT 'OR' COMMENT 'radio(PU=Purchase,OR=Order,TS=Transfer Send,TR=Transfer Receive,TC=Transfer Cancelled, TB=Transfer Back,OB=Order Back,PA=Product Add,PE=Product Edit)',
  					`type` char(1) NOT NULL COMMENT 'radio(I=In,O=Out)',
  					`entry_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  					`msg` char(255) CHARACTER SET utf8 NOT NULL DEFAULT '',
  					`prev_stock` int(11)  NOT NULL DEFAULT '0',
  					`stock_val` int(11) unsigned NOT NULL DEFAULT '0',
  					`ex1_param` char(64) NOT NULL DEFAULT '' COMMENT 'extra fields',
  					`ex2_param` char(64) NOT NULL DEFAULT '' COMMENT 'extra fields',  					
  					PRIMARY KEY (`id`)
					)";
			require_once ABSPATH . 'wp-admin/includes/upgrade.php';
			dbDelta( $sql );
		}
	}
}
