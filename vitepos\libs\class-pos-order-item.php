<?php
/**
 * Its pos order-item model
 *
 * @since: 21/09/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package VitePos\Libs
 */

namespace VitePos\Libs;

use VitePos\Modules\POS_Settings;

/**
 * Class Pos Order Item
 *
 * @package VitePos\Libs
 */
class POS_Order_Item {
	/**
	 * Its property product_name
	 *
	 * @var string
	 */
	public $product_name;
	/**
	 * Its property order line item  id
	 *
	 * @var int
	 */
	public $item_id;
	/**
	 * Its property product_id
	 *
	 * @var int
	 */
	public $product_id;
	/**
	 * Its property variation_id
	 *
	 * @var int
	 */
	public $variation_id;
	/**
	 * Its property quantity
	 *
	 * @var int
	 */
	public $quantity = 0;

	/**
	 * Its property quantity
	 *
	 * @var int
	 */
	public $refunded_qty = 0;

	/**
	 * Its property description
	 *
	 * @var string
	 */
	public $description = '';
	/**
	 * Its property image
	 *
	 * @var string
	 */
	public $image = '';
	/**
	 * Its property price
	 *
	 * @var float
	 */
	public $price = 0.0;
	/**
	 * Its property regular_price
	 *
	 * @var float
	 */
	public $regular_price = 0.0;
	/**
	 * Its property tax_amount
	 *
	 * @var float
	 */
	public $tax_amount = 0.0;
	/**
	 * Its property addons
	 *
	 * @var array|mixed|string
	 */
	public $addons;
	/**
	 * Its property addon_total
	 *
	 * @var float
	 */
	public $addon_total = 0.0;
	/**
	 * Its property addon_tax
	 *
	 * @var float
	 */
	public $addon_tax = 0.0;

	/**
	 * The GetProductItem is generated by appsbd
	 *
	 * @param \WC_Order_Item $item Its string.
	 * @param \WC_Order $order Its string.
	 *
	 * @return POS_Order_Item
	 */
	public static function get_product_item( $item, &$order ) {
		$product              = wc_get_product( $item->get_product_id() );
		$o_item               = new POS_Order_Item();
		$o_item->item_id      = $item->get_id();
		$o_item->variation_id = $item->get_variation_id();
		$o_item->attributes   = array();
		$o_item->description  = '';
		$attr_string          = '';
		if ( ! empty( $o_item->variation_id ) ) {
			$vproduct      = null;
			$product_attrs = self::get_item_meta_data( $item, $vproduct );

			foreach ( $product_attrs as $attr ) {
				$attribute            = new \stdClass();
				$attribute->opt_title = $attr->display_key;
				$attribute->opt_slug  = $attr->display_slug;
				$attribute->val_slug  = $attr->display_value_slug;
				$attribute->val_title = $attr->display_value;
				$o_item->attributes[] = $attribute;

				$attr_string         .= $attr->display_value . ',';
				$o_item->description .= '<span>' . $attr->display_key . ' : <b>' . $attr->display_value . '</b></span>';

			}
		}

		$attr_string        = trim( $attr_string, ", \t\n\r\0\x0B" );
		$o_item->product_id = $item->get_product_id();
				$o_item->product_name = $item->get_name( '' ) . ( ! empty( $attr_string ) ? '-' . $attr_string : '' );
		if ( ! empty( $product ) ) {
			$o_item->image = self::get_wc_product_image( $product );
		} else {
			$o_item->image = $item->get_product_id();
		}
		$o_item->quantity   = $item->get_quantity();
		$o_item->refunded_qty = absint($order->get_qty_refunded_for_item( $o_item->item_id ));

		$o_item->addon_tax = (float) $item->get_meta( '_vtp_addon_tax' );
		$o_item->tax_total = (float) $item->get_total_tax();
		if ( $o_item->quantity > 0 && ( $o_item->tax_total > 0 || $o_item->addon_tax > 0 ) ) {
			$o_item->tax_amount = ( ( $o_item->tax_total - $o_item->addon_tax ) / $o_item->quantity );
		}
		$o_item->addons      = $item->get_meta( '_vtp_addons' );
		$o_item->addon_total = (float) $item->get_meta( '_vtp_addon_total' );
		self::set_item_wise_fee_discount( $o_item, $item, $order );


		$include_tax = $order->get_prices_include_tax();
		if ( POS_Settings::is_restaurant_mode() ) {
			if ( $item->meta_exists( '_vtp_items_price' ) ) {
				$o_item->price = (float) $item->get_meta( '_vtp_items_price' );
			} else {
				$o_item->price = ( (float) $order->get_item_subtotal( $item, false, true ) - $o_item->addon_total );
			}
			$o_item->regular_price = ( (float) $item->get_meta( '_vtp_regular_price' ) ) - ( $o_item->addon_total );
			$o_item->qty_rdy       = (float) $item->get_meta( '_vtp_qty_rdy' );
			$o_item->qty_pre       = (float) $item->get_meta( '_vtp_qty_pre' );
			$o_item->qty_srv       = (float) $item->get_meta( '_vtp_qty_srv' );

		} else {
			$o_item->price         = (float) $order->get_item_subtotal( $item, false, true );
			$o_item->regular_price = (float) $item->get_meta( '_vtp_regular_price' );
		}
		if ( $include_tax ) {
			$o_item->regular_price = 0.0;
		}

		return $o_item;
	}

	/**
	 * The set item wise fee discount is generated by appsbd
	 *
	 * @param \WC_Order $order
	 */
	public static function set_item_wise_fee_discount( &$resp_item, $item, $order ) {
		$total_fee = (double) $order->get_meta( '_vtp_fee_total' );
		$total_dis = (double) $order->get_meta( '_vtp_discount_total' );

		$total_dis   = 0 > $total_dis ? ( - 1 ) * $total_dis : $total_dis;
		$total_fee   = 0 > $total_fee ? ( - 1 ) * $total_fee : $total_fee;
		$order_total = $order->get_subtotal();

		$resp_item->discount = 0.0;
		$resp_item->fee      = 0.0;
		if ( 0 < $order_total && ( 0 < $total_dis || 0 < $total_fee ) ) {
			$item_sub_total = (double) $item->get_subtotal();
			if ( $total_dis > 0 ) {
				$dis_amount          = (double) ( $total_dis / $order_total ) * $item_sub_total;
				$resp_item->discount = $dis_amount;
			}
			if ( $total_dis > 0 ) {
				$fee_amount     = (double) ( $total_fee / $order_total ) * $item_sub_total;
				$resp_item->fee = $fee_amount;
			}
		}
		$resp_item->discount_amount = 0.0;
		$resp_item->fee_amount      = 0.0;
		if ( 0 < $resp_item->quantity ) {
			$resp_item->discount_amount = 0 != $resp_item->discount ? $resp_item->discount / $resp_item->quantity : 0;
			$resp_item->fee_amount      = 0 != $resp_item->fee ? $resp_item->fee / $resp_item->quantity : 0;
		}
	}


	/**
	 * The get item meta data is generated by appsbd
	 *
	 * @param mixed  $item Its order item.
	 * @param null   $product Its Order product.
	 * @param string $hideprefix Its hide prefix.
	 *
	 * @return array
	 */
	public static function get_item_meta_data( $item, &$product = null, $hideprefix = '_' ) {
		if ( empty( $product ) ) {
			$product = is_callable( array( $item, 'get_product' ) ) ? $item->get_product() : false;
		}
		$meta_data         = $item->get_meta_data();
		$hideprefix_length = ! empty( $hideprefix ) ? strlen( $hideprefix ) : 0;
		$meta_response     = array();
		foreach ( $meta_data as $meta ) {
			if ( empty( $meta->id ) || '' === $meta->value || ! is_scalar( $meta->value ) || ( $hideprefix_length && substr(
				$meta->key,
				0,
				$hideprefix_length
			) === $hideprefix ) ) {
				continue;
			}
			$itemmeta                     = new \stdClass();
			$attribute_key                = str_replace( 'attribute_', '', $meta->key );
			$itemmeta->display_slug       = $meta->key;
			$itemmeta->display_key        = wc_attribute_label( $attribute_key, $product );
			$itemmeta->display_value_slug = wp_kses_post( $meta->value );
			$itemmeta->display_value      = wp_kses_post( $meta->value );
			if ( taxonomy_exists( $attribute_key ) ) {
				$term = get_term_by( 'slug', $meta->value, $attribute_key );
				if ( ! is_wp_error( $term ) && is_object( $term ) && $term->name ) {
					$itemmeta->display_value = $term->name;
				}
			}
			$meta_response[] = $itemmeta;
		}
		return $meta_response;
	}

	/**
	 * The get wc product image is generated by appsbd
	 *
	 * @param any    $product Its string.
	 * @param string $size Its string.
	 *
	 * @return false|string Its string.
	 */
	private static function get_wc_product_image( $product, $size = 'woocommerce_thumbnail' ) {
		 $image = '';
		if ( $product->get_image_id() ) {
			$image = wp_get_attachment_image_url( $product->get_image_id(), $size );
		} elseif ( $product->get_parent_id() ) {
			$parent_product = wc_get_product( $product->get_parent_id() );
			if ( $parent_product ) {
				$image = wp_get_attachment_image_url( $parent_product->get_image_id(), $size );
			}
		} else {
			$image = wc_placeholder_img_src( $size );

		}
		return $image;
	}
}
