<?php
/**
 * Pos Purchase Database Model
 *
 * @package Vitepos\Models\Database
 */

namespace Vitepos\Models\Database;

use VitePos\Core\ViteposModel;
use VitePos\Libs\POS_Product;

/**
 * Class Mapbd_pos_purchase
 *
 * @properties id,vendor_id,warehouse_id,grand_total,status,payment_status,order_tax,tax_type,discount,discount_type,shipping_cost,other_expense,purchase_note,purchase_date,added_by
 */
class Mapbd_Pos_Purchase extends ViteposModel {
	/**
	 * Its property id
	 *
	 * @var int
	 */
	public $id;
	/**
	 * Its property vendor_id
	 *
	 * @var int
	 */
	public $vendor_id;
	/**
	 * Its property warehouse_id
	 *
	 * @var int
	 */
	public $warehouse_id;
	/**
	 * Its property grand_total
	 *
	 * @var float
	 */
	public $grand_total;
	/**
	 * Its property status
	 *
	 * @var bool
	 */
	public $status;
	/**
	 * Its property payment_status
	 *
	 * @var bool
	 */
	public $payment_status;
	/**
	 * Its property order_tax
	 *
	 * @var string
	 */
	public $order_tax;
	/**
	 * Its property tax_type
	 *
	 * @var string
	 */
	public $tax_type;
	/**
	 * Its property tax_total
	 *
	 * @var float
	 */
	public $tax_total;
	/**
	 * Its property discount
	 *
	 * @var float
	 */
	public $discount;
	/**
	 * Its property discount_type
	 *
	 * @var string
	 */
	public $discount_type;
	/**
	 * Its property discount_total
	 *
	 * @var float
	 */
	public $discount_total;
	/**
	 * Its property shipping_cost
	 *
	 * @var float
	 */
	public $shipping_cost;
	/**
	 * Its property total_item
	 *
	 * @var int
	 */
	public $total_item;
	/**
	 * Its property total_quantity
	 *
	 * @var int
	 */
	public $total_quantity;
	/**
	 * Its property other_expense
	 *
	 * @var float
	 */
	public $other_expense;
	/**
	 * Its property purchase_note
	 *
	 * @var string
	 */
	public $purchase_note;
	/**
	 * Its property purchase_date
	 *
	 * @var Date
	 */
	public $purchase_date;
	/**
	 * Its property added_by
	 *
	 * @var string
	 */
	public $added_by;


	/**
	 * Mapbd_pos_purchase constructor.
	 */
	public function __construct() {
		parent::__construct();
		$this->set_validation();
		$this->table_name     = 'apbd_pos_purchase';
		$this->primary_key    = 'id';
		$this->unique_key     = array();
		$this->multi_key      = array();
		$this->auto_inc_field = array( 'id' );
		$this->app_base_name  = 'apbd-elite-pos';
		$this->date_fields    = array( 'purchase_date' );

	}


	/**
	 * The set validation is generated by appsbd
	 */
	public function set_validation() {
		$this->validations = array(
			'id'             => array(
				'Text' => 'Id',
				'Rule' => 'max_length[11]|integer',
			),
			'vendor_id'      => array(
				'Text' => 'Vendor',
				'Rule' => 'required|max_length[11]|integer',
			),
			'warehouse_id'   => array(
				'Text' => 'Outlet',
				'Rule' => 'required|max_length[11]|integer',
			),
			'grand_total'    => array(
				'Text' => 'Grand Total',
				'Rule' => 'max_length[12]|numeric',
			),
			'status'         => array(
				'Text' => 'Status',
				'Rule' => 'max_length[1]',
			),
			'payment_status' => array(
				'Text' => 'Payment Status',
				'Rule' => 'max_length[1]',
			),
			'order_tax'      => array(
				'Text' => 'Order Tax',
				'Rule' => 'max_length[7]|numeric',
			),
			'tax_type'       => array(
				'Text' => 'Tax Type',
				'Rule' => 'max_length[1]',
			),
			'tax_total'      => array(
				'Text' => 'Tax Total',
				'Rule' => 'max_length[11]|numeric',
			),
			'discount'       => array(
				'Text' => 'Discount',
				'Rule' => 'max_length[11]|numeric',
			),
			'discount_type'  => array(
				'Text' => 'Discount Type',
				'Rule' => 'max_length[1]',
			),
			'discount_total' => array(
				'Text' => 'Discount Total',
				'Rule' => 'max_length[11]|numeric',
			),
			'shipping_cost'  => array(
				'Text' => 'Shipping Cost',
				'Rule' => 'max_length[11]|numeric',
			),
			'other_expense'  => array(
				'Text' => 'Other Expense',
				'Rule' => 'max_length[11]|numeric',
			),
			'purchase_date'  => array(
				'Text' => 'Purchase Date',
				'Rule' => 'max_length[20]',
			),
			'added_by'       => array(
				'Text' => 'Added By',
				'Rule' => 'max_length[11]|integer',
			),

		);
	}


	/**
	 * The get property raw options is generated by appsbd
	 *
	 * @param \Appsbd\V1\Core\any $property Its sting.
	 * @param false               $is_with_select Its bool.
	 *
	 * @return array|string[]
	 */
	public function get_property_raw_options( $property, $is_with_select = false ) {
		$return_obj = array();
		switch ( $property ) {
			case 'status':
				$return_obj = array(
					'A' => 'Active',
					'I' => 'Inactive',
				);
				break;
			case 'payment_status':
				$return_obj = array(
					'P' => 'Paid',
					'U' => 'Unpaid',
				);
				break;
			case 'tax_type':
				$return_obj = array(
					'P' => 'Percentage',
					'A' => 'Amount',
				);
				break;
			case 'discount_type':
				$return_obj = array(
					'P' => 'Percentage',
					'A' => 'Amount',
				);
				break;
			default:
		}
		if ( $is_with_select ) {
			return array_merge( array( '' => 'Select' ), $return_obj );
		}
		return $return_obj;

	}

	/**
	 * The get property options color is generated by appsbd
	 *
	 * @param \Appsbd\V1\Core\any $property Its string.
	 *
	 * @return array|string[]
	 */
	public function get_property_options_color( $property ) {
		$return_obj = array();
		switch ( $property ) {
			case 'status':
				$return_obj = array(
					'A' => 'success',
					'I' => 'danger',
				);
				break;
			case 'payment_status':
				$return_obj = array(
					'P' => 'info',
					'U' => 'success',
				);
				break;
			case 'tax_type':
				$return_obj = array(
					'P' => 'info',
					'A' => 'success',
				);
				break;
			case 'discount_type':
				$return_obj = array(
					'P' => 'info',
					'A' => 'success',
				);
				break;
			default:
		}
		return $return_obj;

	}

	/**
	 * The get property options icon is generated by appsbd
	 *
	 * @param \Appsbd\V1\Core\any $property Its string.
	 *
	 * @return array|string[]
	 */
	public function get_property_options_icon( $property ) {
		$return_obj = array();
		switch ( $property ) {
			case 'status':
				$return_obj = array(
					'A' => 'fa fa-check-circle-o',
					'I' => 'fa fa-times-circle-o',
				);
				break;
			case 'payment_status':
				$return_obj = array(
					'P' => 'fa fa-hourglass-1',
					'U' => '',
				);
				break;
			case 'tax_type':
				$return_obj = array(
					'P' => 'fa fa-hourglass-1',
					'A' => 'fa fa-check-circle-o',
				);
				break;
			case 'discount_type':
				$return_obj = array(
					'P' => 'fa fa-hourglass-1',
					'A' => 'fa fa-check-circle-o',
				);
				break;
			default:
		}
		return $return_obj;

	}

	/**
	 * The create db table is generated by appsbd
	 */
	public static function create_db_table() {
		$this_obj = new static();
		$table    = $this_obj->db->prefix . $this_obj->table_name;
		if ( $this_obj->db->get_var( "show tables like '{$table}'" ) != $table ) {
			$sql = "CREATE TABLE `{$table}` (
					  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
					  `vendor_id` int(11) unsigned NOT NULL COMMENT 'FK(wp_apbd_pos_vendor,id,name)',
					  `warehouse_id` int(11) unsigned NOT NULL COMMENT 'FK(wp_apbd_pos_warehouse,id,name)',
					  `grand_total` decimal(11,2) unsigned NOT NULL DEFAULT 0.00,
					  `status` char(1) NOT NULL DEFAULT 'A' COMMENT 'radio(A=Active,I=Inactive)',
					  `payment_status` char(1) NOT NULL DEFAULT 'P' COMMENT 'radio(P=Paid,U=Unpaid)',
					  `order_tax` decimal(6,2) unsigned NOT NULL DEFAULT 0.00,
					  `tax_type` char(1) NOT NULL DEFAULT 'P' COMMENT 'radio(P=Percentage,A=Amount)',
					  `tax_total` decimal(6,2) unsigned NOT NULL DEFAULT 0.00,
					  `discount` decimal(10,2) unsigned NOT NULL DEFAULT 0.00,
					  `discount_type` char(1) NOT NULL DEFAULT 'A' COMMENT 'radio(P=Percentage,A=Amount)',
					  `discount_total` decimal(10,2) unsigned NOT NULL DEFAULT 0.00,
					  `shipping_cost` decimal(10,2) unsigned NOT NULL DEFAULT 0.00,
					  `other_expense` decimal(10,2) unsigned NOT NULL DEFAULT 0.00,
					  `purchase_note` text NOT NULL DEFAULT '' COMMENT 'textarea',
					  `purchase_date` timestamp NOT NULL DEFAULT current_timestamp(),
					  `added_by` int(11) unsigned DEFAULT NULL,
					  `total_item` int(10) unsigned NOT NULL,
  					  `total_quantity` int(11) NOT NULL,
					  PRIMARY KEY (`id`)
					) ";
			require_once ABSPATH . 'wp-admin/includes/upgrade.php';
			dbDelta( $sql );
		}
	}

	/**
	 * The updated price product counter is generated by appsbd
	 *
	 * @return mixed Its mixed param.
	 */
	public static function updated_price_product_counter() {
		$src_by           = array();
		$src_by[]         = array(
			'prop' => '_vt_purchase_price_change',
			'val'  => 'Y',
			'opr'  => 'eq',
		);
		$response_product = POS_Product::get_product_from_woo_products_with_variations( 1, 1000, $src_by );
		return $response_product->records;
	}
}
