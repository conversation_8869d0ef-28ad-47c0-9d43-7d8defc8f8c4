<?php
/**
 * Its for Pos Warehouse module
 *
 * @package VitePos\Modules
 */

namespace VitePos\Modules;

use Appsbd\V1\Core\BaseModule;
use Appsbd\V1\libs\Ajax_Confirm_Response;
use Appsbd\V1\libs\Ajax_Data_Response;
use Appsbd\V1\libs\Ajax_Response;
use Appsbd\V1\libs\AppInput;
use Vitepos\Models\Database\Mapbd_Pos_Custom_Field;

/**
 * Class Apbd_pos_warehouse
 */
class POS_Custom_Field extends BaseModule {
	/**
	 * The initialize is generated by appsbd
	 */
	public function initialize() {

		 $this->add_ajax_action( 'add', array( $this, 'add' ) );
		 $this->add_ajax_action( 'edit', array( $this, 'edit' ) );
		 $this->add_ajax_action( 'delete', array( $this, 'delete' ) );
		 $this->add_ajax_action( 'details', array( $this, 'details' ) );
		 $this->add_ajax_action( 'change-status', array( $this, 'status_change' ) );
	}



	/**
	 * The OptionForm is generated by appsbd
	 */
	public function option_form() {
		$this->set_title( 'Custom Field List' );
		$this->set_subtitle( '' );
		$this->display();
	}

	/**
	 * The on init is generated by appsbd
	 */
	public function on_init() {
		parent::on_init();
	}

	/**
	 * The get menu title is generated by appsbd
	 *
	 * @return mixed Its mixed.
	 */
	public function get_menu_title() {
		return $this->__( 'Custom Field List' );
	}
	/**
	 * The get menu sub title is generated by appsbd
	 *
	 * @return mixed Its mixed.
	 */
	public function get_menu_sub_title() {
		return $this->__( 'Custom Field List' );
	}

	/**
	 * The get menu icon is generated by appsbd
	 *
	 * @return string Its string.
	 */
	public function get_menu_icon() {
		return 'aps aps-shop';
	}

	/**
	 * The add is generated by appsbd
	 */
	public function add() {
		$response = new Ajax_Confirm_Response();
		if ( APPSBD_IS_POST_BACK ) {
			$nobject = new Mapbd_Pos_Custom_Field();
			if ( $nobject->set_from_post_data( true ) ) {
				if ( $nobject->is_set_prperty( 'options' ) ) {
					$nobject->options( serialize( $nobject->options ) );
				}

				if ( $nobject->save() ) {
					$this->add_info( 'Successfully added' );
					$response->display_with_response( true );
					return;
				}
			} else {
				$this->add_error( 'Invalid request' );
				$response->display_with_response( false );
				return;
			}
		}
	}

	/**
	 * The edit outlet is generated by appsbd
	 */
	public function edit() {
		$response = new Ajax_Confirm_Response();
		$id       = AppInput::post_value( 'id' );
		if ( empty( $id ) ) {
			$this->add_error( 'Invalid request' );
			$response->display_with_response( false );
			return;
		}
		if ( APPSBD_IS_POST_BACK ) {
			$uobject = new Mapbd_Pos_Custom_Field();
			if ( $uobject->set_from_post_data( false ) ) {
				$propes = 'label,type,is_half_field,is_required,status,help_text,is_calculable,show_where,options,operator';
				if ( $uobject->is_set_prperty( 'options' ) ) {
					$uobject->options( serialize( $uobject->options ) );
				}
				if ( $uobject->unset_all_excepts( $propes ) ) {
					$uobject->set_where_update( 'id', $id );
					if ( $uobject->Update() ) {
						$this->add_info( 'Successfully updated' );
						$response->display_with_response( true );
						return;
					} else {
						$this->add_error( 'Update Failed' );
						$response->display_with_response( false );
						return;
					}
				} else {
					$this->add_error( 'This Property Should not be update' );
					$response->display_with_response( false );
					return;
				}
			} else {
				$this->add_error( 'Invalid request' );
				$response->display_with_response( false );
				return;
			}
		} else {
			$this->add_error( 'Invalid request' );
			$response->display_with_response( false );
			return;
		}
	}


	/**
	 * The view warehouse details is generated by appsbd
	 */
	public function details() {
		$response = new Ajax_Confirm_Response();
		$id       = AppInput::post_value( 'id' );
		if ( empty( $id ) ) {
			$this->add_error( 'Invalid request' );
			$response->display_with_response( false );
			return;
		}
		$propes  = 'id,label,type,is_half_field,is_required,status,help_text,is_calculable,show_where,options,operator';
		$details = new Mapbd_Pos_Custom_Field();
		$details->id( $id );
		if ( $details->select() ) {
			$response->display_with_response( true, $details->get_properties_api_response( $propes ) );
		} else {
			$this->add_error( 'No custom field found with these ids' );
			$response->display_with_response( false, null );
		}
	}


	/**
	 * The data is generated by appsbd
	 */
	public function data() {
		$main_response = new Ajax_Data_Response();
		$response      = new Ajax_Response();
		$mainobj       = new Mapbd_Pos_Custom_Field();
		$mainobj->set_search_by_param( $main_response->src_by, 'label' );
		$mainobj->set_sort_by_param( $main_response->sort_by );
		$records         = $mainobj->count_aLL( $main_response->src_item, $main_response->src_text, $main_response->multiparam, 'after' );
		$set_data        = new \stdClass();
		$set_data->types = $mainobj->get_property_raw_options( 'type' );
		if ( $records > 0 ) {
			$main_response->set_grid_records( $records );
			$result = $mainobj->select_all_grid_data(
				'id,label,type,status,show_where',
				'',
				'',
				$main_response->limit,
				$main_response->limit_start(),
				$main_response->src_item,
				$main_response->src_text,
				$main_response->multiparam,
				'after'
			);
			$main_response->set_grid_data( $result );
		}
		$set_data->data = $main_response->final_response;
		$response->set_response( true, $set_data );
		$response->display();
	}
	/**
	 * The delete item is generated by appsbd
	 */
	public function delete() {
		$param         = AppInput::post_value( 'id' );
		$main_response = new Ajax_Confirm_Response();
		if ( empty( $param ) ) {
			$this->add_error( 'Invalid Request' );
			$main_response->display_with_response( false );

			return;
		}
		$mr = new Mapbd_Pos_Custom_Field();
		$mr->id( $param );
		if ( $mr->select() ) {
			if ( Mapbd_Pos_Custom_Field::delete_by_id( $param ) ) {
				$this->add_info( 'Successfully deleted' );
				$main_response->display_with_response( true );
			} else {
				$this->add_error( 'Delete failed try again' );
				$main_response->display_with_response( false );
			}
		}
	}
	/**
	 * The status change is generated by appsbd
	 */
	public function status_change() {
		$param         = AppInput::post_value( 'id' );
		$main_response = new Ajax_Confirm_Response();
		if ( empty( $param ) ) {
			$this->add_error( 'Invalid Request' );
			$main_response->display_with_response( false );
			return;
		}
		$mr = new Mapbd_Pos_Custom_Field();
		$mr->id( $param );
		if ( $mr->select( 'status' ) ) {
			$status = 'A';
			if ( 'A' == $mr->status ) {
				$status = 'I';
			}
			$uo = new Mapbd_Pos_Custom_Field();
			$uo->status( $status );
			$uo->set_where_update( 'id', $param );
			if ( $uo->Update() ) {
				$this->add_info( 'Status changed successfully' );
				$main_response->display_with_response( true );
			} else {
				$this->add_error( 'Status change failed,please try again' );
				$main_response->display_with_response( false );
			}
		}
	}
}
