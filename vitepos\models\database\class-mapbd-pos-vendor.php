<?php
/**
 * Pos Vendor Database Model
 *
 * @package Vitepos\Models\Database
 */

namespace Vitepos\Models\Database;

use VitePos\Core\ViteposModel;

/**
 * Class Mapbd_pos_vendor
 *
 * @properties id,name,email,contact_no,vendor_note,status,added_by
 */
class Mapbd_Pos_Vendor extends ViteposModel {
	/**
	 * Its property id
	 *
	 * @var int
	 */
	public $id;
	/**
	 * Its property name
	 *
	 * @var string
	 */
	public $name;
	/**
	 * Its property email
	 *
	 * @var string
	 */
	public $email;
	/**
	 * Its property contact_no
	 *
	 * @var int
	 */
	public $contact_no;
	/**
	 * Its property vendor_note
	 *
	 * @var string
	 */
	public $vendor_note;
	/**
	 * Its property status
	 *
	 * @var bool
	 */
	public $status;
	/**
	 * Its property added_by
	 *
	 * @var string
	 */
	public $added_by;


	/**
	 * Mapbd_pos_vendor constructor.
	 */
	public function __construct() {
		parent::__construct();
		$this->set_validation();
		$this->table_name     = 'apbd_pos_vendor';
		$this->primary_key    = 'id';
		$this->unique_key     = array();
		$this->multi_key      = array();
		$this->auto_inc_field = array( 'id' );
		$this->app_base_name  = 'apbd-elite-pos';

	}


	/**
	 * The set validation is generated by appsbd
	 */
	public function set_validation() {
		$this->validations = array(
			'id'         => array(
				'Text' => 'Id',
				'Rule' => 'max_length[11]|integer',
			),
			'name'       => array(
				'Text' => 'Name',
				'Rule' => 'max_length[100]',
			),
			'email'      => array(
				'Text' => 'Email',
				'Rule' => 'max_length[150]|valid_email',
			),
			'contact_no' => array(
				'Text' => 'Mobile Number',
				'Rule' => 'max_length[150]',
			),
			'status'     => array(
				'Text' => 'Status',
				'Rule' => 'max_length[1]',
			),
			'added_by'   => array(
				'Text' => 'Added By',
				'Rule' => 'required|max_length[11]|integer',
			),

		);
	}

	/**
	 * The get property raw options is generated by appsbd
	 *
	 * @param \Appsbd\V1\Core\any $property Its string.
	 * @param false               $is_with_select Its bool.
	 *
	 * @return array|string[]
	 */
	public function get_property_raw_options( $property, $is_with_select = false ) {
		$return_obj = array();
		switch ( $property ) {
			case 'status':
				$return_obj = array(
					'A' => 'Active',
					'I' => 'Inactive',
				);
				break;
			default:
		}
		if ( $is_with_select ) {
			return array_merge( array( '' => 'Select' ), $return_obj );
		}
		return $return_obj;

	}

	/**
	 * The create db table is generated by appsbd
	 */
	public static function create_db_table() {
		$this_obj = new static();
		$table    = $this_obj->db->prefix . $this_obj->table_name;
		if ( $this_obj->db->get_var( "show tables like '{$table}'" ) != $table ) {
			$sql = "CREATE TABLE `{$table}` (
					  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
					  `name` char(100) NOT NULL DEFAULT '',
					  `email` char(150) NOT NULL DEFAULT '',
					  `contact_no` char(150) NOT NULL DEFAULT '',
					  `vendor_note` text DEFAULT NULL COMMENT 'textarea',
					  `status` char(1) NOT NULL DEFAULT 'A' COMMENT 'bool(A=Active,I=Inactive)',
					  `added_by` int(11) unsigned NOT NULL,
					  PRIMARY KEY (`id`)
					) ";
			require_once ABSPATH . 'wp-admin/includes/upgrade.php';
			dbDelta( $sql );
		}
	}

	/**
	 * The delete by id is generated by appsbd
	 *
	 * @param any $id Its vendor id param.
	 *
	 * @return bool
	 */
	public static function delete_by_id( $id ) {
		return parent::delete_by_key_value( 'id', $id );
	}

}
