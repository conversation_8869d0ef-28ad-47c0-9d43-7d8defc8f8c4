<?php
/**
 * Pos Vendor Database Model
 *
 * @package Vitepos\Models\Database
 */

namespace Vitepos\Models\Database;

use VitePos\Core\ViteposModel;

/**
 * Class Mapbd_pos_vendor
 *
 * @properties id,name,email,contact_no,vendor_note,status,added_by
 */
class Mapbd_Pos_Addon extends ViteposModel {
	/**
	 * Its property id
	 *
	 * @var int
	 */
	public $id;
	/**
	 * Its property title
	 *
	 * @var string
	 */
	public $title;
	/**
	 * Its property status
	 *
	 * @var bool
	 */
	public $status;
	/**
	 * Its property added_by
	 *
	 * @var string
	 */
	public $added_by;


	/**
	 * Mapbd_pos_vendor constructor.
	 */
	public function __construct() {
		parent::__construct();
		$this->set_validation();
		$this->table_name     = 'apbd_pos_addon';
		$this->primary_key    = 'id';
		$this->unique_key     = array();
		$this->multi_key      = array();
		$this->auto_inc_field = array( 'id' );
		$this->app_base_name  = 'apbd-elite-pos';

	}


	/**
	 * The set validation is generated by appsbd
	 */
	public function set_validation() {
		$this->validations = array(
			'id'       => array(
				'Text' => 'Id',
				'Rule' => 'max_length[11]|integer',
			),
			'title'    => array(
				'Text' => 'Title',
				'Rule' => 'required|max_length[255]',
			),
			'status'   => array(
				'Text' => 'Status',
				'Rule' => 'max_length[1]',
			),
			'added_by' => array(
				'Text' => 'Added By',
				'Rule' => 'required|max_length[11]|integer',
			),

		);
	}

	/**
	 * The get property raw options is generated by appsbd
	 *
	 * @param \Appsbd\V1\Core\any $property Its string.
	 * @param false               $is_with_select Its bool.
	 *
	 * @return array|string[]
	 */
	public function get_property_raw_options( $property, $is_with_select = false ) {
		$return_obj = array();
		switch ( $property ) {
			case 'status':
				$return_obj = array(
					'A' => 'Active',
					'I' => 'Inactive',
				);
				break;
			default:
		}
		if ( $is_with_select ) {
			return array_merge( array( '' => 'Select' ), $return_obj );
		}

		return $return_obj;

	}

	/**
	 * The create db table is generated by appsbd
	 */
	public static function create_db_table() {
		$this_obj = new static();
		$table    = $this_obj->db->prefix . $this_obj->table_name;
		if ( $this_obj->db->get_var( "show tables like '{$table}'" ) != $table ) {
			$sql = "CREATE TABLE `{$table}` (
					  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
					  `title` char(255) NOT NULL DEFAULT '',
					  `status` char(1) NOT NULL DEFAULT 'A' COMMENT 'bool(A=Active,I=Inactive)',
					  `added_by` int(11) unsigned NOT NULL,
					  PRIMARY KEY (`id`)
					) ";
			require_once ABSPATH . 'wp-admin/includes/upgrade.php';
			dbDelta( $sql );
		}
	}

	/**
	 * The delete by id is generated by appsbd
	 *
	 * @param any $id Its vendor id param.
	 *
	 * @return bool
	 */
	public static function delete_by_id( $id ) {
		if ( parent::delete_by_key_value( 'id', $id ) ) {
			Mapbd_Pos_Addon_Field::delete_by_addon_id( $id );
			Mapbd_Pos_Addon_Rule_Group::delete_by_addon_id( $id );

			return true;
		}

		return false;
	}

	/**
	 * The get all addons is generated by appsbd
	 *
	 * @param string $status Its a status parem.
	 *
	 * @return array|false|Mapbd_Pos_Addon[]
	 */
	public static function get_all_addons( $status = 'A' ) {
		if ( ! empty( $status ) ) {
			$addons = self::find_all_grid_data_by( 'status', $status );
		} else {
			$addons = self::fetch_all_grid_data();
		}
		foreach ( $addons as &$addon ) {
			$addon->fields      = Mapbd_Pos_Addon_Field::get_all_field_by( $addon->id );
			$addon->rule_groups = Mapbd_Pos_Addon_Rule_Group::get_all_rule_groups_by( $addon->id );
		}
		return $addons;
	}
}
