<?php
/**
 * Pos Warehouse Database Model
 *
 * @package Vitepos\Models\Database
 */

namespace Vitepos\Models\Database;

use VitePos\Core\ViteposModel;

/**
 * Class Mapbd_pos_warehouse
 *
 * @properties meta_id,post_id,meta_key,meta_value
 */
class Mapbd_Postmeta extends ViteposModel {
	/**
	 * Its property meta_id
	 *
	 * @var int
	 */
	public $meta_id;
	/**
	 * Its property post_id
	 *
	 * @var int
	 */
	public $post_id;
	/**
	 * Its property meta_key
	 *
	 * @var string
	 */
	public $meta_key;
	/**
	 * Its property meta_value
	 *
	 * @var mixed
	 */
	public $meta_value;


	/**
	 * Mapbd_pos_warehouse constructor.
	 */
	public function __construct() {
		parent::__construct();
		$this->set_validation();
		$this->table_name     = 'postmeta';
		$this->primary_key    = 'meta_id';
		$this->unique_key     = array();
		$this->multi_key      = array( array( 'post_id' ), array( 'meta_key' ) );
		$this->auto_inc_field = array( 'meta_id' );
		$this->app_base_name  = 'apbd-vite-pos';

	}


	/**
	 * The set validation is generated by appsbd
	 */
	public function set_validation() {
		$this->validations = array(
			'meta_id'    => array(
				'Text' => 'Meta Id',
				'Rule' => 'max_length[20]',
			),
			'post_id'    => array(
				'Text' => 'Post Id',
				'Rule' => 'max_length[20]',
			),
			'meta_key'   => array(
				'Text' => 'Meta Key',
				'Rule' => 'max_length[255]',
			),
			'meta_value' => array(
				'Text' => 'Meta Value',
				'Rule' => '',
			),

		);
	}


	/**
	 * The create db table is generated by appsbd
	 */
	public static function create_db_table() {
		return;
	}

	/**
	 * The drop db table is generated by appsbd
	 */
	public function drop_db_table() {
		return;
	}

	/**
	 * The DeleteById is generated by appsbd
	 *
	 * @param any $id Its integer.
	 *
	 * @return bool
	 */
	public static function delete_by_id( $id ) {
		return self::delete_by_key_value( 'id', $id );
	}
}
