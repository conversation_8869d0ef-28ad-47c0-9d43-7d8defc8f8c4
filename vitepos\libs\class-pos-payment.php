<?php
/**
 * Its pos order model
 *
 * @since: 21/09/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package VitePos\Libs
 */

namespace VitePos\Libs;

use Matrix\Exception;
use Vitepos\Models\Database\Mapbd_Pos_Cash_Drawer;
use Vitepos\Models\Database\Mapbd_Pos_Cash_Drawer_Types;
use Vitepos\Models\Database\Mapbd_Pos_Role;
use Vitepos\Models\Database\Mapbd_Pos_Stock_Log;
use Vitepos\Models\Database\Mapbd_Pos_Warehouse;
use VitePos\Modules\POS_Settings;

/**
 * Class POS_Payment
 *
 * @package VitePos\Libs
 */
class POS_Payment {
	/**
	 * Its property data
	 *
	 * @var array
	 */
	protected $data;
	/**
	 * Its property is_checkout
	 *
	 * @var bool|mixed
	 */
	protected $is_checkout = true;
	/**
	 * Its property is_offline
	 *
	 * @var bool|mixed
	 */
	protected $is_offline = false;
	/**
	 * Its property current_user
	 *
	 * @var null
	 */
	protected $current_user = null;
	/**
	 * Its property given_amount
	 *
	 * @var float
	 */
	protected $given_amount = 0.0;
	/**
	 * Its property grand_total
	 *
	 * @var float
	 */
	protected $grand_total = 0.0;
	/**
	 * Its property order
	 *
	 * @var \WC_Order
	 */
	protected $order;
	/**
	 * Its property order_id
	 *
	 * @var string
	 */
	protected $order_id = '';
	/**
	 * Its property user_discount
	 *
	 * @var array|float
	 */
	protected $user_discount;
	/**
	 * Its property discount_total
	 *
	 * @var float
	 */
	protected $discount_total;
	/**
	 * Its property discount
	 *
	 * @var float
	 */
	protected $discount;
	/**
	 * Its property is_restaurant
	 *
	 * @var bool
	 */
	protected $is_restaurant = false;
	/**
	 * Its property order_details
	 *
	 * @var object
	 */
	protected $order_details;

	/**
	 * Its property order_details
	 *
	 * @var array
	 */
	protected $current_stocks = array();


	/**
	 * Its property outlet_obj
	 *
	 * @var Mapbd_Pos_Warehouse|null
	 */
	protected $outlet_obj;
	/**
	 * Its property counter_id
	 *
	 * @var int
	 */
	private $counter_id;
	/**
	 * Its property outlet_id
	 *
	 * @var int
	 */
	private $outlet_id;
	/**
	 * Its property is_need_to_update_stock
	 *
	 * @var bool
	 */
	private $is_need_to_update_stock = false;

	/**
	 * POS_Payment constructor.
	 *
	 * @param array $data Its data param.
	 * @param int   $outlet_id Its outlet_id param.
	 * @param int   $counter_id Its counter_id param.
	 */
	public function __construct( $data, $outlet_id, $counter_id ) {
		$this->payload      = $data;
		$this->outlet_id    = $outlet_id;
		$this->counter_id   = $counter_id;
		$this->outlet_obj   = Mapbd_Pos_Warehouse::find_by( 'id', $this->outlet_id );
		$this->current_user = wp_get_current_user();
		if ( POS_Settings::is_stockable() ) {
						add_filter( 'woocommerce_payment_complete_reduce_order_stock', '__return_false' );
		}
	}

	/**
	 * The set outlet is generated by appsbd
	 *
	 * @param any $outlet_id its outlet id param.
	 * @param any $counter_id Its counter id param.
	 */
	public function set_outlet( $outlet_id, $counter_id ) {
		$this->outlet_id  = $outlet_id;
		$this->counter_id = $counter_id;
		$this->outlet_obj = Mapbd_Pos_Warehouse::find_by( 'id', $this->outlet_id );
	}

	/**
	 * Its destruct method.
	 */
	public function __destruct() {
		if ( POS_Settings::is_stockable() ) {
						remove_filter( 'woocommerce_payment_complete_reduce_order_stock', '__return_false' );
		}
	}

	/**
	 * The get order details is generated by appsbd
	 *
	 * @return object
	 */
	public function get_order_details() {
		if ( empty( $this->order_details ) ) {
			$obj                 = new \stdClass();
			$obj->is_complete    = 'N';
			$obj->next           = '';
			$obj->data           = null;
			$obj->order          = null;
			$obj->is_stock       = false;
			$obj->current_stock  = array();
			$this->order_details = $obj;
		}
		if ( POS_Settings::is_stockable() && $this->is_need_to_update_stock ) {
			$this->order_details->is_stock      = true;
			$this->order_details->current_stock = $this->current_stocks;
		} else {
			$this->order_details->is_stock      = false;
			$this->order_details->current_stock = array();
		}
		if ( 'SE' == $obj->next && ! POS_Settings::is_enable_customer_email() ) {
			$obj->next = '';
		}
		return $this->order_details;
	}

	/**
	 * The GetPayload is generated by appsbd
	 *
	 * @param mixed  $key Its string.
	 * @param string $default Its null.
	 *
	 * @return mixed|string|null Its string.
	 */
	public function get_payload( $key, $default = null ) {
		return ! empty( $this->payload[ $key ] ) ? $this->payload[ $key ] : $default;
	}


	/**
	 * The create order is generated by appsbd
	 *
	 * @return bool
	 * @throws \WC_Data_Exception If data not saved.
	 */
	private function create_order() {
		if ( ! $this->check_checkout_discount_fee_limit() ) {
			return false;
		}
		$grand_total = (float) $this->get_payload( 'grand_total', 0.0 );
		if ( ! empty( $this->payload['items'] ) ) {
			$billing_address = $this->get_outlet_address();
			$customer_id = $this->get_payload(
				'customer',
				POS_Settings::get_module_option( 'pos_customer', null )
			);
			$order_arg       = array();
			if ( ! empty( $customer_id ) ) {
				$order_arg['customer_id'] = $customer_id;
			}
						$this->order = wc_create_order( $order_arg );

			/**
			 * Its for check is there any change before process
			 *
			 * @param $billing address
			 * @param $order \WC_Order Object
			 * @param $order_arg customer data
			 *
			 * @since 1.0
			 */
			$billing_address = apply_filters(
				'vitepos/filter/billing-address',
				$billing_address,
				$this->order,
				$customer_id
			);
			$this->set_address( $billing_address, 'billing' );

			file_put_contents(WP_CONTENT_DIR."/billing.txt",print_r($billing_address,true),FILE_APPEND);
			$total_tax = 0.0;

			foreach ( $this->payload['items'] as $item ) {
				$arguments = array(
					'total_tax' => $item['tax_amount'] * $item['quantity'],

				);
				try {
					$item_regular_price = 0.0;
					$item_sale_price    = 0.0;
					$item_price         = 0.0;
					if ( ! empty( $item['variation_id'] ) ) {
						if ( ! empty( $item['attributes'] ) && is_array( $item['attributes'] ) ) {
							$arguments ['variation'] = array();
							foreach ( $item['attributes'] as $attribute ) {
								$attribute                                       = (object) $attribute;
								$arguments ['variation'][ $attribute->opt_slug ] = $attribute->val_slug;
							}
						} else {
							$arguments ['variation'] = vitepos_get_product_variation_attributes( $item['variation_id'] );
						}

						$arguments ['name'] = wc_get_product( $item['product_id'] )->get_name();
						$product            = new \WC_Product_Variation( $item['variation_id'] );
						if ( ! empty( $item['addon_total'] ) ) {
							$item_regular_price = floatval( $product->get_regular_price( '' ) );
							$item_sale_price    = floatval( $product->get_sale_price( '' ) );
							$item_price         = floatval( $product->get_price( '' ) );
							$price              = $item_price + floatval( $item['addon_total'] );
							$product->set_price( $price );
							$product->set_regular_price( $item_regular_price );
						}
						$item_id = $this->order->add_product(
							$product,
							$item['quantity'],
							$arguments
						); 					} else {
						$product = wc_get_product( $item['product_id'] );
						if ( ! empty( $item['addon_total'] ) ) {
							$item_regular_price = floatval( $product->get_regular_price( '' ) );
							$item_sale_price    = floatval( $product->get_sale_price( '' ) );
							$item_price         = floatval( $product->get_price( '' ) );
							$price              = $item_price + floatval( $item['addon_total'] );
							$product->set_price( $price );
							$product->set_regular_price( $item_regular_price );
						}
						$item_id = $this->order->add_product(
							$product,
							$item['quantity'],
							$arguments
						); 					}
					$total_tax += ( $item['quantity'] * ( $item['tax_amount'] + $item['addon_total'] ) );
					$oitem      = new \WC_Order_Item_Product( $item_id );
					if ( ! empty( $item['attributes'] ) && is_array( $item['attributes'] ) ) {
						$oitem->add_meta_data( '_vtp_attributes', $item['attributes'] );
					}

					if ( ! empty( $item_regular_price ) ) {
						if ( ! empty( $item['addon_total'] ) ) {
							$oitem->add_meta_data(
								'_vtp_regular_price',
								$item_regular_price + floatval( $item['addon_total'] )
							);
						}
					} else {
						$oitem->add_meta_data( '_vtp_regular_price', '' );
					}

					if ( ! empty( $item['addon_total'] ) ) {
						$oitem->add_meta_data( '_vtp_addon_total', floatval( $item['addon_total'] ) );
					}
					if ( ! empty( $item['addon_tax'] ) ) {
						$oitem->add_meta_data( '_vtp_addon_tax', floatval( $item['addon_tax'] ) );
					}
					if ( ! empty( $item['addons'] ) ) {
						$oitem->add_meta_data( '_vtp_items_price', $item_price );
						$oitem->add_meta_data( '_vtp_addons', $item['addons'] );
					}
					$oitem->save();

				} catch ( Exception $e ) {
					POS_Settings::get_module_instance()->add_error( $e->getMessage() );
				}
			}

			$this->calculate_totals( true );
						$this->set_order_tax( $total_tax );

			$this->calculate_totals( false );
			$rounding_factor = null;
			if ( $this->order->get_total() != $grand_total ) {
				try {
					$this->order->add_meta_data(
						'_vtp_miss_total',
						( - 1 ) * ( $this->order->get_total() - $grand_total )
					);
					$this->order->set_total( $grand_total );
				} catch ( Exception $e ) {
					$this->calculate_totals( false );
				}
			}

			$this->order->add_meta_data( '_vt_tax_method', POS_Settings::tax_method() );
			$this->order->add_meta_data( '_is_vitepos', 'Y' );
			$this->order->add_meta_data( '_vtp_order_note', $this->get_payload( 'note', '' ) );
			if ( $this->is_offline ) {
				$this->outlet_id  = $this->get_payload( 'outlet_id', $this->outlet_id );
				$this->counter_id = $this->get_payload( 'counter_id', $this->counter_id );
				$cash_drawer      = $this->get_payload( 'cash_drawer_id', null );
				if ( ! empty( $cash_drawer ) ) {
					$this->order->add_meta_data( '_vtp_cash_drawer_id', $cash_drawer );
				}
			}
				$this->order->add_meta_data( '_vtp_outlet_id', $this->outlet_id );
				$this->order->add_meta_data( '_vtp_counter_id', $this->counter_id );

			$this->order->add_meta_data( '_vtp_order_note', $this->get_payload( 'note', '' ) );
			$this->order->add_meta_data( '_vt_is_paid', 'N' );
			$this->order->add_meta_data( '_vt_email_sent', 'N' );
			if ( $this->is_offline ) {
				$offline_id = $this->get_payload( 'offline_id', '' );
				if ( ! empty( $offline_id ) ) {
					$this->order->add_meta_data( '_vtp_offline_id', $offline_id );
				}

				$processed_by_offline = $this->get_payload( 'processed_by' );
				$user                 = get_user_by( 'login', $processed_by_offline );
				if ( ! empty( $user->ID ) ) {
					$this->order->add_meta_data( '_vtp_offline_synced_by', $this->current_user->ID );
					$processed_by = $user->ID;
					$this->order->add_meta_data( '_vtp_processed_by', $processed_by );
				}
			}
			return true;
		} else {
			POS_Settings::get_module_instance()->add_error( 'Items empty' );

			return false;
		}

		return false;
	}

	/**
	 * The set order tax is generated by appsbd
	 *
	 * @param int $total_tax Its total_tax param.
	 *
	 * @throws \WC_Data_Exception Throws exception.
	 */
	protected function set_order_tax( $total_tax = 0 ) {
		$payload_total_tax = $this->get_payload( 'tax_total' );
		if ( ! empty( $payload_total_tax ) ) {
			$this->order->set_cart_tax( $payload_total_tax );
		} else {
			try {
				$calculated_tax = $this->order->get_tax_totals();
				if ( $total_tax > 0 ) {
					if ( empty( $calculated_tax ) || $calculated_tax != $total_tax ) {
						$this->order->set_cart_tax( $total_tax );
					}
				}
			} catch ( \WC_Data_Exception $e ) {
				POS_Settings::get_module_instance()->add_error( $e->getMessage() );
			} catch ( Exception $e ) {
				POS_Settings::get_module_instance()->add_error( $e->getMessage() );
			}
		}
	}
	/**
	 * The calculate totals is generated by appsbd
	 *
	 * @param bool $and_taxes Its and_taxes param.
	 */
	protected function calculate_totals( $and_taxes = true ) {
		$current_billing = $this->order->get_address( 'billing' );
		$this->set_address( $this->get_outlet_address() );
		$this->order->calculate_totals( $and_taxes );
		$this->set_address( $current_billing, 'billing' );
	}
	protected function set_address($address,$type = 'billing'){
		if(!empty($address['email']) && !is_email($address['email'])){
			unset($address['email']);
		}
		$this->order->set_address( $address, 'billing' );
	}

	/**
	 * The get outlet address is generated by appsbd
	 *
	 * @return array
	 */
	protected function get_outlet_address() {
		$billing_address = array(
			'first_name' => $this->outlet_obj->name,
			'last_name'  => '',
			'email'      => $this->outlet_obj->email,
			'phone'      => $this->outlet_obj->phone,
			'address_1'  => 'Y' == $this->outlet_obj->main_branch ? 'Main Branch' : '',
			'city'       => $this->outlet_obj->city,
			'state'      => $this->outlet_obj->state,
			'postcode'   => $this->outlet_obj->zip_code,
			'country'    => $this->outlet_obj->country,
		);

		return $billing_address;
	}

	/**
	 * The check checkout pre order is generated by appsbd
	 *
	 * @return bool
	 */
	protected function check_checkout_pre_order() {
		if ( $this->is_checkout ) {
			if ( floatval( $this->payload['grand_total'] ) < 0 ) {
				POS_Settings::get_module_instance()->add_error( 'You can not order less than 0 amount' );

				return false;
			}
			$this->given_amount = (float) $this->get_payload( 'given_amount', 0.0 );
			$this->grand_total  = (float) $this->get_payload( 'grand_total', 0.0 );
			if ( $this->given_amount < $this->grand_total ) {
				POS_Settings::get_module_instance()->add_error( 'Paid amount must be greater or equal to grand total amount' );

				return false;
			}
		}

		return true;
	}

	/**
	 * The check checkout discount fee limit is generated by appsbd
	 *
	 * @return bool
	 */
	public function check_checkout_discount_fee_limit() {
		return true;
		if ( ! $this->is_offline && ! POS_Settings::is_admin_user() ) {
			if ( ! current_user_can( 'pos-discount' ) && ( ! empty( $this->payload['discounts'] ) && is_array( $this->payload['discounts'] ) ) ) {
				POS_Settings::get_module_instance()->add_error( 'You do not have permission to give discount' );

				return false;
			}
			$this->current_user  = wp_get_current_user();
			$this->user_discount = Mapbd_Pos_Role::get_discount_percentage( $this->current_user );
			if ( ! $this->check_discount_limit(
				$this->payload['sub_total'],
				$this->user_discount,
				$this->payload['discounts']
			) ) {
				POS_Settings::get_module_instance()->add_error( 'You can not give this much discount' );

				return false;
			}
		}

		if ( ! current_user_can( 'pos-fee' ) && ( ! empty( $this->payload['fees'] ) && is_array( $this->payload['fees'] ) ) ) {
			POS_Settings::get_module_instance()->add_error( 'You do not have permission to have fees' );

			return false;
		}

		return true;
	}

	/**
	 * The check items stock is generated by appsbd
	 *
	 * @return bool
	 */
	protected function check_items_stock() {
				if ( ! POS_Settings::is_stockable() ) {
			return true;
		}
		$is_ok                = true;
		$this->current_stocks = array();
		$msg                  = '';
		$is_default_stock=POS_Settings::is_default_stock();
		foreach ( $this->payload['items'] as $item ) {
			$qty        = (float) $item['quantity'];
			$product_id = ! empty( $item['variation_id'] ) ? $item['variation_id'] : $item['product_id'];
			$current_stock=0;
			if ( $is_default_stock ) {
				$product = wc_get_product( $product_id );
				if ( $product->is_in_stock() ) {
					$current_stock = $product->get_stock_quantity() ? $product->get_stock_quantity() : 0;
				}
			}else {
				/**
				 * Its use for outlet stock
				 *
				 * @since 1.0.0
				 */
				$current_stock = apply_filters( 'vitepos/filter/outlet-stock', 0, $product_id, $this->outlet_id );
			}

			$current_stock_float = (float) $current_stock;
			if ( $current_stock_float < $qty ) {
				$is_ok                  = false;
				$msg                    .= ( ! empty( $item['product_name'] ) ? $item['product_name'] : '' ) . ',';
				$std                    = new \stdClass();
				$std->product_id        = $product_id;
				$std->variation_id      = $item['variation_id'];
				$std->stock             = $current_stock;
				$this->current_stocks[] = $std;
			}
		}

		if ( ! $is_ok ) {
			POS_Settings::get_module_instance()->add_error( '%s out of stock', rtrim( $msg, ',' ) );
			$this->set_order_details( false, null, 'UPS' );
		}

		return $is_ok;

	}

	/**
	 * The check discount limit is generated by appsbd
	 *
	 * @param mixed $subtotal Its subtotal param.
	 * @param mixed $user_discount Its user_discount param.
	 * @param array $discounts Its discounts param.
	 *
	 * @return bool
	 */
	public function check_discount_limit( $subtotal, $user_discount, $discounts = array() ) {
		$user_max_discount = 0.00;
		if ( ! empty( $subtotal ) && $user_discount > 0 ) {
			$user_max_discount = $user_max_discount + ( floatval( $subtotal ) ) * ( floatval( $user_discount / 100 ) );
		}
		$discount_payload = 0.00;
		if ( $user_discount > 0 && ( ! empty( $discounts ) && is_array( $discounts ) ) ) {
			$discount_payload = $this->get_total_fee_or_discount( $subtotal, $discounts );
		}
		if ( $user_max_discount < $discount_payload ) {
			return false;
		}

		return true;
	}

	/**
	 * The get total fee or discount is generated by appsbd
	 *
	 * @param mixed $subtotal Order subtotal.
	 * @param mixed $fee_or_discounts Discont or fee array.
	 *
	 * @return float|int
	 */
	protected function get_total_fee_or_discount( $subtotal, $fee_or_discounts ) {
		$amount = 0.00;
		if ( ( ! empty( $fee_or_discounts ) && is_array( $fee_or_discounts ) ) ) {
			foreach ( $fee_or_discounts as $item ) {
				if ( strtoupper( $item['type'] ) == 'P' ) {
					$amount = $amount + ( floatval( $subtotal ) ) * ( floatval( $item['val'] / 100 ) );
				} else {
					$amount = $amount + ( floatval( $item['val'] ) );
				}
			}
		}
		return $amount;
	}

	/**
	 * The check offline pre order is generated by appsbd
	 *
	 * @return bool
	 */
	public function check_offline_pre_order() {
		if ( $this->is_offline ) {
			$outlet_id  = $this->get_payload( 'outlet_id', '' );
			$counter_id = $this->get_payload( 'counter_id', '' );
			if ( ! empty( $outlet_id ) && ! empty( $counter_id ) ) {
				$this->set_outlet( $outlet_id, $counter_id );
			} else {
				POS_Settings::get_module_instance()->add_error( 'No offline outlet and counter found' );

				return false;
			}
		}

		return true;
	}

	/**
	 * The add time by status is generated by appsbd
	 *
	 * @param mixed $status Its status param.
	 *
	 * @return bool
	 */
	public function add_time_by_status( $status ) {
		$time_meta_key = '_vt_time_log';
		$time_logs     = $this->order->get_meta( $time_meta_key );
		if ( ! is_array( $time_logs ) ) {
			$time_logs = array();
		}
		$time_obj         = new \stdClass();
		$time_obj->status = $status;
		$time_obj->time   = gmdate( 'Y-m-d H:i:s' );
		$time_logs[]      = $time_obj;
		if ( update_post_meta( $this->order->get_id(), $time_meta_key, $time_logs ) ) {
			return true;
		}

		return false;
	}

	/**
	 * The clear order discount fee is generated by appsbd
	 */
	protected function clear_order_discount_fee() {
		foreach ( $this->order->get_items( 'fee' ) as $item ) {
			if ( $item instanceof \WC_Order_Item_Fee ) {
				$this->order->remove_item( $item->get_id() );
			}
		}
	}

	/**
	 * The set fee discount is generated by appsbd
	 *
	 * @return bool
	 */
	protected function set_fee_discount() {

		if ( is_null( $this->user_discount ) ) {
			$this->user_discount = Mapbd_Pos_Role::get_discount_percentage( $this->current_user );
		}

		$this->clear_order_discount_fee();
		$this->calculate_totals( true );
		$this->set_tax_after_discount_or_fee();
				$total_amount = $this->order->get_subtotal();

		$fee_total = 0.0;
		if ( ! empty( $this->payload['fees'] ) && is_array( $this->payload['fees'] ) ) {
			foreach ( $this->payload['fees'] as $item ) {
				if ( ! empty( $item['type'] ) && ! empty( $item['val'] ) ) {
					$item_val = floatval( $item['val'] );
					$title    = POS_Settings::get_module_instance()->__( 'Fee' );
					if ( $item_val > 0 ) {
						if ( strtoupper( $item['type'] ) == 'P' ) {
							$item_amount = $total_amount * ( $item_val / 100 );
							$title      .= '(' . $item['val'] . '%)';
						} else {
							$item_amount = $item_val;
						}
						$fee_total += $item_amount;

						vitepos_order_add_fee_on_order(
							$this->order,
							$title,
							$item_amount,
							array(
								'_vtp_cal_type' => $item['type'],
								'_vtp_cal_val'  => $item['val'],
							)
						);
					}
				}
			}
		}
		if ( ! $this->is_offline && ! POS_Settings::is_admin_user() ) {
			if ( ! $this->check_discount_limit( $total_amount, $this->user_discount, $this->payload['discounts'] ) ) {
				$this->order->delete( true );
				POS_Settings::get_module_instance()->add_error( 'You can not give this much discount' );

				return false;
			}
		}
		
		$discount = 0.0;
		if ( ! empty( $this->payload['discounts'] ) && is_array( $this->payload['discounts'] ) ) {
			foreach ( $this->payload['discounts'] as $item ) {
				if ( ! empty( $item['type'] ) && ! empty( $item['val'] ) ) {
					$item_val = floatval( $item['val'] );
					$title    = POS_Settings::get_module_instance()->__( 'Discount' );
					if ( $item_val > 0 ) {
						if ( strtoupper( $item['type'] ) == 'P' ) {
							$item_amount = $total_amount * ( $item_val / 100 );
							$title      .= '(' . $item['val'] . '%)';
						} else {
							$item_amount = $item_val;
						}
						$discount += $item_amount;
						vitepos_order_add_discount_on_order(
							$this->order,
							$title,
							$item_amount,
							array(
								'_vtp_cal_type' => $item['type'],
								'_vtp_cal_val'  => $item['val'],
							)
						);
					}
				}
			}
		}

		$tax_method = $this->order->get_meta( '_vt_tax_method' );

		$this->order->add_meta_data( '_vtp_fee_total', $fee_total );
		$this->order->add_meta_data( '_vtp_discount_total', - $discount );

		return true;
	}

	/**
	 * The set tax after discount or fee is generated by appsbd
	 */
	protected function set_tax_after_discount_or_fee() {
		$tax_method = $this->order->get_meta( '_vt_tax_method' );
		if ( 'A' == $tax_method ) {
			$total_amount   = $this->order->get_subtotal();
			$fee_total      = 0.0;
			$discount_total = 0.0;
			if ( ! empty( $this->payload['fees'] ) && is_array( $this->payload['fees'] ) ) {
				$fee_total = $this->get_total_fee_or_discount( $total_amount, $this->payload['fees'] );

			}
			if ( ! empty( $this->payload['discounts'] ) && is_array( $this->payload['discounts'] ) ) {
				$discount_total = $this->get_total_fee_or_discount( $total_amount, $this->payload['discounts'] );
			}
			$final_amount = $fee_total - $discount_total;
			if ( $final_amount < 0 ) {
				$is_discount  = true;
				$final_amount = ( -1 ) * $final_amount;
			} else {
				$is_discount = false;
			}

			if ( $total_amount > 0 && $final_amount > 0 ) {
				$items = array();
				foreach ( $this->order->get_items() as $item ) {
										$item_sub_total = $item->get_subtotal();
					$item_dis       = ( $final_amount / $total_amount ) * $item_sub_total;
					if ( $is_discount ) {
						$item->set_total( $item_sub_total - $item_dis );
					} else {
												$items[ $item->get_id() ] = $item_sub_total;
						$item->set_total( $item_sub_total + $item_dis );
						$item->set_subtotal( $item_sub_total );
					}
				}
				$this->calculate_totals( true );
				if ( ! $is_discount ) {
					foreach ( $this->order->get_items() as $item ) {
						$item->set_total( $items[ $item->get_id() ] );
						$item->set_subtotal( $items[ $item->get_id() ] );
					}
				}
				$this->calculate_totals( false );

			}
		}
	}

	/**
	 * The update order meta is generated by appsbd
	 *
	 * @param mixed $key Its key param.
	 * @param mixed $value Its value param.
	 */
	protected function update_order_meta( $key, $value ) {
		if ( empty( $this->order_id ) ) {
			$this->order->update_meta_data( $key, $value );
		} else {
			$this->order->add_meta_data( $key, $value );
		}
	}

	/**
	 * The is ready to checkout is generated by appsbd
	 *
	 * @return bool
	 * @throws \WC_Data_Exception Its the Exception.
	 */
	protected function is_ready_to_checkout() {
		$given_amount = (float) $this->get_payload( 'given_amount', 0.0 );
		$grand_total  = (float) $this->get_payload( 'grand_total', 0.0 );
		if ( $given_amount < $grand_total ) {
			POS_Settings::get_module_instance()->add_error( 'Paid amount must be greater or equal to grand total amount' );

			return false;
		}

		if ( ! $this->set_fee_discount() ) {
			return false;
		}
		$this->set_order_tax();

		$this->calculate_totals( false );
		$rounding_factor = null;
		if ( $this->order->get_total() != $grand_total ) {
			try {
				$this->order->add_meta_data(
					'_vtp_miss_total',
					( - 1 ) * ( $this->order->get_total() - $grand_total )
				);
				$this->order->set_total( $grand_total );
			} catch ( Exception $e ) {
				$this->calculate_totals( false );
			}
		}
		$this->update_order_meta( '_vtp_order_note', $this->get_payload( 'note', '' ) );
		$this->update_order_meta( '_vtp_payment_note', $this->get_payload( 'payment_note', '' ) );
		$this->update_order_meta( '_vtp_payment_method', $this->get_payload( 'payment_method', '' ) );
		$this->order->add_meta_data( '_vtp_tendered_amount', $this->get_payload( 'given_amount', 0.0 ) );

		$change_amount = $this->get_payload( 'returned_amount', 0.0 );
		$this->order->add_meta_data( '_vtp_change_amount', $change_amount );
		$payment_list = $this->get_payload( 'payment_list', array() );

		
		
		foreach ( $payment_list as &$pmt ) {
			$pmt['is_paid'] = in_array( $pmt['type'], array( 'C', 'S', 'O' ) ) ? 'Y' : 'N';
			$pmt['name']    = $this->get_payment_name( $pmt['type'] );
		}
		$this->order->add_meta_data( '_vtp_payment_list', $payment_list );

		return true;
	}

	/**
	 * The send to kitchen is generated by appsbd
	 *
	 * @return bool
	 */
	public function send_to_kitchen() {
		$this->is_restaurant = true;
		$this->is_checkout   = false;
		$this->is_offline    = false;

		if ( $this->create_order() ) {
			$this->order->add_meta_data( '_vtp_is_resto', 'Y' );
			$this->order->add_meta_data( '_vtp_order_by', $this->current_user->ID ); 
			$this->order->add_meta_data( '_vtp_tables', $this->get_payload( 'table_id', array() ) );
			$this->order->add_meta_data( '_vtp_persons', $this->get_payload( 'persons', 0 ) );
			$this->order->add_meta_data( '_vtp_order_type', $this->get_payload( 'order_type', 'in_store' ) );

			if ( $this->order->update_status( 'vt_in_kitchen', 'Sent to kitchen', true ) ) {
				$this->add_time_by_status( 'vt_in_kitchen' );
				$this->order->add_meta_data( '_vt_is_paid', 'N' );
				POS_Settings::get_module_instance()->add_info( 'Order successfully sent to kitchen' );
				$order_details = POS_Order::get_from_woo_order_restro_by_id(
					$this->order->get_id(),
					false,
					true
				);
				/**
				 * Its for check is there any change before process
				 *
				 * @since 2.0
				 */
				do_action( 'vitepos/action/send-order-push', $order_details );
				$this->set_order_details( true, $order_details, '' );

				return true;
			} else {
				POS_Settings::get_module_instance()->add_error( 'Failed' );

				return false;
			}
		} else {
			return false;
		}
	}

	/**
	 * The restaurant checkout is generated by appsbd
	 *
	 * @param mixed $order_id Its order_id param.
	 *
	 * @return bool
	 * @throws \WC_Data_Exception Its Exception.
	 */
	public function restaurant_checkout( $order_id ) {
		$this->is_restaurant = true;
		$this->is_checkout   = false;
		$this->is_offline    = false;

		if ( empty( $order_id ) ) {
			POS_Settings::get_module_instance()->add_error( 'Empty order id' );
			return false;
		}
		$this->load_order( $order_id );

		$stat = $this->order->get_status();

		if ( ! empty( $this->order ) && ( 'vt_served' == $stat || 'vt_ready_to_srv' == $stat ) ) {
			$billing_address = $this->get_outlet_address();
			$customer_id     = $this->order->get_customer_id();
			if ( empty( $customer_id ) ) {
				$customer_id = $this->get_payload(
					'customer',
					POS_Settings::get_module_option( 'pos_customer', null )
				);
				$this->order->set_customer_id( $customer_id );
				/**
				 * Its for check is there any change before process
				 *
				 * @param $billing_address Object
				 * @param $order \WC_Order Object
				 * @param $order_arg customer data
				 *
				 * @since 1.0
				 */
				$billing_address = apply_filters(
					'vitepos/filter/billing-address',
					$billing_address,
					$this->order,
					$customer_id
				);
				$this->set_address( $billing_address, 'billing' );
			}

			if ( $this->is_ready_to_checkout() ) {
				$this->order->add_meta_data( '_vtp_processed_by', $this->current_user->ID );
				return $this->_complete_order();
			}
		} else {
			POS_Settings::get_module_instance()->add_error( 'You can not checkout this in current status' );
		}
		return false;
	}

	/**
	 * The grocery checkout is generated by appsbd
	 *
	 * @param false $is_offline Its is_offline param.
	 *
	 * @return bool
	 * @throws \WC_Data_Exception Its Exception.
	 */
	public function grocery_checkout( $is_offline = false ) {
				$this->is_restaurant = false;
		$this->is_checkout   = true;
		$this->is_offline    = $is_offline;
		if ( ! $this->check_checkout_pre_order() || ! $this->check_offline_pre_order() || ( ! $is_offline && ! $this->check_items_stock() ) ) {
			return false;
		}
				if ( $this->create_order() ) {
			$this->order->add_meta_data( '_vtp_processed_by', $this->current_user->ID );
			if ( $this->is_ready_to_checkout() ) {
				return $this->_complete_order( $is_offline, true );
			}
		}
		return false;
	}



	/**
	 * The restaurant checkout pay first is generated by appsbd
	 *
	 * @param false $is_offline Its is_offline param.
	 *
	 * @return bool
	 * @throws \WC_Data_Exception Its Exception.
	 */
	public function restaurant_checkout_pay_first( $is_offline = false ) {
				$this->is_restaurant = false;
		$this->is_checkout   = true;
		$this->is_offline    = $is_offline;

		if ( ! $this->check_checkout_pre_order() || ! $this->check_offline_pre_order() ) {
			return false;
		}
		if ( $this->create_order() ) {
			$this->order->add_meta_data( '_vtp_is_resto', 'Y' );
			$this->order->add_meta_data( '_vtp_processed_by', $this->current_user->ID );
			$this->order->add_meta_data( '_vtp_is_pay_first', 'Y' );
			$this->order->add_meta_data( '_vtp_tables', $this->get_payload( 'table_id', array() ) );
			$this->order->add_meta_data( '_vtp_persons', $this->get_payload( 'persons', 0 ) );
			$this->order->add_meta_data( '_vtp_order_type', $this->get_payload( 'order_type', 'in_store' ) );
			if ( $this->is_ready_to_checkout() ) {
				return $this->_complete_order( $is_offline, true );
			}
		}
		return false;
	}

	/**
	 * The cancel order is generated by appsbd
	 *
	 * @return bool
	 */
	public function cancel_order() {
		$this->load_order( $this->get_payload( 'order_id' ) );
		$status = $this->order->get_status();
		POS_Settings::get_module_instance()->add_debug( $status );
		if ( ! empty( $this->order ) ) {
			if ( 'pending' == $status ) {
				if ( $this->order->update_status( 'cancelled', 'Order was cancelled programmatically.', true ) ) {
					$this->reverse_items_stock_on_canceled();
					update_post_meta( $this->order->get_id(), '_vt_is_canceled', 'Y' );
					POS_Settings::get_module_instance()->add_info( 'Order successfully completed' );
					$order_details = POS_Order::get_from_woo_order_details_by_id( $this->order->get_id() );
					$this->set_order_details( false, $order_details, '' );

					return true;
				} else {
					POS_Settings::get_module_instance()->add_error( 'Failed' );
				}
			} elseif ( 'completed' == $status ) {
				POS_Settings::get_module_instance()->add_error( 'Order already in completed status, it is not cancelable' );

				return true;
			} elseif ( 'canceled' == $status ) {
				$this->reverse_items_stock_on_canceled();
				update_post_meta( $this->order->get_id(), '_vt_is_canceled', 'Y' );
				POS_Settings::get_module_instance()->add_info( 'Order successfully completed' );
				$order_details = POS_Order::get_from_woo_order_details_by_id( $this->order->get_id() );
				$this->set_order_details( false, $order_details, '' );

				return true;
			}
		} else {
			POS_Settings::get_module_instance()->add_error( 'Order cancellation is not possible' );
		}

		return false;
	}

	/**
	 * The load order is generated by appsbd
	 *
	 * @param integer $id Order id.
	 */
	protected function load_order( $id ) {
		$this->order_id = $id;
		$this->order    = new \WC_Order( $id );		if ( $this->order->get_meta( '_vtp_is_resto' ) == 'Y' ) {
			$this->is_restaurant = true;
		}
	}

	/**
	 * The complete order payment is generated by appsbd
	 *
	 * @return bool
	 * @throws \Stripe\Exception\ApiErrorException Its api error ecception.
	 */
	public function complete_order_payment() {
		$this->load_order( $this->get_payload( 'order_id' ) );
		$status = $this->order->get_status();
		if ( ! empty( $this->order ) ) {
			if ( 'pending' == $status ) {
				$type           = strtolower( $this->get_payload( 'type' ) );
				$transaction_id = $this->get_payload( 'transaction_id' );
				if ( 'stripe' == $type ) {
					update_post_meta( $this->order->get_id(), '_vt_stp_trans_id', $transaction_id );
					$paymentlist = $this->get_payments();
					foreach ( $paymentlist as &$pt ) {
						if ( 'T' == $pt['type'] ) {
							if ( ! empty( $pt['cm'] ) && 'manual' == $pt['cm'] ) {
								if ( $this->capture_payment_intent( $transaction_id ) ) {
									$pt['is_paid'] = 'Y';
								} else {
									$order_details = POS_Order::get_from_woo_order_details_by_id( $this->order->get_id() );
									$this->set_order_details( false, $order_details, 'RT' );
									return false;
								};
							} else {
								$pt['is_paid'] = 'Y';
							}
						}
					}
				}
				$this->order->update_meta_data( '_vtp_payment_list', $paymentlist );
								return $this->_complete_order();
			} elseif ( 'complete' == $status ) {
				POS_Settings::get_module_instance()->add_info( 'Order successfully completed' );
				$order_details = POS_Order::get_from_woo_order_details_by_id( $this->order->get_id() );
				$this->set_order_details( true, $order_details, '' );

				return true;
			}
		} else {
			POS_Settings::get_module_instance()->add_error( 'Order not found' );
		}

		return false;
	}

	/**
	 * The set order details is generated by appsbd
	 *
	 * @param mixed  $is_complete Its is_complete param.
	 * @param mixed  $order_details Its order_details param.
	 * @param string $next Its next command, SE= Send Email, RSTP=Require Stripe Payment, IFM=Iframe.
	 * @param array  $params Its params param.
	 */
	protected function set_order_details( $is_complete, $order_details, $next = '', $params = array() ) {
		$obj                = new \stdClass();
		$obj->is_complete   = $is_complete ? 'Y' : 'N';
		$obj->next          = $next;
		$obj->data          = (object) $params;
		$obj->order         = $order_details;
		$obj->current_stock = $this->current_stocks;
		if ( 'UPS' == $next ) {
			$this->is_need_to_update_stock = true;
		}
		$this->order_details = $obj;
	}

	/**
	 * The update items stock is generated by appsbd
	 *
	 * @param false $is_offline Its offline param.
	 *
	 * @return bool
	 */
	protected function update_items_stock( $is_offline = false ) {

		if ( ! POS_Settings::is_stockable() ) {
			return true;
		}
		$is_restaurant = $this->order->get_meta( '_vtp_is_resto' ) == 'Y';
		if ( $is_restaurant ) {
			return true;
		}

		$is_already_calculated = get_post_meta( $this->order->get_id(), '_vt_stock_cal', true );
		if ( 'Y' == $is_already_calculated ) {
			return true;
		}
		$is_ok                         = true;
		$this->current_stocks          = array();
		$this->is_need_to_update_stock = true;
		if ( $this->order instanceof \WC_Order ) {
			foreach ( $this->order->get_items() as $item ) {

				if ( $item instanceof \WC_Order_Item_Product ) {
					$product_id   = $item->get_product_id();
					$variation_id = $item->get_variation_id();

					if ( ! empty( $variation_id ) ) {
						$final_id = $variation_id;
					} else {
						$final_id = $product_id;
					}
					$std               = new \stdClass();
					$std->product_id   = $product_id;
					$std->variation_id = $variation_id;
					if(POS_Settings::is_default_stock()){
						$product = wc_get_product($final_id);
						Mapbd_Pos_Stock_Log::AddLog(
							'O',
							0,
							$final_id,
							$product->get_stock_quantity(),
							$item->get_quantity(),
							'Order completed',
							$this->order->get_id(),
							'OR',
							'W'
						);
						$std->stock=($product->get_stock_quantity()-$item->get_quantity());
						update_post_meta($product->get_id(), '_stock', $std->stock);
					}else{
						if ( ! Mapbd_Pos_Warehouse::reduce_stock_for_outlet(
							$final_id,
							$this->outlet_id,
							$item->get_quantity(),
							'Order completed',
							$this->order->get_id(),
							'OR',
							$is_offline
						) ) {
							return false;
						};
						/**
						 * The hook is use for outlet stock
						 *
						 * @since 1.0.0
						 */
						$std->stock = apply_filters( 'vitepos/filter/outlet-stock', 0, $final_id, $this->outlet_id );

					}

				}


				$this->current_stocks[] = $std;
			}
		}
		if ( ! empty( $this->current_stocks ) ) {
			/**
			 * Its for check is there any change before process
			 *
			 * @since 2.0
			 */
			do_action( 'vitepos/action/stock-updated', $this->current_stocks, $this->outlet_id );
		}
		update_post_meta( $this->order->get_id(), '_vt_stock_cal', 'Y' );
		update_post_meta( $this->order->get_id(), '_vt_stock_reverse', 'N' );

		return $is_ok;

	}

	/**
	 * The reverse items stock on canceled is generated by appsbd.
	 *
	 * @return bool
	 */
	protected function reverse_items_stock_on_canceled() {
				if ( ! POS_Settings::is_stockable() ) {
			return true;
		}

		$is_vt_pos = get_post_meta( $this->order->get_id(), '_is_vitepos', true );
		if ( 'Y' != $is_vt_pos ) {
			return true;
		}

		$is_restaurant = $this->order->get_meta( '_vtp_is_resto' ) == 'Y';
		if ( $is_restaurant ) {
			return true;
		}

		$is_already_reversed = get_post_meta( $this->order->get_id(), '_vt_stock_reverse', true );
		if ( 'Y' == $is_already_reversed ) {
			return true;
		}
		$is_ok                         = true;
		$this->current_stocks          = array();
		$this->is_need_to_update_stock = true;
		if ( $this->order instanceof \WC_Order ) {
			foreach ( $this->order->get_items() as $item ) {
				if ( $item instanceof \WC_Order_Item_Product ) {
					$product_id   = $item->get_product_id();
					$variation_id = $item->get_variation_id();
					if ( ! empty( $variation_id ) ) {
						$final_id = $variation_id;
					} else {
						$final_id = $product_id;
					}
					Mapbd_Pos_Warehouse::increase_stock_for_outlet(
						$final_id,
						$this->outlet_id,
						$item->get_quantity(),
						'Order canceled',
						$this->order->get_id(),
						'OC'
					);
				}
				$std               = new \stdClass();
				$std->product_id   = $product_id;
				$std->variation_id = $variation_id;
				/**
				 * This hook us use for outlet stock.
				 *
				 * @since 1.0.0
				 */
				$std->stock = apply_filters( 'vitepos/filter/outlet-stock', 0, $final_id, $this->outlet_id );

				$this->current_stocks[] = $std;
			}
		}

		update_post_meta( $this->order->get_id(), '_vt_stock_reverse', 'Y' );
		update_post_meta( $this->order->get_id(), '_vt_stock_cal', 'N' );

		if ( ! empty( $this->current_stocks ) ) {
			/**
			 * Its for check is there any change before process
			 *
			 * @since 2.0
			 */
			do_action( 'vitepos/action/stock-updated', $this->current_stocks, $this->outlet_id );
		}

		return $is_ok;

	}

	/**
	 * The update cash drawer is generated by appsbd.
	 */
	protected function update_cash_drawer() {
		$payment_list         = $this->get_payments();
		$processed_by         = $this->order->get_meta( '_vtp_processed_by' );
		$cash_drawer_id       = $this->order->get_meta( '_vtp_cash_drawer_id' );
		$is_found_cash_drawer = false;
		if ( ! $this->is_offline && ! empty( $cash_drawer_id ) ) {
			$cash_drawer = Mapbd_Pos_Cash_Drawer::find_by( 'id', $cash_drawer_id );
			if ( ! empty( $cash_drawer->outlet_id ) ) {
				$is_found_cash_drawer = true;
			}
		}
		if ( ! $is_found_cash_drawer ) {
			if ( ! $this->is_offline ) {
				$this->outlet_id  = $this->order->get_meta( '_vtp_outlet_id' );
				$this->counter_id = $this->order->get_meta( '_vtp_counter_id' );
			}
			$cash_drawer = Mapbd_Pos_Cash_Drawer::get_by_counter( $this->outlet_id, $this->counter_id, $processed_by );
			$this->order->add_meta_data( '_vtp_cash_drawer_id', $cash_drawer->id );
		}
		$change_amount = (float) $this->order->get_meta( '_vtp_change_amount' );
		$cash_found    = false;
		foreach ( $payment_list as $payment ) {
			if ( 'C' == $payment['type'] ) {
				$cash_found = true;
				$amount     = doubleval( $payment['amount'] ) - doubleval( $change_amount );
				if ( $amount > 0.0 ) {
					Mapbd_Pos_Cash_Drawer::add_order( $this->current_user->ID, $amount, $this->order->get_id(), $this->outlet_id, $this->counter_id );
				} else {
					Mapbd_Pos_Cash_Drawer::add_order( $this->current_user->ID, doubleval( $payment['amount'] ), $this->order->get_id(), $this->outlet_id, $this->counter_id );
					Mapbd_Pos_Cash_Drawer::add_change_log(
						$this->current_user->ID,
						doubleval( $change_amount ),
						$this->order->get_id(),
						$this->outlet_id,
						$this->counter_id
					);
				}
			}
			Mapbd_Pos_Cash_Drawer_Types::AddLog( $cash_drawer->id, $this->current_user->ID, $this->order->get_id(), $payment['type'], $payment['amount'] );
		}

		if ( ! empty( $cash_drawer ) ) {
			$this->order->add_meta_data( '_vtp_cash_drawer_id', $cash_drawer->id );
			if ( $change_amount > 0 ) {
				if ( ! $cash_found ) {
					Mapbd_Pos_Cash_Drawer::add_change_log(
						$this->current_user->ID,
						$change_amount,
						$this->order->get_id(),
						$this->outlet_id,
						$this->counter_id
					);
				}

				Mapbd_Pos_Cash_Drawer_Types::AddLog(
					$cash_drawer->id,
					$this->current_user->ID,
					$this->order->get_id(),
					'_',
					$change_amount
				);
			}
		}
	}
	/**
	 * The is ok all payment is generated by appsbd
	 *
	 * @return \stdClass
	 */
	protected function is_ok_all_payment() {

		$resp               = new \stdClass();
		$resp->order_status = true;
		$resp->need_payment = false;
		$resp->next         = '';
		$resp->payment_data = null;
		$payment_list       = $this->get_payments();
		if ( empty( $payment_list ) ) {
			$resp->order_status   = false;
			$resp->payment_status = false;

			return $resp;
		}
		$process_status = null;
		foreach ( $payment_list as &$item ) {
			if ( ! isset( $item['is_paid'] ) ) {
				$item['is_paid'] = 'N';

			}
			if ( 'Y' != $item['is_paid'] ) {
				$process_status = $this->process_payment( $item );
				break;
			}
		}
		if ( ! empty( $process_status ) ) {
			if ( ! empty( $process_status->status ) ) {
				if ( $process_status->is_delete ) {
					$resp->order_status = false;
					$resp->need_payment = false;

					return $resp;
				} else {
					$resp->need_payment = true;
					$this->order->update_meta_data( '_vtp_payment_list', $payment_list );
					$resp->next         = $process_status->next;
					$resp->payment_data = $process_status->payment_data;

					return $resp;
				}
			}
		}

		return $resp;
	}

	/**
	 * The process payment is generated by appsbd
	 *
	 * @param mixed $payment_item Its payment item param.
	 *
	 * @return \stdClass
	 */
	protected function process_payment( &$payment_item ) {
		$resp               = new \stdClass();
		$resp->status       = false;
		$resp->is_delete    = false;
		$resp->next         = '';
		$resp->payment_data = null;

		if ( 'T' == $payment_item['type'] ) { 			$resp->status    = true;
			$stripe_settings = \VitePos\Modules\POS_Payment::get_payment_gw_settings( 'stripe' );

			if ( ! empty( $stripe_settings ) && 'Y' == $stripe_settings->is_enable ) {
				if ( empty( $payment_item['client_secret'] ) ) {
					$stripe_secret_key = '' . $stripe_settings->settings->secret_key;
					\Stripe\Stripe::setApiKey( $stripe_secret_key );
					try {
						$param = array(
							'amount'         => $payment_item['amount'] * 100,
							'currency'       => $this->order->get_currency(),
							'capture_method' => 'manual',
						);
						if ( ! empty( $stripe_settings->settings->capture_method ) && 'P' == $stripe_settings->settings->capture_method ) {
							unset( $param ['capture_method'] );
						}
						$payment_intent                = \Stripe\PaymentIntent::create( $param );
						$payment_item['intend_id']     = $payment_intent->id;
						$payment_item['cm']            = ! empty( $param['capture_method'] ) ? $param['capture_method'] : 'automatic';
						$payment_item['client_secret'] = $payment_intent->client_secret;
						$resp->next                    = 'SCP';
						$resp->payment_data            = array( 'client_secret' => $payment_item['client_secret'] );
						POS_Settings::get_module_instance()->add_info( 'Order requires payment process' );

						return $resp;
					} catch ( \Stripe\Exception\ApiErrorException $e ) {
						POS_Settings::get_module_instance()->add_error( $e->getMessage() );
						$resp->is_delete = true;
					} catch ( Error $e ) {
						POS_Settings::get_module_instance()->add_error( $e->getMessage() );
						$resp->is_delete = true;
					}
				} else {
					$resp->next         = 'SCP';
					$resp->payment_data = array( 'client_secret' => $payment_item['client_secret'] );
				}
			} else {
				$resp->is_delete = true;
				POS_Settings::get_module_instance()->add_info( 'Stripe configuration error in server' );
			}
		}

		return $resp;
	}

	/**
	 * The capture payment intent is generated by appsbd
	 *
	 * @param string $intend_id Its stripe intend id.
	 *
	 * @return bool
	 */
	protected function capture_payment_intent( $intend_id ) {
		$stripe_settings = \VitePos\Modules\POS_Payment::get_payment_gw_settings( 'stripe' );
		if ( ! empty( $stripe_settings ) && ! empty( $stripe_settings->settings->secret_key ) ) {
			if ( ! empty( $intend_id ) ) {
				$stripe_secret_key = '' . $stripe_settings->settings->secret_key;
				try {
					$stripe = new \Stripe\StripeClient( $stripe_secret_key );
					$resp   = $stripe->{'paymentIntents'}->capture( $intend_id, array() );
					if ( ! empty( $resp->status ) && 'succeeded' == $resp->status ) {
						return true;
					}
				} catch ( \Stripe\Exception\ApiErrorException $e ) {
					POS_Settings::get_module_instance()->add_error( $e->getMessage() );
					return false;
				} catch ( Error $e ) {
					POS_Settings::get_module_instance()->add_error( $e->getMessage() );
					return false;
				} catch ( Exception $e ) {
					POS_Settings::get_module_instance()->add_error( $e->getMessage() );
					return false;
				}
			} else {
				POS_Settings::get_module_instance()->add_error( 'Empty intend id' );
				return false;
			}
		}
		return false;
	}

	/**
	 * The has payments is generated by appsbd
	 *
	 * @return bool
	 */
	protected function has_payments() {
		$payment_list = $this->get_payments();

		return ! empty( $payment_list );
	}

	/**
	 * The get payments is generated by appsbd
	 *
	 * @return array|mixed|string|null
	 */
	protected function get_payments() {
		$payment_list = $this->order->get_meta( '_vtp_payment_list' );
		if ( empty( $payment_list ) ) {
			$payment_list = get_post_meta( $this->order->get_id(), '_vtp_payment_list', true );
			if ( empty( $payment_list ) ) {
				$payment_list = $this->get_payload( 'payment_list', array() );
			}
		}

		return $payment_list;
	}

	/**
	 * The  complete order is generated by appsbd
	 *
	 * @param false $is_offline Its offline param.
	 * @param false $can_delete its con delete param.
	 *
	 * @return bool
	 * @throws \Stripe\Exception\ApiErrorException Its api error exception.
	 */
	protected function _complete_order( $is_offline = false, $can_delete = false ) {
		$is_restaurant = $this->order->get_meta( '_vtp_is_resto' ) == 'Y';
		$is_pay_first  = $this->order->get_meta( '_vtp_is_pay_first' ) == 'Y';
		if ( ! $is_offline ) {
			$payment_response = $this->is_ok_all_payment();
			$is_paid          = 'Y';
			$is_complete      = true;
			if ( $payment_response->order_status ) {
				$status = $this->get_order_complete_status( $is_restaurant, $is_pay_first );
				if ( $payment_response->need_payment ) {
					$status      = 'pending';
					$is_paid     = 'N';
					$is_complete = false;
				}

				if ( $this->order->update_status( $status, 'Vitepos checkout', true ) ) {
					if ( ! $is_restaurant && ! $this->is_restaurant ) {
						if ( ! $this->update_items_stock() ) {
							$this->order->delete( true );
							$this->is_need_to_update_stock = true;
							$this->check_items_stock();
							return false;
						}
					}
					if ( ! $payment_response->need_payment ) {
						$this->update_cash_drawer();
					}

					update_post_meta( $this->order->get_id(), '_vt_is_paid', $is_paid );
					$order_details = POS_Order::get_from_woo_order_restro_by_id(
						$this->order->get_id(),
						false,
						true
					);
					if ( ! $payment_response->need_payment ) {
						if ( $is_restaurant || $this->is_restaurant ) {
							if ( $is_pay_first && POS_Settings::is_kitchen() ) {
								POS_Settings::get_module_instance()->add_info( 'Order successfully sent to kitchen' );
							} else {
								POS_Settings::get_module_instance()->add_info( 'Order successfully completed' );
							}
						}
					}

					if ( $is_complete ) {
						if ( empty( $this->outlet_obj->email ) || $this->outlet_obj->email != $this->order->get_billing_email( '' ) ) {
							$payment_response->next = 'SE';
						} else {
							$payment_response->next = '';
							$this->order->add_order_note( 'Skipped customer email sent as same email of outlet' );
						}
					}
					if ( $is_restaurant ) {
						/**
						 * Its for check is there any change before process
						 *
						 * @since 2.0
						 */
						do_action( 'vitepos/action/send-order-push', $order_details );
					}
					$this->set_order_details(
						$is_complete,
						$order_details,
						$payment_response->next,
						$payment_response->payment_data
					);

					return true;
				} else {
					POS_Settings::get_module_instance()->add_error( 'Failed' );
				}
			} else {
				if ( $can_delete ) {
					$this->order->delete( true );
				}
				POS_Settings::get_module_instance()->add_error( 'Payment data missing' );

				return false;
			}
		} else {
			$status = 'completed';
			if ( $this->order->update_status( $status, 'Vitepos checkout', true ) ) {
				if ( ! $is_restaurant && ! $this->is_restaurant ) {
					$this->update_items_stock( true );
				}
				$this->update_cash_drawer();
				update_post_meta( $this->order->get_id(), '_vt_is_paid', 'Y' );
				POS_Settings::get_module_instance()->add_info( 'Order successfully completed' );
				if ( $is_restaurant ) {
					$order_details = POS_Order::get_from_woo_order_restro_by_id( $this->order->get_id(), false, true );
					if ( $is_pay_first && POS_Settings::is_kitchen() ) {
						POS_Settings::get_module_instance()->add_info( 'Order successfully sent to kitchen' );
					} else {
						POS_Settings::get_module_instance()->add_info( 'Order successfully completed' );
					}
				} else {
					$order_details = POS_Order::get_from_woo_order_details_by_id( $this->order->get_id() );
				}

								if ( empty( $this->outlet_obj->email ) || $this->outlet_obj->email != $this->order->get_billing_email( '' ) ) {
					/**
					 * Its for check is there any change before process
					 *
					 * @since 2.0
					 */
					do_action( 'vitepos/action/send-customer-email', $this->order->get_id() );
				} else {
					$this->order->add_order_note( 'Skipped customer email sent as same email of outlet' );
				}
				$this->set_order_details( 'Y', $order_details, '', null );

				return true;
			} else {
				POS_Settings::get_module_instance()->add_error( 'Failed' );
			}
		}
		return false;
	}

	/**
	 * The get order complete status is generated by appsbd
	 *
	 * @param any $is_restaurant its restaurant param.
	 * @param any $is_pay_first its pay first param.
	 *
	 * @return string
	 */
	protected function get_order_complete_status( $is_restaurant, $is_pay_first ) {

		$status = 'completed';
		if ( $is_restaurant ) {
			if ( $is_pay_first ) {
				if ( POS_Settings::is_kitchen() ) {
					$status = 'vt_in_kitchen';

				}
			}
		}
		return $status;
	}
	/**
	 * The get payment name is generated by appsbd
	 *
	 * @param mixed $type It the payment type.
	 *
	 * @return string
	 */
	public function get_payment_name( $type ) {
		switch ( $type ) {
			case 'T':
				return 'Stripe';
			case 'C':
				return 'Cash';
			case 'S':
				return 'Swipe';
			case 'O':
				return 'Others';
			default:
				return 'Unknown';
		}
	}

}

/**
 * Class payment_response
 *
 * @package VitePos\Libs
 */
class Payment_Response {
	/**
	 * Its property order_status
	 *
	 * @var bool
	 */
	public $order_status = true;
	/**
	 * Its property need_payment
	 *
	 * @var bool
	 */
	public $need_payment = true;
	/**
	 * Its property next
	 *
	 * @var string
	 */
	public $next = '';
	/**
	 * Its property payment_data
	 *
	 * @var null
	 */
	public $payment_data = null;
}
