<?php
/**
 * Its for Pos Counter module
 *
 * @package VitePos\Modules
 */

namespace VitePos\Modules;

use Appsbd\V1\Core\BaseModule;
use Appsbd\V1\libs\Ajax_Data_Response;
use Appsbd\V1\libs\Ajax_Response;
use Automattic\WooCommerce\Utilities\NumberUtil;
use VitePos\Libs\Pos_Product_Addon;
use Vitepos\Models\Database\Mapbd_Pos_Addon;
use Vitepos\Models\Database\Mapbd_pos_counter;

/**
 * Class POS_Counter
 */
class Pos_Addon extends BaseModule {
	/**
	 * Its property active_addons_list
	 *
	 * @var array
	 */
	protected static $active_addons_list = array();
	/**
	 * Its property is_loaded
	 *
	 * @var bool
	 */
	protected static $is_loaded = false;

	/**
	 * The initialize is generated by appsbd
	 */
	public function initialize() {
		add_filter( 'vitepos/filter/product-details', array( $this, 'product_addons' ), 10, 3 );
		add_filter( 'vitepos/filter/addon-tax', array( $this, 'addon_tax_calculation' ), 10, 3 );

	}

	/**
	 * The on init is generated by appsbd
	 */
	public function on_init() {
		parent::on_init();

		/**  //M
		if(!empty($_GET['is_debug1'])) {

		}
		$obj   = \json_decode( '{"id":3783,"barcode":3783,"name":"variation test 2","is_new":false,"image":"http:\/\/localhost\/Projects\/wcdev\/wp-content\/uploads\/woocommerce-placeholder-324x324.png","image_gallery":[],"sale_price":null,"regular_price":"","price_html":"From: <span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#2547;&nbsp;<\/span>20.00<\/bdi><\/span>","price":"20","cross_sale":[3102],"up_sale":[3089],"attributes":[{"id":3,"name":"Brand","slug":"pa_brand","visible":true,"variation":true,"options":[{"slug":"divi","name":"Divi"},{"slug":"divi-engine","name":"Divi Engine"},{"slug":"woocommerce","name":"WooCommerce"},{"slug":"WordPress","name":"WordPress"}]},{"id":1,"name":"Color","slug":"pa_color","visible":true,"variation":true,"options":[{"slug":"black","name":"Black"},{"slug":"blue","name":"Blue"},{"slug":"gray","name":"Gray"}]},{"id":2,"name":"Size","slug":"pa_size","visible":true,"variation":true,"options":[{"slug":"large","name":"Large"},{"slug":"medium","name":"Medium"},{"slug":"small","name":"Small"}]}],"variations":[{"id":3784,"name":"variation test 2","slug":"variation-test-2","image":false,"product_id":3783,"sale_price":null,"regular_price":"20","price":"20","in_stock":true,"manage_stock":false,"stock_quantity":null,"low_stock_amount":"","taxable":"Y","tax_status":"taxable","tax_class":"","on_sale":"N","attributes":[{"name":"Brand","slug":"pa_brand","option":"woocommerce"},{"name":"Color","slug":"pa_color","option":"black"},{"name":"Size","slug":"pa_size","option":"medium"}],"price_html":"<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#2547;&nbsp;<\/span>20.00<\/bdi><\/span>","barcode":3784,"sku":"variation-test-2-woocommerce-black-medium","weight":"0.00","height":"0.00","width":"0.00","length":"0.00","purchase_cost":"","is_parent_dimension":true,"outlet_id":2,"tax_rate":2},{"id":3785,"name":"variation test 2","slug":"variation-test-2-2","image":false,"product_id":3783,"sale_price":null,"regular_price":"30","price":"30","in_stock":true,"manage_stock":false,"stock_quantity":null,"low_stock_amount":"","taxable":"Y","tax_status":"taxable","tax_class":"","on_sale":"N","attributes":[{"name":"Brand","slug":"pa_brand","option":"WordPress"},{"name":"Color","slug":"pa_color","option":""},{"name":"Size","slug":"pa_size","option":"medium"}],"price_html":"<span class=\"woocommerce-Price-amount amount\"><bdi><span class=\"woocommerce-Price-currencySymbol\">&#2547;&nbsp;<\/span>30.00<\/bdi><\/span>","barcode":3785,"sku":"variation-test-2-wordpress--medium","weight":"0.00","height":"0.00","width":"0.00","length":"0.00","purchase_cost":"","is_parent_dimension":true,"outlet_id":2,"tax_rate":3}],"group_product":null,"parent_product":null,"manage_stock":false,"stock_quantity":null,"stock_status":"instock","low_stock_amount":"","purchasable":"Y","average_rating":"0.00","rating_count":0,"slug":"variation_test_2","sku":"","description":"","purchase_cost":"0.00","is_favorite":"N","taxable":"Y","tax_status":"taxable","tax_class":"","type":"variable","weight":"0.00","height":"0.00","width":"0.00","length":"0.00","addons":[],"outlet_id":2,"category_ids":[33],"categories":[33],"tax_rate":2}' );
		$addon = $this->product_addons( [], $obj );
		print_r( $addon );
		die;
		*/
	}

	/**
	 * The product addons is generated by appsbd
	 *
	 * @param mixed       $product_addons Its product_addons param.
	 * @param mixed       $product Its product param.
	 *
	 * @param \WC_Product $wc_product Its wc_product param.
	 *
	 * @return mixed
	 */
	public function product_addons( $product_addons, $product, $wc_product ) {

		$added_addons = array();
		foreach ( $product->category_ids as $category_id ) {
			$addons = self::check_rule_category( $category_id, $product->id );
			foreach ( $addons as $addon ) {
				if ( ! in_array( $addon->id, $added_addons ) ) {
					$added_addons[] = $addon->id;
										Pos_Product_Addon::set_product_addon_by( $addon, $product_addons, $wc_product );
				}
			}
		}

		return $product_addons;
	}

	/**
	 * The addon tax calculation is generated by appsbd
	 *
	 * @param mixed $tax_amount Its tax_amount param.
	 * @param mixed $addon_price Its addon_price param.
	 * @param mixed $wc_product Its wc_product param.
	 *
	 * @return float
	 */
	public function addon_tax_calculation( $tax_amount, $addon_price, $wc_product ) {
		if ( $addon_price > 0.0 && ! empty( $wc_product ) && ( $wc_product instanceof \WC_Product ) ) {
			$tax_amount = NumberUtil::round(
				( floatval(
					wc_get_price_including_tax(
						$wc_product,
						array( 'price' => $addon_price )
					)
				) - floatval( $addon_price ) ),
				wc_get_price_decimals()
			);
		}

		return $tax_amount;
	}
	/**
	 * The load addon list is generated by appsbd
	 */
	public static function load_addon_list() {
		if ( ! self::$is_loaded ) {
			self::$active_addons_list = Mapbd_Pos_Addon::get_all_addons('A');

			self::$is_loaded = true;
		}
	}

	/**
	 * The check rule category is generated by appsbd
	 *
	 * @param mixed $category_id Its category_id param.
	 * @param mixed $product_id Its product_id param.
	 *
	 * @return array
	 */
	protected static function check_rule_category( $category_id, $product_id ) {
		self::load_addon_list();
		$addons = array();
		foreach ( self::$active_addons_list as $addon ) {
			$is_matched_group = false;
			foreach ( $addon->rule_groups as $rule_group ) {
				$active = null;
				foreach ( $rule_group->rules as $rule ) {
					if ( 'C' == $rule->prop ) {
						if ( 'C' == $rule->prop && 'eq' == $rule->cond && in_array( $category_id, $rule->val ) ) {
							if ( is_null( $active ) ) {
								$active = true;
							}
						} elseif ( 'C' == $rule->prop && 'ne' == $rule->cond && in_array( $category_id, $rule->val ) ) {
							if ( is_null( $active ) ) {
								$active = false;
							}
						}
					} elseif ( 'P' == $rule->prop && 'eq' == $rule->cond && in_array( $product_id, $rule->val ) ) {
						if ( is_null( $active ) ) {
							$active = true;
						}
					} elseif ( 'P' == $rule->prop && 'ne' == $rule->cond && in_array( $product_id, $rule->val ) ) {
						$active = false;
					}
				}
				if ( $active ) {
										$is_matched_group = true;
					break;
				}
			}
			if ( $is_matched_group ) {
				$addons[] = $addon;
			}
		}

		return $addons;
	}

}
