<?php
/**
 * Pos Warehouse Database Model
 *
 * @package Vitepos\Models\Database
 */

namespace Vitepos\Models;

use VitePos\Core\ViteposModel;
use Vitepos\Models\Database\Mapbd_Pos_Warehouse;

/**
 * Class Mapbd_Pos_Cash_Drawer
 *
 * @package Vitepos\Models
 */
class Mapbd_POS_Dashboard extends ViteposModel {
	/**
	 * The get order statistics is generated by appsbd
	 *
	 * @return \stdClass
	 */
	public static function get_order_statistics() {

		$db     = self::get_db_object();
		$prefix = $db->prefix;
		$query  = "SELECT count({$prefix}posts.ID) as total_order,	mt1.meta_value as outlet_id,	sum(mt3.meta_value) as total_amount
			FROM	{$prefix}posts INNER JOIN {$prefix}postmeta ON ( {$prefix}posts.ID = {$prefix}postmeta.post_id and ({$prefix}postmeta.meta_key = '_is_vitepos' AND {$prefix}postmeta.meta_value = 'Y')) INNER JOIN {$prefix}postmeta AS mt1 ON ( {$prefix}posts.ID = mt1.post_id and mt1.meta_key = '_vtp_outlet_id' )
			INNER JOIN {$prefix}postmeta AS mt2 ON ( {$prefix}posts.ID = mt2.post_id and mt2.meta_key = '_vtp_payment_list') INNER JOIN {$prefix}postmeta AS mt3 ON ( {$prefix}posts.ID = mt3.post_id and mt3.meta_key = '_order_total')
			WHERE {$prefix}posts.post_type IN ( 'shop_order', 'shop_order_refund' ) 
			AND (({$prefix}posts.post_status = 'wc-completed')) 
			GROUP BY outlet_id
			ORDER BY total_amount desc";

		$response               = new \stdClass();
		$response->total_order  = 0;
		$response->total_amount = 0.0;
		$response->outlets      = $db->get_results( $query );

		$outlets                 = Mapbd_Pos_Warehouse::fetch_all_key_value( 'id', 'name' );
		$response->total_outlets = count( $outlets );
		foreach ( $response->outlets as &$item ) {
			$response->total_order  += $item->total_order;
			$response->total_amount += $item->total_amount;
			$item->total_amount      = vitepos_number_format( $item->total_amount );
			$item->total_amount_text = wc_price( $item->total_amount );
			$item->outlet_name       = appsbd_get_text_by_key( $item->outlet_id, $outlets );

		}
		$response->total_amount_text = wc_price( $response->total_amount );

		return $response;
	}

}
