# دليل البدء السريع - شاشات المطبخ والعرض

## الوصول للشاشات

### 1. شاشة المطبخ
```
https://yoursite.com/kitchen-display
```
**الاستخدام:**
- عرض جميع الطلبات الواردة للمطبخ
- بدء تحضير الطلب بالضغط على "بدء التحضير"
- إكمال الطلب بالضغط على "اكتمل"
- إضافة ملاحظات للطلبات

### 2. شاشة عرض الزبون
```
https://yoursite.com/customer-display
```
**الاستخدام:**
- عرض حالة الطلبات للعملاء
- قائمة انتظار الطلبات
- إشعارات عند جاهزية الطلب

### 3. شاشة التحضير
```
https://yoursite.com/preparation-screen
```
**الاستخدام:**
- إدارة الطلبات حسب الفئة
- سحب وإفلات الطلبات بين الحالات
- فلترة الطلبات حسب فئة المنتجات
- عرض إحصائيات الأداء

## الإعداد السريع

### 1. تحديث Rewrite Rules
بعد إضافة الملفات، قم بتحديث Permalinks:
1. اذهب إلى WordPress Admin
2. Settings > Permalinks
3. اضغط "Save Changes"

### 2. التحقق من الوصول
تأكد من أن الروابط تعمل:
- `/kitchen-display` - شاشة المطبخ
- `/customer-display` - شاشة عرض الزبون
- `/preparation-screen` - شاشة التحضير

### 3. إعداد الأجهزة
- **للمطبخ:** استخدم جهاز لوحي أو شاشة كبيرة
- **للعملاء:** شاشة مرئية في منطقة الانتظار
- **للتحضير:** جهاز كمبيوتر أو لوحي للمدير

## الاختصارات

### شاشة المطبخ:
- `F5` - تحديث الطلبات
- `Ctrl+R` - تحديث الطلبات

### شاشة التحضير:
- `F5` - تحديث الطلبات
- `Ctrl+R` - تحديث الطلبات
- السحب والإفلات لتغيير حالة الطلب

## استكشاف الأخطاء

### إذا لم تعمل الشاشات:
1. تحقق من تحديث Permalinks
2. تأكد من تفعيل JavaScript
3. تحقق من اتصال الإنترنت
4. راجع console المتصفح للأخطاء

### إذا لم تظهر الطلبات:
1. تحقق من إعدادات API
2. تأكد من وجود طلبات في النظام
3. تحقق من صلاحيات المستخدم

## الدعم الفني

للحصول على المساعدة:
1. راجع ملف `KITCHEN_DISPLAYS_README.md` للتفاصيل الكاملة
2. تحقق من ملفات الـ logs
3. تواصل مع فريق الدعم الفني

---

**ملاحظة:** تأكد من أن نظام VitePos يعمل بشكل صحيح قبل استخدام الشاشات الجديدة.
