<?php
/**
 * Pos Warehouse Database Model
 *
 * @package Vitepos\Models\Database
 */

namespace Vitepos\Models\Database;

use VitePos\Core\ViteposModel;


/**
 * Class Mapbd_pos_cash_drawer_log
 *
 * @package Vitepos\Models\Database
 */
class Mapbd_Pos_Cash_Drawer_Log extends ViteposModel {
	/**
	 * Its property id
	 *
	 * @var int
	 */
	public $id;
	/**
	 * Its property cash_drawer_id
	 *
	 * @var int
	 */
	public $cash_drawer_id;
	/**
	 * Its property ref_id
	 *
	 * @var int
	 */
	public $ref_id;
	/**
	 * Its property note
	 *
	 * @var String
	 */
	public $note;
	/**
	 * Its property pre_balance
	 *
	 * @var float
	 */
	public $pre_balance;
	/**
	 * Its property amount
	 *
	 * @var float
	 */
	public $amount;
	/**
	 * Its property user_id
	 *
	 * @var int
	 */
	public $user_id;
	/**
	 * Its property log_type
	 *
	 * @var String
	 */
	public $log_type;
	/**
	 * Its property ref_type
	 *
	 * @var String
	 */
	public $ref_type;
	/**
	 * Its property closing_balance
	 *
	 * @var float
	 */
	public $closing_balance;
	/**
	 * Its property entry_time
	 *
	 * @var String
	 */
	public $entry_time;


	/**
	 * Mapbd_pos_cash_drawer_log constructor.
	 */
	public function __construct() {
		parent::__construct();
		$this->SetValidation();
		$this->table_name     = 'apbd_pos_cash_drawer_log';
		$this->primary_key    = 'id';
		$this->unique_key     = array();
		$this->multi_key      = array();
		$this->auto_inc_field = array( 'id' );
		$this->app_base_name  = 'apbd-vite-pos';

	}


	/**
	 * The set validation is generated by appsbd
	 */
	public function set_validation() {
		$this->validations = array(
			'id'              => array(
				'Text' => 'Id',
				'Rule' => 'max_length[10]|integer',
			),
			'cash_drawer_id'  => array(
				'Text' => 'Cash Drawer Id',
				'Rule' => 'required|max_length[11]|integer',
			),
			'ref_id'          => array(
				'Text' => 'Ref Id',
				'Rule' => 'max_length[50]',
			),
			'note'            => array(
				'Text' => 'Note',
				'Rule' => 'max_length[255]',
			),
			'pre_balance'     => array(
				'Text' => 'Pre Balance',
				'Rule' => 'max_length[11]|numeric',
			),
			'amount'          => array(
				'Text' => 'Amount',
				'Rule' => 'max_length[11]|numeric',
			),
			'user_id'         => array(
				'Text' => 'User Id',
				'Rule' => 'max_length[11]|integer',
			),
			'log_type'        => array(
				'Text' => 'Log Type',
				'Rule' => 'max_length[1]',
			),
			'ref_type'        => array(
				'Text' => 'Ref Type',
				'Rule' => 'max_length[1]',
			),
			'closing_balance' => array(
				'Text' => 'Closing Balance',
				'Rule' => 'max_length[11]|numeric',
			),
			'entry_time'      => array(
				'Text' => 'Entry Time',
				'Rule' => 'max_length[20]',
			),

		);
	}

	/**
	 * The get property raw options is generated by appsbd
	 *
	 * @param mixed   $property Its the property.
	 * @param boolean $is_with_select False if with select.
	 *
	 * @return array|string[]
	 */
	public function get_property_raw_options( $property, $is_with_select = false ) {
		$return_obj = array();
		switch ( $property ) {
			case 'log_type':
				$return_obj = array(
					'D' => 'Debit',
					'C' => 'Credit',
				);
				break;
			case 'ref_type':
				$return_obj = array(
					'O' => 'Order',
					'C' => 'Close',
				);
				break;
			default:
		}
		if ( $is_with_select ) {
			return array_merge( array( '' => 'Select' ), $return_obj );
		}
		return $return_obj;

	}

	/**
	 * The AddLog is generated by appsbd
	 * The AddLog is generated by appsbd
	 *
	 * @param mixed $cash_drawer_id Its cash_drawe_id param.
	 * @param mixed $note Its note param.
	 * @param mixed $pre_balance Its pre_balance param.
	 * @param mixed $amount Its amount param.
	 * @param mixed $log_type Its log_type param, D=Debit,C=Credit.
	 * @param mixed $ref_type Its ref_type param, O=Order,C=Close.
	 * @param mixed $ref_id Its ref_id param, order_id or empty on close.
	 *
	 * @return bool
	 */
	public static function AddLog( $cash_drawer_id, $note, $pre_balance, $amount, $log_type, $ref_type, $ref_id ) {
		$newobj = new self();
		$newobj->cash_drawer_id( $cash_drawer_id );
		$newobj->user_id( get_current_user_id() );
		$newobj->note( $note );
		$newobj->pre_balance( $pre_balance );
		$newobj->amount( $amount );
		$newobj->log_type( $log_type ); 		$newobj->ref_type( $ref_type ); 		$newobj->ref_id( $ref_id ); 		$newobj->entry_time( gmdate( 'Y-m-d' ) );
		return $newobj->save();
	}
	/**
	 * The create db table is generated by appsbd
	 */
	public static function create_db_table() {
		$this_obj = new static();
		$table    = $this_obj->db->prefix . $this_obj->table_name;
		if ( $this_obj->db->get_var( "show tables like '{$table}'" ) != $table ) {
			$sql = "CREATE TABLE `{$table}` (
					  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
					  `cash_drawer_id` int(11) NOT NULL,
					  `ref_id` char(50) NOT NULL,
					  `note` char(255) NOT NULL DEFAULT '',
					  `pre_balance` decimal(10,2) unsigned NOT NULL DEFAULT 0.00,
					  `amount` decimal(10,2) unsigned NOT NULL DEFAULT 0.00,
					  `user_id` int(11) NOT NULL,
					  `log_type` char(1) NOT NULL DEFAULT 'C' COMMENT 'radio(D=Debit,C=Credit)',
					  `ref_type` char(1) NOT NULL DEFAULT 'O' COMMENT 'radio(O=Order,C=Close)',
					  `closing_balance` decimal(10,2) unsigned DEFAULT 0.00,
					  `entry_time` timestamp NULL DEFAULT current_timestamp(),
					  PRIMARY KEY (`id`) USING BTREE
					) ";
			require_once ABSPATH . 'wp-admin/includes/upgrade.php';
			dbDelta( $sql );
		}
	}
}
