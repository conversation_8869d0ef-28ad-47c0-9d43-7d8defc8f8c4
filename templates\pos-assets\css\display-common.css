/* Common Styles for All Display Screens */

/* CSS Variables for theming */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --border-radius: 10px;
    --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-base: 1rem;
    --font-size-lg: 1.25rem;
    --font-size-xl: 1.5rem;
    --font-size-xxl: 2rem;
    
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
}

/* Dark theme variables */
[data-theme="dark"] {
    --primary-color: #0d6efd;
    --light-color: #212529;
    --dark-color: #f8f9fa;
    --box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

/* Reset and base styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    margin: 0;
    padding: 0;
    direction: rtl;
    background: var(--light-color);
    color: var(--dark-color);
}

/* Common layout classes */
.display-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.display-header {
    background: white;
    box-shadow: var(--box-shadow);
    border-bottom: 3px solid var(--primary-color);
    padding: var(--spacing-md);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.display-content {
    flex: 1;
    padding: var(--spacing-md);
    overflow-y: auto;
}

.display-footer {
    background: white;
    border-top: 1px solid #e9ecef;
    padding: var(--spacing-md);
    text-align: center;
}

/* Common card styles */
.display-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    transition: var(--transition);
}

.display-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.display-card.urgent {
    border-left: 4px solid var(--danger-color);
    animation: pulse-urgent 2s infinite;
}

@keyframes pulse-urgent {
    0% { box-shadow: var(--box-shadow); }
    50% { box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3); }
    100% { box-shadow: var(--box-shadow); }
}

/* Common button styles */
.display-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: calc(var(--border-radius) / 2);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    text-decoration: none;
    font-size: var(--font-size-base);
}

.display-btn:hover {
    transform: translateY(-1px);
}

.display-btn:active {
    transform: translateY(0);
}

.display-btn.btn-primary {
    background: var(--primary-color);
    color: white;
}

.display-btn.btn-primary:hover {
    background: #0056b3;
}

.display-btn.btn-success {
    background: var(--success-color);
    color: white;
}

.display-btn.btn-success:hover {
    background: #218838;
}

.display-btn.btn-warning {
    background: var(--warning-color);
    color: #212529;
}

.display-btn.btn-warning:hover {
    background: #e0a800;
}

.display-btn.btn-danger {
    background: var(--danger-color);
    color: white;
}

.display-btn.btn-danger:hover {
    background: #c82333;
}

.display-btn.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.display-btn.btn-secondary:hover {
    background: #5a6268;
}

/* Common badge styles */
.display-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.display-badge.badge-primary {
    background: var(--primary-color);
    color: white;
}

.display-badge.badge-success {
    background: var(--success-color);
    color: white;
}

.display-badge.badge-warning {
    background: var(--warning-color);
    color: #212529;
}

.display-badge.badge-danger {
    background: var(--danger-color);
    color: white;
}

.display-badge.badge-info {
    background: var(--info-color);
    color: white;
}

.display-badge.badge-light {
    background: #e9ecef;
    color: #495057;
}

/* Common status indicators */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-left: var(--spacing-xs);
}

.status-indicator.status-waiting {
    background: var(--warning-color);
}

.status-indicator.status-preparing {
    background: #fd7e14;
}

.status-indicator.status-ready {
    background: var(--success-color);
}

.status-indicator.status-served {
    background: #6c5ce7;
}

/* Common loading states */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-xxl);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    margin-top: var(--spacing-md);
    color: var(--secondary-color);
    font-size: var(--font-size-lg);
}

/* Common empty states */
.empty-state {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--secondary-color);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: var(--spacing-md);
    color: var(--dark-color);
}

.empty-state p {
    font-size: var(--font-size-lg);
    margin: 0;
}

/* Common grid layouts */
.display-grid {
    display: grid;
    gap: var(--spacing-md);
}

.display-grid.grid-1 {
    grid-template-columns: 1fr;
}

.display-grid.grid-2 {
    grid-template-columns: repeat(2, 1fr);
}

.display-grid.grid-3 {
    grid-template-columns: repeat(3, 1fr);
}

.display-grid.grid-4 {
    grid-template-columns: repeat(4, 1fr);
}

.display-grid.grid-auto {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

/* Common flex utilities */
.d-flex {
    display: flex;
}

.flex-column {
    flex-direction: column;
}

.justify-content-center {
    justify-content: center;
}

.justify-content-between {
    justify-content: space-between;
}

.align-items-center {
    align-items: center;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-1 {
    flex: 1;
}

/* Common spacing utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

/* Common text utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }
.text-muted { color: var(--secondary-color); }

.fw-normal { font-weight: 400; }
.fw-bold { font-weight: 700; }

.fs-sm { font-size: 0.875rem; }
.fs-base { font-size: var(--font-size-base); }
.fs-lg { font-size: var(--font-size-lg); }
.fs-xl { font-size: var(--font-size-xl); }
.fs-xxl { font-size: var(--font-size-xxl); }

/* Common animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.animate-fadeIn {
    animation: fadeIn 0.5s ease-out;
}

.animate-fadeInUp {
    animation: fadeInUp 0.5s ease-out;
}

.animate-fadeInDown {
    animation: fadeInDown 0.5s ease-out;
}

.animate-slideInRight {
    animation: slideInRight 0.5s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* Common form elements */
.display-form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    transition: var(--transition);
}

.display-form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.display-form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-left: 2.5rem;
}

/* Common modal styles */
.display-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.display-modal-content {
    background: white;
    border-radius: var(--border-radius);
    padding: var(--spacing-xl);
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

/* Common toast notifications */
.display-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    color: white;
    font-weight: 600;
    animation: slideInRight 0.3s ease-out;
}

.display-toast.toast-success {
    background: var(--success-color);
}

.display-toast.toast-error {
    background: var(--danger-color);
}

.display-toast.toast-warning {
    background: var(--warning-color);
    color: #212529;
}

.display-toast.toast-info {
    background: var(--info-color);
}

/* Responsive design */
@media (max-width: 768px) {
    .display-grid.grid-2,
    .display-grid.grid-3,
    .display-grid.grid-4 {
        grid-template-columns: 1fr;
    }
    
    .display-header {
        padding: var(--spacing-sm);
    }
    
    .display-content {
        padding: var(--spacing-sm);
    }
    
    .display-card {
        padding: var(--spacing-md);
    }
    
    .display-btn {
        padding: var(--spacing-sm);
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    :root {
        --font-size-base: 0.9rem;
        --font-size-lg: 1.1rem;
        --font-size-xl: 1.3rem;
        --font-size-xxl: 1.8rem;
    }
    
    .display-modal-content {
        padding: var(--spacing-lg);
        margin: var(--spacing-md);
    }
    
    .display-toast {
        right: 10px;
        left: 10px;
        min-width: auto;
    }
}

/* Print styles */
@media print {
    .display-header,
    .display-footer,
    .display-btn,
    .display-modal {
        display: none !important;
    }
    
    .display-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    }
    
    .display-card {
        border: 2px solid #000;
    }
    
    .display-btn {
        border: 2px solid currentColor;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
