<?php
/**
 * Its api for vendor
 *
 * @since: 12/07/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package VitePos\Api\V1
 */

namespace VitePos\Api\V1;

use Appsbd\V1\libs\API_Data_Response;
use VitePos\Core\ViteposModel;
use VitePos\Libs\API_Base;
use Vitepos\Models\Database\Mapbd_Pos_Addon;
use Vitepos\Models\Database\Mapbd_Pos_Addon_Field;
use Vitepos\Models\Database\Mapbd_Pos_Addon_Field_Option;
use Vitepos\Models\Database\Mapbd_Pos_Addon_Rule;
use Vitepos\Models\Database\Mapbd_Pos_Addon_Rule_Group;
use Vitepos\Models\Mapbd_Addon_Details;
use VitePos\Modules\POS_Settings;

/**
 * Class pos_vendor_api
 *
 * @package VitePos\Api\V1
 */
class Pos_Addon_Api extends API_Base {

	/**
	 * The set api base is generated by appsbd
	 *
	 * @return mixed|string
	 */
	public function set_api_base() {
		return 'addon';
	}

	/**
	 * The routes is generated by appsbd
	 *
	 * @return mixed|void
	 */
	public function routes() {
		$this->register_rest_route( 'POST', 'list', array( $this, 'addon_list' ) );
		$this->register_rest_route( 'POST', 'create', array( $this, 'create_addon' ) );
		$this->register_rest_route( 'POST', 'update', array( $this, 'update_addon' ) );
		$this->register_rest_route( 'POST', 'delete-addon', array( $this, 'delete_addon' ) );
		$this->register_rest_route( 'POST', 'change-status', array( $this, 'change_addon_status' ) );
		$this->register_rest_route( 'GET', 'details/(?P<id>\d+)', array( $this, 'addon_details' ) );
	}
	/**
	 * The set route permission is generated by appsbd
	 *
	 * @param \VitePos\Libs\any $route Its string.
	 *
	 * @return bool
	 */
	public function set_route_permission( $route ) {
		switch ( $route ) {
			case 'list':
				return current_user_can( 'addon-menu' );
			case 'create':
				return current_user_can( 'addon-add' );
			case 'update':
				return current_user_can( 'addon-edit' );
			case 'delete-addon':
				return current_user_can( 'addon-delete' );
			case 'change-status':
				return current_user_can( 'addon-status-change' );
			default:
				POS_Settings::is_pos_user();
		}

		return parent::set_route_permission( $route );
	}

	/**
	 * The vendor list is generated by appsbd
	 *
	 * @return API_Data_Response
	 */
	public function addon_list() {
		$response_data        = new API_Data_Response();
		$mainobj              = new Mapbd_Pos_Addon();
		$response_data->limit = $this->get_payload( 'limit', 20 );
		$response_data->page  = $this->get_payload( 'page', 1 );
		$src_props            = $this->get_payload( 'src_by', array() );
		$sort_props           = $this->get_payload( 'sort_by', array() );
		$all_props            = 'title,id';
		$mainobj->set_search_by_param( $src_props, $all_props );
		$mainobj->set_sort_by_param( $sort_props );
		if ( $response_data->set_total_records( $mainobj->count_all() ) ) {
			$response_data->rowdata = $mainobj->select_all_grid_data( '', '', '', $response_data->limit, $response_data->limit_start() );
		}
		return $response_data;
	}

	/**
	 * The vendor details is generated by appsbd
	 *
	 * @param any $data Its string.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function addon_details( $data ) {
		if ( ! empty( $data['id'] ) ) {
			$id    = intval( $data['id'] );
			$addon = Mapbd_Addon_Details::get_details( $id );
			$this->set_response( ! empty( $addon ), '', $addon );
			return $this->response;
		}
		$this->set_response( false, 'data not found or invalid param' );
		return $this->response;

	}

	/**
	 * The create vendor is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function create_addon() {
		$addon_obj = new Mapbd_Pos_Addon();
		$addon_obj->set_from_array( $this->payload );
		$addon_obj->added_by( $this->get_current_user_id() );
		$is_item_ok = true;
		$fields     = array();
		if ( $addon_obj->is_valid_form( true ) ) {
			if ( ! empty( $this->payload['fields'] ) ) {
				foreach ( $this->payload['fields'] as $field ) {
					$demo_field          = new \stdClass();
					$demo_field->options = array();
					$addon_field         = new Mapbd_Pos_Addon_Field();
					$addon_field->set_from_array( $field );
					if ( ! $addon_field->is_valid_form( true ) ) {
						$is_item_ok = false;
					} else {
						if ( ! empty( $field['options'] ) ) {
							foreach ( $field['options'] as $option ) {
								$field_option = new Mapbd_Pos_Addon_Field_Option();
								$field_option->set_from_array( $option );
								if ( ! $field_option->is_valid_form( true ) ) {
									$is_item_ok = false;
								} else {
									$demo_field->options[] = $field_option;
								}
							}
						}
						$demo_field->field = $addon_field;
						$fields[]          = $demo_field;
					}
				}
			}
			$rules_groups = array();
			if ( ! empty( $this->payload['rule_group'] ) ) {
				foreach ( $this->payload['rule_group'] as $r_group ) {
					$demo_group        = new \stdClass();
					$demo_group->rules = array();
					$addon_r_group     = new Mapbd_Pos_Addon_Rule_Group();
					$addon_r_group->set_from_array( $r_group );
					if ( ! $addon_r_group->set_from_array( $r_group, true ) ) {
						$is_item_ok = false;
					} else {
						if ( ! empty( $r_group['rules'] ) ) {
							foreach ( $r_group['rules'] as $rule ) {
								$group_rule = new Mapbd_Pos_Addon_Rule();
								if ( ! $group_rule->set_from_array( $rule, true ) ) {
									$is_item_ok = false;
								} else {
									$demo_group->rules[] = $group_rule;
								}
							}
						}
						$demo_group->group = $addon_r_group;
						$rules_groups[]    = $demo_group;
					}
				}
			}

			if ( $is_item_ok ) {
				if ( $addon_obj->save() ) {
					foreach ( $fields as $field ) {
						$field->field->addon_id( $addon_obj->id );
						if ( $field->field->save() ) {
							foreach ( $field->options as $field_option ) {
								$field_option->field_id( $field->field->id );
								$field_option->save();
							}
						}
					}
					foreach ( $rules_groups as $rule_group ) {
						$rule_group->group->addon_id( $addon_obj->id );
						$rule_group->group->status( 'A' );
						if ( $rule_group->group->save() ) {
							foreach ( $rule_group->rules as $rule ) {
								$rule->rule_group_id( $rule_group->group->id );
								$rule->save();
							}
						}
					}
					$this->add_info( 'successfully saved' );
					$this->set_response( true );
				} else {
					$this->set_response( false, appsbd_get_msg_api() );
				}
			}
		}
		return $this->response->get_response();
	}

	/**
	 * The update status is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function update_addon() {
		if ( ! empty( $this->payload['id'] ) ) {
			if ( Mapbd_Addon_Details::update( $this->payload ) ) {
				$this->response->set_response( true, appsbd_get_msg_api() );
				return $this->response;
			} else {
				$this->response->set_response( false, appsbd_get_msg_api() );
				return $this->response;
			}
		} else {
			$this->response->set_response( false, 'Nothing to update' );
			return $this->response;
		}
	}

	/**
	 * The delete vendor is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function delete_addon() {
		if ( ! empty( $this->payload['id'] ) ) {
			$id = intval( $this->payload['id'] );
			$mr = new Mapbd_Pos_Addon();
			$mr->id( $id );
			if ( $mr->Select() ) {
				if ( Mapbd_Pos_Addon::delete_by_id( $id ) ) {
					$this->add_info( 'Addon deleted successfully' );
					$this->response->set_response( true );

				} else {
					$this->add_error( 'Addon delete failed' );
					$this->response->set_response( false );
				}
			} else {
				$this->add_error( 'No addon found with this param' );
				$this->response->set_response( false );
			}
			return $this->response->get_response();
		}
	}
	/**
	 * The delete vendor is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function change_addon_status() {
		if ( ! empty( $this->payload['id'] ) ) {
			$id = intval( $this->payload['id'] );
			$mr = new Mapbd_Pos_Addon();
			$mr->id( $id );
			if ( $mr->Select() ) {
				$status = 'A';
				if ( 'A' == $mr->status ) {
					$status = 'I';
				}
				$uo = new Mapbd_Pos_Addon();
				$uo->status( $status );
				$uo->set_where_update( 'id', $id );
				if ( $uo->Update() ) {
					$this->add_info( 'Status changed successfully' );
					$this->response->set_response( true );
				} else {
					$this->add_error( 'Status change failed,please try again' );
					$this->response->set_response( false );
				}
			} else {
				$this->add_error( 'No addon found with this param' );
				$this->response->set_response( false );
			}
			return $this->response->get_response();
		}
	}
}
