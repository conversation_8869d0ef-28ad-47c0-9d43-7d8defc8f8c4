# شاشات المطبخ وعرض الزبون وشاشة التحضير

تم إضافة ثلاث شاشات جديدة لنظام VitePos لتحسين إدارة المطبخ وتجربة العملاء:

## الشاشات المضافة

### 1. شاشة المطبخ (Kitchen Display)
**الرابط:** `yoursite.com/kitchen-display`

**الوظائف:**
- عرض جميع الطلبات الواردة للمطبخ
- تصنيف الطلبات حسب الحالة (في الانتظار، قيد التحضير، جاهز للتقديم)
- إمكانية بدء تحضير الطلب
- إمكانية إكمال تحضير الطلب
- إمكانية رفض الطلب
- إضافة ملاحظات للطلبات
- تحديث تلقائي كل 5 ثوانٍ
- عرض الوقت المنقضي لكل طلب
- تنبيهات للطلبات العاجلة (أكثر من 30 دقيقة)

**المميزات:**
- واجهة سهلة الاستخدام
- ألوان مميزة لكل حالة طلب
- عداد للطلبات في كل فئة
- إشعارات صوتية عند إكمال الطلبات
- دعم اللمس للأجهزة اللوحية

### 2. شاشة عرض الزبون (Customer Display)
**الرابط:** `yoursite.com/customer-display`

**الوظائف:**
- عرض الطلب الحالي للعميل
- عرض حالة الطلبات (قيد التحضير، جاهز للاستلام، تم التقديم)
- قائمة انتظار الطلبات
- عرض الوقت والتاريخ الحالي
- رسائل ترحيبية للعملاء

**المميزات:**
- تصميم جذاب وودود للعملاء
- تحديث تلقائي كل 3 ثوانٍ
- إشعارات صوتية عند جاهزية الطلب
- رسوم متحركة لتحسين التجربة
- عرض شعار المطعم

### 3. شاشة التحضير (Preparation Screen)
**الرابط:** `yoursite.com/preparation-screen`

**الوظائف:**
- إدارة الطلبات حسب الفئة
- تصفية الطلبات بناءً على فئة المنتجات
- عرض إحصائيات الطلبات
- سحب وإفلات الطلبات بين الحالات
- مؤقت لوردية العمل
- عرض تفاصيل كاملة للطلبات

**المميزات:**
- تنظيم الطلبات في أعمدة حسب الحالة
- فلترة حسب فئة المنتجات
- إحصائيات مباشرة للأداء
- واجهة سهلة للسحب والإفلات
- عرض تفصيلي لكل طلب

## كيفية الوصول للشاشات

### الروابط المباشرة:
```
https://yoursite.com/kitchen-display     - شاشة المطبخ
https://yoursite.com/customer-display    - شاشة عرض الزبون
https://yoursite.com/preparation-screen  - شاشة التحضير
```

## الملفات المضافة

### ملفات PHP (Templates):
- `templates/kitchen-display.php` - شاشة المطبخ
- `templates/customer-display.php` - شاشة عرض الزبون
- `templates/preparation-screen.php` - شاشة التحضير

### ملفات CSS (التصميم):
- `templates/pos-assets/css/kitchen-display.css` - تصميم شاشة المطبخ
- `templates/pos-assets/css/customer-display.css` - تصميم شاشة عرض الزبون
- `templates/pos-assets/css/preparation-screen.css` - تصميم شاشة التحضير

### ملفات JavaScript (الوظائف):
- `templates/pos-assets/js/kitchen-display.js` - وظائف شاشة المطبخ
- `templates/pos-assets/js/customer-display.js` - وظائف شاشة عرض الزبون
- `templates/pos-assets/js/preparation-screen.js` - وظائف شاشة التحضير

### التحديثات على الملفات الموجودة:
- `vitepos/modules/class-pos-settings.php` - إضافة rewrite rules والدوال الجديدة

## الإعدادات المطلوبة

### 1. تحديث Rewrite Rules
تم إضافة القواعد التالية تلقائياً:
```php
add_rewrite_rule( '^kitchen-display/?$', 'index.php?vitepos_kitchen=true', 'top' );
add_rewrite_rule( '^customer-display/?$', 'index.php?vitepos_customer=true', 'top' );
add_rewrite_rule( '^preparation-screen/?$', 'index.php?vitepos_preparation=true', 'top' );
```

### 2. Query Variables
تم إضافة المتغيرات التالية:
- `vitepos_kitchen`
- `vitepos_customer`
- `vitepos_preparation`

## API Endpoints المستخدمة

الشاشات تستخدم نفس API endpoints الموجودة في النظام:
- `vitepos/v1/restaurant/kitchen-order-list` - قائمة طلبات المطبخ
- `vitepos/v1/restaurant/start-preparing` - بدء تحضير الطلب
- `vitepos/v1/restaurant/complete-preparing` - إكمال تحضير الطلب
- `vitepos/v1/restaurant/deny-order` - رفض الطلب
- `vitepos/v1/restaurant/add-kitchen-note` - إضافة ملاحظة
- `vitepos/v1/restaurant/served-list` - قائمة الطلبات المقدمة
- `vitepos/v1/product/categories` - قائمة الفئات
- `vitepos/v1/order/details` - تفاصيل الطلب

## المتطلبات التقنية

### المتصفحات المدعومة:
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### الدقة المُوصى بها:
- شاشة المطبخ: 1920x1080 أو أعلى
- شاشة العرض للزبون: 1366x768 أو أعلى
- شاشة التحضير: 1920x1080 أو أعلى

## الاستخدام

### للمطبخ:
1. افتح شاشة المطبخ على جهاز مخصص
2. راقب الطلبات الواردة
3. اضغط "بدء التحضير" عند البدء
4. اضغط "اكتمل" عند الانتهاء

### لعرض الزبائن:
1. افتح شاشة عرض الزبون على شاشة مرئية للعملاء
2. سيتم عرض الطلبات تلقائياً
3. العملاء يمكنهم متابعة حالة طلباتهم

### لإدارة التحضير:
1. افتح شاشة التحضير
2. استخدم الفلتر لعرض فئة معينة
3. اسحب الطلبات بين الأعمدة لتغيير الحالة
4. راقب الإحصائيات والأداء

## الصيانة والتحديث

### تحديث الشاشات:
- الشاشات تحدث نفسها تلقائياً
- يمكن الضغط على F5 للتحديث اليدوي
- يمكن استخدام زر التحديث في كل شاشة

### استكشاف الأخطاء:
1. تأكد من تفعيل JavaScript
2. تحقق من اتصال الإنترنت
3. تأكد من صحة إعدادات API
4. راجع console المتصفح للأخطاء

## الأمان

- الشاشات تستخدم نفس نظام الأمان الموجود في VitePos
- يُنصح بتشغيل الشاشات على شبكة محلية آمنة
- يمكن تقييد الوصول حسب IP إذا لزم الأمر

## الدعم الفني

في حالة وجود مشاكل:
1. تحقق من ملفات الـ logs
2. تأكد من تحديث النظام
3. راجع إعدادات الخادم
4. تواصل مع فريق الدعم الفني

---

**ملاحظة:** هذه الشاشات مصممة للعمل مع نظام VitePos الحالي وتتطلب أن يكون النظام يعمل بشكل صحيح.
