/* Customer Display Styles */
.customer-display-body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
    direction: rtl;
    overflow-x: hidden;
}

/* Header Styles */
.customer-header {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-bottom: 4px solid #0984e3;
}

.header-content {
    animation: fadeInDown 1s ease-out;
}

.restaurant-logo {
    max-height: 80px;
    width: auto;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.welcome-title {
    color: #2d3436;
    font-weight: 700;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-subtitle {
    color: #636e72;
    font-size: 1.2rem;
    margin-bottom: 0;
}

/* Current Order Section */
.current-order-section {
    padding: 2rem 1rem;
}

.current-order-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    animation: slideInUp 0.8s ease-out;
}

.order-header {
    text-align: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.order-header h3 {
    color: #2d3436;
    font-weight: 700;
    font-size: 1.8rem;
}

.order-content {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.no-order-message {
    text-align: center;
    color: #636e72;
    font-size: 1.2rem;
}

.no-order-message i {
    color: #ddd;
    margin-bottom: 1rem;
}

/* Order Details */
.order-details {
    text-align: right;
}

.order-number {
    font-size: 2rem;
    font-weight: 700;
    color: #0984e3;
    margin-bottom: 1rem;
    text-align: center;
}

.order-items {
    margin-bottom: 1.5rem;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.order-item:hover {
    background: #e9ecef;
    transform: translateX(-5px);
}

.item-name {
    font-weight: 600;
    color: #2d3436;
    font-size: 1.1rem;
}

.item-quantity {
    background: #0984e3;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    min-width: 50px;
    text-align: center;
}

.order-total {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #00b894;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
}

/* Status Section */
.status-section {
    padding: 1rem;
    margin-bottom: 2rem;
}

.status-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    animation: fadeInUp 0.8s ease-out;
}

.status-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.status-card.preparing {
    border-top: 4px solid #fdcb6e;
}

.status-card.ready {
    border-top: 4px solid #00b894;
}

.status-card.served {
    border-top: 4px solid #6c5ce7;
}

.status-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.status-card.preparing .status-icon {
    color: #fdcb6e;
}

.status-card.ready .status-icon {
    color: #00b894;
}

.status-card.served .status-icon {
    color: #6c5ce7;
}

.status-info h4 {
    color: #2d3436;
    font-weight: 700;
    margin-bottom: 1rem;
}

.order-numbers {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
}

.order-number-badge {
    background: #e9ecef;
    color: #495057;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 1.1rem;
    animation: bounceIn 0.6s ease-out;
}

.order-number-badge.highlight {
    background: #0984e3;
    color: white;
    animation: pulse 2s infinite;
}

/* Queue Section */
.queue-section {
    padding: 1rem;
}

.queue-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.queue-header h3 {
    color: white;
    font-weight: 700;
    font-size: 1.8rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.queue-container {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 1rem;
    max-height: 300px;
    overflow: hidden;
}

.queue-scroll {
    max-height: 250px;
    overflow-y: auto;
    padding-right: 10px;
}

.queue-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: slideInRight 0.5s ease-out;
}

.queue-item.current {
    background: #0984e3;
    color: white;
    font-weight: 700;
    animation: pulse 2s infinite;
}

.queue-order-number {
    font-size: 1.2rem;
    font-weight: 700;
}

.queue-status {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 600;
}

.status-waiting {
    background: #fdcb6e;
    color: #2d3436;
}

.status-preparing {
    background: #e17055;
    color: white;
}

.status-ready {
    background: #00b894;
    color: white;
}

/* Footer */
.customer-footer {
    background: rgba(255, 255, 255, 0.9);
    border-top: 3px solid #0984e3;
    margin-top: auto;
}

.footer-content {
    animation: fadeInUp 1s ease-out;
}

.current-time {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3436;
    margin-bottom: 0.5rem;
}

.footer-message {
    color: #636e72;
    font-size: 1.1rem;
    margin-bottom: 0;
}

/* Animations */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-title {
        font-size: 2rem;
    }
    
    .welcome-subtitle {
        font-size: 1rem;
    }
    
    .current-order-card {
        padding: 1.5rem;
    }
    
    .order-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .status-section .col-md-4 {
        margin-bottom: 1rem;
    }
    
    .queue-scroll {
        max-height: 200px;
    }
}

/* Scrollbar Styling */
.queue-scroll::-webkit-scrollbar {
    width: 8px;
}

.queue-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.queue-scroll::-webkit-scrollbar-thumb {
    background: #0984e3;
    border-radius: 10px;
}

.queue-scroll::-webkit-scrollbar-thumb:hover {
    background: #0770c4;
}
