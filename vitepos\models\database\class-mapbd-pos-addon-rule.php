<?php
/**
 * Pos Vendor Database Model
 *
 * @package Vitepos\Models\Database
 */

namespace Vitepos\Models\Database;

use VitePos\Core\ViteposModel;

/**
 * Class Mapbd_pos_vendor
 *
 * @properties id,name,email,contact_no,vendor_note,status,added_by
 */
class Mapbd_Pos_Addon_Rule extends ViteposModel {
	/**
	 * Its property id
	 *
	 * @var int
	 */
	public $id;
	/**
	 * Its property prop
	 *
	 * @var string
	 */
	public $prop;
	/**
	 * Its property value
	 *
	 * @var string
	 */
	public $val;
	/**
	 * Its property condition
	 *
	 * @var bool
	 */
	public $cond;
	/**
	 * Its property rule_group_id
	 *
	 * @var string
	 */
	public $rule_group_id;


	/**
	 * Mapbd_pos_vendor constructor.
	 */
	public function __construct() {
		parent::__construct();
		$this->set_validation();
		$this->table_name     = 'apbd_pos_addon_rule';
		$this->primary_key    = 'id';
		$this->unique_key     = array();
		$this->multi_key      = array();
		$this->auto_inc_field = array( 'id' );
		$this->app_base_name  = 'apbd-elite-pos';

	}


	/**
	 * The set validation is generated by appsbd
	 */
	public function set_validation() {
		$this->validations = array(
			'id'            => array(
				'Text' => 'Id',
				'Rule' => 'max_length[11]|integer',
			),
			'prop'          => array(
				'Text' => 'Property',
				'Rule' => 'required|max_length[150]',
			),
			'val'           => array(
				'Text' => 'Value',
				'Rule' => 'required|max_length[150]',
			),
			'cond'          => array(
				'Text' => 'Condition',
				'Rule' => 'max_length[2]',
			),
			'rule_group_id' => array(
				'Text' => 'Rule Group Id',
				'Rule' => 'max_length[11]|integer',
			),

		);
	}

	/**
	 * The get property raw options is generated by appsbd
	 *
	 * @param \Appsbd\V1\Core\any $property Its string.
	 * @param false               $is_with_select Its bool.
	 *
	 * @return array|string[]
	 */
	public function get_property_raw_options( $property, $is_with_select = false ) {
		$return_obj = array();
		switch ( $property ) {
			case 'condition':
				$return_obj = array(
					'eq' => 'Equal',
					'ne' => 'Not Equal',
				);
				break;
			default:
		}
		if ( $is_with_select ) {
			return array_merge( array( '' => 'Select' ), $return_obj );
		}
		return $return_obj;

	}

	/**
	 * The create db table is generated by appsbd
	 */
	public static function create_db_table() {
		$this_obj = new static();
		$table    = $this_obj->db->prefix . $this_obj->table_name;
		if ( $this_obj->db->get_var( "show tables like '{$table}'" ) != $table ) {
			$sql = "CREATE TABLE `{$table}` (
					  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  						`prop` char(150) NOT NULL DEFAULT '',
  						`val` char(150) NOT NULL DEFAULT '',
  						`cond` char(2) NOT NULL DEFAULT 'eq' COMMENT 'radio(eq=Equal,ne=Not Equal)',
  						`rule_group_id` int(11) unsigned NOT NULL,
  						PRIMARY KEY (`id`)
					) ";
			require_once ABSPATH . 'wp-admin/includes/upgrade.php';
			dbDelta( $sql );
		}
	}

	/**
	 * The delete by id is generated by appsbd
	 *
	 * @param any $id Its vendor id param.
	 *
	 * @return bool
	 */
	public static function delete_by_id( $id ) {
		return parent::delete_by_key_value( 'id', $id );
	}
	/**
	 * The delete by id is generated by appsbd
	 *
	 * @param any $rule_group_id Its vendor id param.
	 *
	 * @return bool
	 */
	public static function delete_by_group_id( $rule_group_id ) {
		return parent::delete_by_key_value( 'rule_group_id', $rule_group_id, true );
	}

	/**
	 * The get all rules by is generated by appsbd
	 *
	 * @param mixed $group_id Its group_id param.
	 */
	public static function get_all_rules_by( $group_id ) {
		$rules = self::find_all_grid_data_by( 'rule_group_id', $group_id );
		foreach ( $rules as &$rule ) {
			if ( 'C' == $rule->prop ) {
				$category_id = $rule->val;
				$rule->val   = array();
				self::get_categories_id_with_child( $category_id, $rule->val );
			} elseif ( 'P' == $rule->prop ) {
				$product_id = $rule->val;
				$rule->val  = array();
				self::get_products_id_with_child( $product_id, $rule->val );
			}
		}
		return $rules;
	}

	/**
	 * The get categories id with child is generated by appsbd
	 *
	 * @param mixed $cat_id Its cat_id param.
	 * @param array $rules Its rules param.
	 */
	public static function get_categories_id_with_child( $cat_id, &$rules ) {
		$child_arg = array(
			'hide_empty' => false,
			'parent'     => $cat_id,
		);
		$rules[]   = intval( $cat_id );
		$child_cat = get_terms( 'product_cat', $child_arg );
		foreach ( $child_cat as $key => $item ) {
			if ( $item->parent == $cat_id ) {
				self::get_categories_id_with_child( $item->term_id, $rules );
			}
		}
	}
	/**
	 * The get categories id with child is generated by appsbd
	 *
	 * @param mixed $product_id Its cat_id param.
	 * @param array $rules Its rules param.
	 */
	public static function get_products_id_with_child( $product_id, &$rules ) {
		$product_id = intval( $product_id );
		$product    = wc_get_product( $product_id );
		$rules[]    = $product_id;
		if ( $product->is_type( 'variable' ) && $product->has_child() ) {
			$rules = array_merge( $rules, $product->get_children() );
		}
	}

}
