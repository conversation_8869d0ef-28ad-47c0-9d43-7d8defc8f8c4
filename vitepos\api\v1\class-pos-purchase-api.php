<?php
/**
 * Its api for purchase
 *
 * @since: 12/07/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package VitePos\Api\V1
 */

namespace VitePos\Api\V1;

use Appsbd\V1\libs\API_Data_Response;
use VitePos\Libs\API_Base;
use VitePos\Libs\POS_Product;
use Vitepos\Models\Database\Mapbd_pos_purchase;
use Vitepos\Models\Database\Mapbd_Pos_Purchase_Item;
use Vitepos\Models\Database\Mapbd_Pos_Stock_Log;
use Vitepos\Models\Database\Mapbd_Pos_Stock_Transfer;
use Vitepos\Models\Database\Mapbd_Pos_Stock_Transfer_Item;
use Vitepos\Models\Database\Mapbd_Pos_Warehouse;
use VitePos\Modules\POS_Settings;
use WPMailSMTP\Vendor\Monolog\Handler\DeduplicationHandler;

/**
 * Class pos_purchase_api
 *
 * @package VitePos\Api\V1
 */
class Pos_Purchase_Api extends API_Base {

	/**
	 * The set api base is generated by appsbd
	 *
	 * @return mixed|string
	 */
	public function set_api_base() {
		return 'purchase';
	}

	/**
	 * The routes is generated by appsbd
	 *
	 * @return mixed|void
	 */
	public function routes() {
		$this->register_rest_route( 'POST', 'list', array( $this, 'purchase_list' ) );
		$this->register_rest_route( 'POST', 'transfer-list', array( $this, 'transfer_list' ) );
		$this->register_rest_route( 'POST', 'receive-list', array( $this, 'receive_list' ) );
		$this->register_rest_route( 'POST', 'updated-price-list', array( $this, 'updated_price_list' ) );
		$this->register_rest_route( 'POST', 'create', array( $this, 'create_purchase' ) );
		$this->register_rest_route( 'POST', 'stock-transfer', array( $this, 'stock_transfer' ) );
		$this->register_rest_route( 'GET', 'stock-log/(?P<id>\d+)', array( $this, 'stock_logs' ) );
		$this->register_rest_route( 'POST', 'stock-receive', array( $this, 'stock_receive' ) );
		$this->register_rest_route( 'POST', 'stock-accept', array( $this, 'stock_accept' ) );
		$this->register_rest_route( 'POST', 'stock-decline', array( $this, 'stock_decline' ) );
		$this->register_rest_route( 'GET', 'details/(?P<id>\d+)', array( $this, 'purchase_details' ) );
		$this->register_rest_route( 'GET', 'transfer-details/(?P<id>\d+)', array( $this, 'transfer_details' ) );
		$this->register_rest_route( 'GET', 'updated-product-details/(?P<id>\d+)', array( $this, 'updated_product_details' ) );
		$this->register_rest_route( 'POST', 'update-product-price', array( $this, 'update_product_price' ) );
		$this->register_rest_route( 'POST', 'ignore-update-price', array( $this, 'ignore_update_price' ) );
	}

	/**
	 * The set route permission is generated by appsbd
	 *
	 * @param \VitePos\Libs\any $route Its string.
	 *
	 * @return bool
	 */
	public function set_route_permission( $route ) {
		switch ( $route ) {
			case 'list':
				return current_user_can( 'purchase-menu' ) || current_user_can( 'stock-menu' ) || current_user_can( 'can-see-any-outlet-purchases' );
			case 'create':
				return current_user_can( 'purchase-add' ) || current_user_can( 'stock-add' );
			case 'details':
				return current_user_can( 'purchase-details' );
			case 'updated-price-list':
				return current_user_can( 'updated-price-list' );
			case 'update-product-price':
				return current_user_can( 'update-price' );
			case 'ignore-update-price':
				return current_user_can( 'ignore-update-price' );
			default:
				POS_Settings::is_pos_user();
		}

		return parent::set_route_permission( $route );
	}
	/**
	 * The purchase list is generated by appsbd
	 *
	 * @return API_Data_Response
	 */
	public function purchase_list() {
		$mainobj              = new Mapbd_pos_purchase();
		$response_data        = new API_Data_Response();
		$response_data->limit = $this->get_payload( 'limit', 20 );
		$response_data->page  = $this->get_payload( 'page', 1 );
		$src_props            = $this->get_payload( 'src_by', array() );
		$mainobj->set_search_by_param( $src_props );
		if ( ! POS_Settings::is_admin_user() && ! current_user_can( 'can-see-any-outlet-purchases' ) ) {
			$outlets = get_user_meta( $this->get_current_user_id(), 'outlet_id', true );
			if ( is_array( $outlets ) ) {
				$outlet_in = "'" . implode( "','", $outlets ) . "'";
				$mainobj->warehouse_id( "IN ($outlet_in)", true );
			} else {
				$response_data->set_total_records( 0 );
				return $response_data;
			}
		}
		$order_by = 'purchase_date';
		$order    = 'DESC';
		if ( ! empty( $this->payload['sort_by'][0] ) ) {
			$order_by = $this->payload['sort_by'][0]['prop'];
			$order    = $this->payload['sort_by'][0]['ord'];
		}

		if ( $response_data->set_total_records( $mainobj->count_all() ) ) {
			$outlets                = Mapbd_Pos_Warehouse::find_all_by_key_value( 'status', 'A', 'id', 'name' );
			$response_data->rowdata = $mainobj->select_all_grid_data( '', $order_by, $order, $response_data->limit, $response_data->limit_start() );
			foreach ( $response_data->rowdata as &$data ) {
				$data->purchase_date   = appsbd_get_wp_datetime_with_format( $data->purchase_date );
				$data->discount        = doubleval( $data->discount );
				$data->discount_total  = doubleval( $data->discount_total );
				$data->tax_total       = doubleval( $data->tax_total );
				$data->grand_total     = doubleval( $data->grand_total );
				$data->warehouse_title = appsbd_get_text_by_key( $data->warehouse_id, $outlets );
			}
		}
		return $response_data;
	}
	/**
	 * The purchase list is generated by appsbd
	 *
	 * @return API_Data_Response
	 */
	public function transfer_list() {
		$mainobj              = new Mapbd_Pos_Stock_Transfer();
		$response_data        = new API_Data_Response();
		$response_data->limit = $this->get_payload( 'limit', 20 );
		$response_data->page  = $this->get_payload( 'page', 1 );
		$src_props            = $this->get_payload( 'src_by', array() );
		$mainobj->set_search_by_param( $src_props );
		$order_by = 'transfer_date';
		$order    = 'DESC';
		if ( ! empty( $this->payload['sort_by'][0] ) ) {
			$order_by = $this->payload['sort_by'][0]['prop'];
			$order    = $this->payload['sort_by'][0]['ord'];
		}
		$args  = array(
			'role__not_in' => array( 'customer', 'subscriber' ),
		);
		$users = get_users( $args );
				if ( $response_data->set_total_records( $mainobj->count_all() ) ) {
			$outlets = Mapbd_Pos_Warehouse::find_all_by_key_value( 'status', 'A', 'id', 'name' );
			$mainobj->transfer_from( $this->get_outlet_id() );
			$response_data->rowdata = $mainobj->select_all_grid_data( '', $order_by, $order, $response_data->limit, $response_data->limit_start() );
			$transfer_status        = $mainobj->get_property_raw_options( 'transfer_status' );
			foreach ( $response_data->rowdata as &$data ) {
				$data = $this->get_transfer_data( $data, $users, $outlets, $transfer_status );
			}
		}
		return $response_data;
	}
	/**
	 * The purchase list is generated by appsbd
	 *
	 * @return API_Data_Response
	 */
	public function receive_list() {
		$mainobj              = new Mapbd_Pos_Stock_Transfer();
		$response_data        = new API_Data_Response();
		$response_data->limit = $this->get_payload( 'limit', 20 );
		$response_data->page  = $this->get_payload( 'page', 1 );
		$src_props            = $this->get_payload( 'src_by', array() );
		$mainobj->set_search_by_param( $src_props );
		$mainobj->transfer_to( $this->get_outlet_id() );
		
		
		$order_by = 'transfer_date';
		$order    = 'DESC';
		if ( ! empty( $this->payload['sort_by'][0] ) ) {
			$order_by = $this->payload['sort_by'][0]['prop'];
			$order    = $this->payload['sort_by'][0]['ord'];
		}
		$args  = array(
			'role__not_in' => array( 'customer', 'subscriber' ),
		);
		$users = get_users( $args );
				if ( $response_data->set_total_records( $mainobj->count_all() ) ) {
			$outlets                = Mapbd_Pos_Warehouse::find_all_by_key_value( 'status', 'A', 'id', 'name' );
			$response_data->rowdata = $mainobj->select_all_grid_data( '', $order_by, $order, $response_data->limit, $response_data->limit_start() );
			$transfer_status        = $mainobj->get_property_raw_options( 'transfer_status' );
			foreach ( $response_data->rowdata as &$data ) {
				$data = $this->get_transfer_data( $data, $users, $outlets, $transfer_status );
			}
		}
		return $response_data;
	}

	/**
	 * The get transfer data is generated by appsbd
	 *
	 * @param mixed $data Transfer Stock Data.
	 * @param mixed $users Transfer Stock User.
	 * @param mixed $outlets Transfer Stock outlets.
	 * @param mixed $transfer_status Transfer status.
	 *
	 * @return mixed
	 */
	public function get_transfer_data( &$data, $users, $outlets, $transfer_status ) {
		$data->transfer_date = appsbd_get_wp_datetime_with_format( $data->transfer_date );
		$data->receive_date  = appsbd_get_wp_datetime_with_format( $data->receive_date );
		if ( ! empty( $data->receive_by ) ) {
			$data->receive_by_name = $this->get_user_name( $data->receive_by, $users );
		}
		$data->transfer_by_name      = $this->get_user_name( $data->transfer_by, $users );
		$data->transfer_from_name    = appsbd_get_text_by_key( $data->transfer_from, $outlets );
		$data->transfer_to_name      = appsbd_get_text_by_key( $data->transfer_to, $outlets );
		$data->transfer_status_title = appsbd_get_text_by_key( $data->transfer_status, $transfer_status );
		return $data;
	}

	/**
	 * The get user name is generated by appsbd
	 *
	 * @param any $id Its id Param.
	 * @param any $users Its user Param.
	 *
	 * @return string
	 */
	public function get_user_name( $id, $users ) {
		if ( count( $users ) > 0 ) {
			foreach ( $users as $user ) {
				if ( $user->ID == $id ) {
					return ! empty( $user->first_name ) ? $user->first_name . ' ' . $user->last_name : $user->display_name;
				}
			}
		}
	}
	/**
	 * The purchase list is generated by appsbd
	 *
	 * @return API_Data_Response
	 */
	public function updated_price_list() {
		self::set_vite_pos_request();
		$page                 = $this->get_payload( 'page', 1 );
		$limit                = $this->get_payload( 'limit', 20 );
		$src_props            = $this->get_payload( 'src_by', array() );
		$sort_by_props        = $this->get_payload( 'sort_by', array() );
		$response_product     = POS_Product::get_product_from_woo_products_with_variations(
			$page,
			$limit,
			$src_props,
			$sort_by_props
		);
		$response_data        = new API_Data_Response();
		$response_data->page  = $page;
		$response_data->limit = $limit;
		if ( $response_data->set_total_records( $response_product->records ) ) {
			$response_data->rowdata = $response_product->products;
		}
		$this->response->set_response( true, '', $response_data );
		return $this->response;
	}

	/**
	 * The update wc stock is generated by appsbd
	 *
	 * @param \WC_Product $product Its Product Param.
	 * @param any         $purchase Its purchase Param.
	 * @param any         $add_to_list Its add to list Param.
	 * @param string      $outlet_id Its Outlet id Param.
	 *
	 * @return bool
	 */
	public function update_wc_stock( &$product, $purchase, $add_to_list, $outlet_id = '' ) {
		$outlet_stock = intval( $purchase->stock_quantity );
		$pre_stock    = intval( $product->get_stock_quantity() );
		$stock        = $pre_stock + $outlet_stock;

		if ( ! POS_Settings::is_stockable() || POS_Settings::is_default_stock() ) {
			if ( ! $product->get_manage_stock() ) {
				$product->set_manage_stock( true );
			}
			$product->set_stock_quantity( $stock );
			if ( $product->meta_exists( '_vt_prev_purchase_price' ) ) {
				$product->update_meta_data( '_vt_prev_purchase_price', $purchase->prev_purchase_cost );
			} else {
				$product->add_meta_data( '_vt_prev_purchase_price', $purchase->prev_purchase_cost );
			}
			$msg = POS_Settings::get_module_instance()->__( 'Stock increased' );
			Mapbd_Pos_Stock_Log::AddLog( 'I', 0, $product->get_id(), $pre_stock, $outlet_stock, $msg, $purchase->id, 'PU', 'W' );
		}

				if ( ! empty( $outlet_id ) && POS_Settings::is_stockable() && ! POS_Settings::is_default_stock() ) {
			if ( ! Mapbd_Pos_Warehouse::increase_stock_for_outlet( $product, $outlet_id, $outlet_stock, 'Product purchased', $purchase->id, 'PU' ) ) {
				$this->add_error( 'Stock update is not possible' );
				return false;
			}
		}
		if ( $product->meta_exists( '_vt_purchase_cost' ) ) {
			$product->update_meta_data( '_vt_purchase_cost', $purchase->purchase_cost );
		} else {
			$product->add_meta_data( '_vt_purchase_cost', $purchase->purchase_cost );
		}
		if ( 'Y' == $add_to_list ) {
			if ( $product->meta_exists( '_vt_purchase_price_change' ) ) {
				$product->update_meta_data( '_vt_purchase_price_change', $add_to_list );
			} else {
				$product->add_meta_data( '_vt_purchase_price_change', $add_to_list );
			}
		}
		if ( $product->save() ) {
			return true;
		} else {
			$this->add_error( 'Stock not updated' );
			return false;
		}
	}

	/**
	 * The create purchase is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function stock_transfer() {
		if ( POS_Settings::is_default_stock() ) {
			$this->response->set_response( false, 'Usages WooCommerce stock, so you can not transfer' );
			return $this->response;
		}
		if ( ! empty( $this->payload ) ) {
			$transfer_obj = new Mapbd_Pos_Stock_Transfer();
			$transfer_obj->set_from_array( $this->payload );
			$transfer_obj->transfer_by( $this->get_current_user_id() );
			$transfer_obj->transfer_status( 'P' );
			$transfer_obj->transfer_date( gmdate( 'Y-m-d H:i:s' ) );
			if ( $transfer_obj->is_valid_form( true ) ) {
				if ( ! empty( $this->payload['items'] ) ) {
					$is_item_ok     = true;
					$transfer_items = array();
					foreach ( $this->payload['items'] as $item ) {
												$t_items = new Mapbd_Pos_Stock_Transfer_Item();
						$t_items->set_from_array( $item );
						if ( ! $t_items->is_valid_form( true ) ) {
							$is_item_ok = false;
						} else {
							/**
							 * Its for checks the stock of the product for required outlet.
							 *
							 * Param $proudct_id, $quantity,$outlet_id
							 *
							 * @since 2.0
							 */
							$product = wc_get_product( $t_items->product_id );
							/**
							 * Its the filter about checking stock.
							 *
							 * @since 1.0.0
							 */
							$is_in_stock = apply_filters( 'vitepos/filter/check-stock', $product, $t_items->product_qty, $transfer_obj->transfer_from );
							if ( $is_in_stock ) {
								$transfer_items[] = $t_items;
							} else {
								$is_item_ok = false;
							}
						}
					}
					if ( $is_item_ok ) {
						$is_item_save_ok = true;
						$data            = array();
						if ( $transfer_obj->save() ) {
							foreach ( $transfer_items as $transfer_item ) {
								$transfer_item->transfer_id( $transfer_obj->id );
								if ( ! $transfer_item->save() ) {
									$is_item_save_ok = false;
								} else {
									$product = wc_get_product( $transfer_item->product_id );
									$final_id=0;
									Mapbd_Pos_Warehouse::reduce_stock_for_outlet( $product, $transfer_obj->transfer_from, $transfer_item->product_qty, 'Product transfer', $transfer_obj->id, 'TS' );
									$std = new \stdClass();
									if ( 'variation' == $product->get_type() ) {
										$std->product_id   = $product->get_parent_id();
										$std->variation_id = $product->get_id();
										$final_id          = $std->variation_id;
									} else {
										$std->product_id   = $product->get_id();
										$std->variation_id = '';
										$final_id          = $std->product_id;
									}
									/**
									 *  Its the action about woo pay order after submition.
									 *
									 * @since 1.0.0
									 */
									$std->stock = apply_filters( 'vitepos/filter/outlet-stock', 0, $final_id, $transfer_obj->transfer_from );
									$data[]     = $std;

								}
							}
						}
						if ( $is_item_save_ok ) {
							if ( ! empty( $data ) ) {
								/**
								 * Its for check is there any change before process
								 *
								 * @since 2.0
								 */
								do_action( 'vitepos/action/stock-updated', $data, $transfer_obj->transfer_from );
							}
							$this->response->set_response( true, 'Successfully transferred', $data );
							return $this->response;
						}
					} else {
						$this->add_error( 'Item is not available' );
					}
				}
				$this->add_error( 'No item found to transfer' );
			}
			$this->add_error( 'Not valid' );
		}
		$this->response->set_response( false, '' );
		return $this->response;
	}

	/**
	 * The add stock to product is generated by appsbd
	 *
	 * @param any    $product Its Product param.
	 * @param any    $outlet_id Its outlet id param.
	 * @param any    $quantity Its quantity param.
	 * @param string $msg Its mgs param.
	 *
	 * @return bool
	 */
	public function add_stock_to_product( $product, $outlet_id, $quantity, $msg = '' ) {
		return Mapbd_Pos_Warehouse::increase_stock_for_outlet( $product, $outlet_id, $quantity, $msg );
	}

	/**
	 * The remove stock from product is generated by appsbd
	 *
	 * @param any $product Its Product param.
	 * @param any $outlet_id Its outlet id param.
	 * @param any $quantity Its quantity param.
	 *
	 * @return bool
	 */
	public function remove_stock_from_product( $product, $outlet_id, $quantity ) {
		return Mapbd_Pos_Warehouse::reduce_stock_for_outlet( $product, $outlet_id, $quantity );
	}

	/**
	 * The receive stock is generated by appsbd
	 */
	public function stock_receive() {
		if ( POS_Settings::is_default_stock() ) {
			$this->response->set_response( false, 'Usages WooCommerce stock, so you can not receive' );
			return $this->response;
		}
		if ( ! empty( $this->payload['id'] ) ) {
			$id          = intval( $this->payload['id'] );
			$receive_obj = new Mapbd_Pos_Stock_Transfer();
			if ( $receive_obj->set_from_array( $this->payload, false ) ) {
				$receive_obj->transfer_status( 'R' );
				$receive_obj->receive_by( $this->get_current_user_id() );
				$receive_obj->receive_date( gmdate( 'Y-m-d H:i:s' ) );
				$propes = 'transfer_status,receive_by,receive_date,receive_note';
				if ( $receive_obj->unset_all_excepts( $propes ) ) {
					$receive_obj->set_where_update( 'id', $id );
					$transfer_items = Mapbd_Pos_Stock_Transfer_Item::find_all_by( 'transfer_id', $id );
					$is_ok_receive  = true;
					foreach ( $transfer_items as &$item ) {
						$product = wc_get_product( $item->product_id );
						if ( ! Mapbd_Pos_Warehouse::increase_stock_for_outlet( $product, $this->get_outlet_id(), intval( $item->product_qty ), 'Transfer received', $id, 'TR' ) ) {
							$is_ok_receive = false;
						}
					}
					if ( $is_ok_receive ) {
						if ( $receive_obj->update() ) {
							$trans_obj = Mapbd_Pos_Stock_Transfer::find_by( 'id', $id );
							$data      = array();
							$t_item    = new Mapbd_Pos_Stock_Transfer_Item();
							$t_item->transfer_id( $trans_obj->id );
							$items = $t_item->select_all_grid_data();
							foreach ( $items as $item ) {
								$product = wc_get_product( $item->product_id );
								$std     = new \stdClass();
								if ( 'variation' == $product->get_type() ) {
									$std->product_id   = $product->get_parent_id();
									$std->variation_id = $product->get_id();
								} else {
									$std->product_id   = $product->get_id();
									$std->variation_id = '';
								}
								/**
								 *  Its the action about woo pay order after submition.
								 *
								 * @since 1.0.0
								 */
								$std->stock = apply_filters( 'vitepos/filter/outlet-stock', 0, $std->product_id, $this->get_outlet_id() );
								$data[]     = $std;
							}
							if ( ! empty( $data ) ) {
								/**
								 * Its for check is there any change before process
								 *
								 * @since 2.0
								 */
								do_action( 'vitepos/action/stock-updated', $data, $this->get_outlet_id() );
							}
							$this->response->set_response( true, 'Stock received', $data );
							return $this->response;
						}
					}
				}
			}
			$this->response->set_response( false, 'Not received yet', null );
			return $this->response;
		}
		$this->response->set_response( false, 'Transfer id is not found for receive', null );
		return $this->response;
	}

	/**
	 * The stock accept is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function stock_accept() {
		if ( POS_Settings::is_default_stock() ) {
			$this->response->set_response( false, 'Usages WooCommerce stock, so you can not receive' );
			return $this->response;
		}
		if ( ! empty( $this->payload['id'] ) ) {
			$id          = intval( $this->payload['id'] );
			$receive_obj = new Mapbd_Pos_Stock_Transfer();
			$receive_obj->transfer_status( 'A' );
			$receive_obj->accepted_by( $this->get_current_user_id() );
			$receive_obj->accepted_date( gmdate( 'Y-m-d H:i:s' ) );
			$receive_obj->set_where_update( 'id', $id );
			$transfer_items = Mapbd_Pos_Stock_Transfer_Item::find_all_by( 'transfer_id', $id );
			$is_ok_receive  = true;
			$t_obj          = new Mapbd_Pos_Stock_Transfer();
			$t_obj->id( $this->payload['id'] );
			$t_obj->select();
			foreach ( $transfer_items as  &$item ) {
				$product = wc_get_product( $item->product_id );
				if ( ! Mapbd_Pos_Warehouse::increase_stock_for_outlet( $product, $t_obj->transfer_from, intval( $item->product_qty ), 'Transfer back', $this->payload['id'], 'TB' ) ) {
					$is_ok_receive = false;
				}
			}
			if ( $is_ok_receive ) {
				if ( $receive_obj->update() ) {
					$trans_obj = Mapbd_Pos_Stock_Transfer::find_by( 'id', $id );
					$data      = array();
					$t_item    = new Mapbd_Pos_Stock_Transfer_Item();
					$t_item->transfer_id( $trans_obj->id );
					$items = $t_item->select_all_grid_data();
					foreach ( $items as $item ) {
						$product = wc_get_product( $item->product_id );
						$std     = new \stdClass();
						if ( 'variation' == $product->get_type() ) {
							$std->product_id   = $product->get_parent_id();
							$std->variation_id = $product->get_id();
						} else {
							$std->product_id   = $product->get_id();
							$std->variation_id = '';
						}
						/**
						 *  Its the action about woo pay order after submition.
						 *
						 * @since 1.0.0
						 */
						$std->stock = apply_filters( 'vitepos/filter/outlet-stock', 0, $std->product_id, $this->get_outlet_id() );
						$data[]     = $std;
					}
					if ( ! empty( $data ) ) {
						/**
						 * Its for check is there any change before process
						 *
						 * @since 2.0
						 */
						do_action( 'vitepos/action/stock-updated', $data, $this->get_outlet_id() );
					}
					$this->response->set_response( true, 'Stock accepted', $data );
					return $this->response;
				}
			}

			$this->response->set_response( false, 'Not accepted', null );
			return $this->response;
		}
		$this->response->set_response( false, 'Transfer id is not found', null );
		return $this->response;
	}
	/**
	 * The receive stock is generated by appsbd
	 */
	public function stock_decline() {
		if ( POS_Settings::is_default_stock() ) {
			$this->response->set_response( false, 'Usages WooCommerce stock, so you can not receive' );
			return $this->response;
		}
		if ( ! empty( $this->payload['id'] ) ) {
			$id      = intval( $this->payload['id'] );
			$data    = array();
			$old_obj = new Mapbd_Pos_Stock_Transfer();
			$old_obj->id( $id );
			$old_obj->select();
			$msg         = 'Transfer declined';
			$receive_obj = new Mapbd_Pos_Stock_Transfer();
				$receive_obj->transfer_status( 'D' );
				$receive_obj->receive_by( $this->get_current_user_id() );
				$receive_obj->receive_date( gmdate( 'Y-m-d H:i:s' ) );
			if ( $old_obj->transfer_to == $this->get_outlet_id() && empty( $this->payload['receive_note'] ) ) {
				$trans_obj = Mapbd_Pos_Stock_Transfer::find_by( 'id', $id );
				$data      = $this->get_transfer_details( $trans_obj );
				$this->response->set_response( false, 'Decline note is required', $data );
				return $this->response;
			}
				$is_ok_receive = true;
			if ( $old_obj->transfer_by == $this->get_current_user_id() && $old_obj->transfer_from == $this->get_outlet_id() && 'P' == $old_obj->transfer_status ) {
				$receive_obj->transfer_status( 'C' );
				$transfer_items = Mapbd_Pos_Stock_Transfer_Item::find_all_by( 'transfer_id', $id );
				$t_obj          = new Mapbd_Pos_Stock_Transfer();
				$t_obj->id( $this->payload['id'] );
				$t_obj->select();
				foreach ( $transfer_items as  &$item ) {
					$product = wc_get_product( $item->product_id );
					if ( ! Mapbd_Pos_Warehouse::increase_stock_for_outlet( $product, $t_obj->transfer_from, intval( $item->product_qty ), 'Transfer cancelled', $this->payload['id'], 'TC' ) ) {
						$is_ok_receive = false;
					}
					if ( $is_ok_receive ) {
							$std = new \stdClass();
						if ( 'variation' == $product->get_type() ) {
							$std->product_id   = $product->get_parent_id();
							$std->variation_id = $product->get_id();
						} else {
							$std->product_id   = $product->get_id();
							$std->variation_id = '';
						}
							/**
							 *  Its the action about woo pay order after submition.
							 *
							 * @since 1.0.0
							 */
							$std->stock = apply_filters( 'vitepos/filter/outlet-stock', 0, $std->product_id, $this->get_outlet_id() );
							$data[]     = $std;
					}
				}
				$msg = 'Transfer cancelled';
			}
			if ( ! $is_ok_receive ) {
				$this->response->set_response( false, 'Cancel is not possible', null );
				return $this->response;
			}
				$receive_obj->receive_note( $this->payload['receive_note'] );
				$receive_obj->set_where_update( 'id', $id );
			if ( $receive_obj->update() ) {
				if ( ! empty( $data ) ) {
					/**
					 * Its for check is there any change before process
					 *
					 * @since 2.0
					 */
					do_action( 'vitepos/action/stock-updated', $data, $this->get_outlet_id() );
				}
				$this->response->set_response( true, $msg, $data );

				return $this->response;
			}
			$this->response->set_response( false, 'Declined is not possible', null );
			return $this->response;
		}
		$this->response->set_response( false, 'Decline id is not valid', null );
		return $this->response;
	}
	/**
	 * The create purchase is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function create_purchase() {

		if ( empty( $this->payload['id'] ) ) {
			$purchase_obj = new Mapbd_pos_purchase();
			$purchase_obj->set_from_array( $this->payload );
			$purchase_obj->payment_status( 'P' );
			$purchase_obj->status( 'A' );
			$purchase_obj->purchase_date = gmdate( 'Y-m-d H:i:s' );
			$purchase_obj->added_by( $this->get_current_user_id() );
			if ( $purchase_obj->is_valid_form( true ) ) {
				if ( ! empty( $this->payload['purchase_items'] ) ) {
					$is_item_ok     = true;
					$purchase_items = array();
					foreach ( $this->payload['purchase_items'] as $purchase_item ) {
						$add_to_list = $purchase_item['add_to_list'];
						$p_items     = new Mapbd_Pos_Purchase_Item();
						$p_items->set_from_array( $purchase_item );
						if ( ! $p_items->is_valid_form( true ) ) {
							$is_item_ok = false;
						} else {
							$purchase_items[] = $p_items;
						}
					}
					$data = array();
					if ( $is_item_ok ) {
						$is_item_save_ok = true;
						if ( $purchase_obj->save() ) {
							foreach ( $purchase_items as $purchase_item_obj ) {
								$purchase_item_obj->purchase_id( $purchase_obj->id );
								if ( $purchase_item_obj->Save() ) {
									$product = wc_get_product( $purchase_item_obj->product_id );
									if ( ! $this->update_wc_stock( $product, $purchase_item_obj, $add_to_list, $purchase_obj->warehouse_id ) ) {
										$this->response->set_response( false, 'Purchase is not possible on this outlet' );
										return $this->response;
									}
									$final_id = 0;
									$std      = new \stdClass();
									if ( 'variation' == $product->get_type() ) {
										$std->product_id   = $product->get_parent_id();
										$std->variation_id = $product->get_id();
										$final_id          = $std->variation_id;
									} else {
										$std->product_id   = $product->get_id();
										$std->variation_id = '';
										$final_id          = $std->product_id;
									}
									if ( POS_Settings::is_default_stock() ) {
										$std->stock = $product->get_stock_quantity();
									} else {
										/**
										 *  Its the action about woo pay order after submition.
										 *
										 * @since 1.0.0
										 */
										$std->stock = apply_filters( 'vitepos/filter/outlet-stock', 0, $final_id, $purchase_obj->warehouse_id );
									}
									$data[] = $std;
								} else {
									$is_item_save_ok = false;
								}
							}

							if ( $is_item_save_ok ) {
								if ( ! empty( $data ) ) {
									/**
									 * Its for check is there any change before process
									 *
									 * @since 2.0
									 */
									do_action( 'vitepos/action/stock-updated', $data, POS_Settings::is_default_stock() ? 0 : $purchase_obj->warehouse_id );
								}
								$this->response->set_response( true, 'Successfully purchased', $data );
								return $this->response;
							}
						}
					}
				} else {
					$this->add_error( 'Purchased failed' );
				}
			}
		}
		$this->response->set_response( false, 'From is not valid' );
		return $this->response;
	}

	/**
	 * The purchase details is generated by appsbd
	 *
	 * @param any $data Its string.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function purchase_details( $data ) {
		if ( ! empty( $data['id'] ) ) {
			$id           = intval( $data['id'] );
			$purchase_obj = new Mapbd_pos_purchase();
			$purchase_obj->id( $id );
			if ( ! POS_Settings::is_admin_user() && ! current_user_can( 'can-see-any-outlet-purchases' ) ) {
				$outlets = get_user_meta( $this->get_current_user_id(), 'outlet_id', true );
				if ( is_array( $outlets ) ) {
					$outlet_in = "'" . implode( "','", $outlets ) . "'";
					$purchase_obj->warehouse_id( "IN ($outlet_in)", true );
				} else {
					$this->add_error( "You don't have permission to view details of this outlet" );
					$this->set_response( false );
					return $this->response->get_response();
				}
			}
			if ( $purchase_obj->Select() ) {
				$purchase_obj->warehouse_title = POS_Settings::get_module_instance()->__( 'Unknown' );
				$outletobj                     = Mapbd_Pos_Warehouse::find_by( 'id', $purchase_obj->warehouse_id );
				if ( ! empty( $outletobj ) ) {
					$purchase_obj->warehouse_title = $outletobj->name;
				}
				$user                   = get_user_by( 'ID', $purchase_obj->added_by );
				$purchase_obj->added_by = $user->first_name ? $user->first_name . ' ' . $user->last_name : $user->user_nicename;
				$p_item                 = new Mapbd_Pos_Purchase_Item();
				$p_item->purchase_id( $purchase_obj->id );
				$purchase_obj->purchase_items = $p_item->select_all_grid_data();
				foreach ( $purchase_obj->purchase_items as $item ) {
					$product = wc_get_product( $item->product_id );
					if ( $product ) {
						$item->in_stock = $product->get_stock_quantity();
					} else {
						$item->in_stock = 0;
					}
				}
				$this->set_response( true, 'Data found', $purchase_obj );
				return $this->response->get_response();
			}
		}
		$this->set_response( false, 'data not found or invalid param' );
		return $this->response->get_response();
	}

	/**
	 * The get transfer details is generated by appsbd
	 *
	 * @param any $transfer_obj Its transfer obj param.
	 *
	 * @return mixed
	 */
	public function get_transfer_details( $transfer_obj ) {
		$outlets         = Mapbd_Pos_Warehouse::find_all_by_key_value( 'status', 'A', 'id', 'name' );
		$users           = get_users();
		$transfer_status = $transfer_obj->get_property_raw_options( 'transfer_status' );

		$data        = $this->get_transfer_data( $transfer_obj, $users, $outlets, $transfer_status );
		$data->items = array();
		$t_item      = new Mapbd_Pos_Stock_Transfer_Item();
		$t_item->transfer_id( $transfer_obj->id );
		$data->items = $t_item->select_all_grid_data();
		foreach ( $data->items as $item ) {
			$product = wc_get_product( $item->product_id );
			if ( $product ) {
				$item->product_name = $product->get_name();
				/**
				 *  Its the action about woo pay order after submition.
				 *
				 * @since 1.0.0
				 */
				$item->current_stock = apply_filters( 'vitepos/filter/outlet-stock', 0, $product, $this->get_outlet_id() );
			} else {
				$item->product_name = 'No name found';
			}
		}
		return $data;
	}
	/**
	 * The purchase details is generated by appsbd
	 *
	 * @param any $data Its string.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function transfer_details( $data ) {
		if ( ! empty( $data['id'] ) ) {
			$id           = intval( $data['id'] );
			$transfer_obj = new Mapbd_Pos_Stock_Transfer();
			$transfer_obj->id( $id );
			if ( ! POS_Settings::is_admin_user() && ! current_user_can( 'can-see-any-outlet-purchases' ) ) {
				$outlets = get_user_meta( $this->get_current_user_id(), 'outlet_id', true );
				if ( is_array( $outlets ) ) {
					$outlet_in = "'" . implode( "','", $outlets ) . "'";
					$transfer_obj->transfer_to( "IN ($outlet_in)", true );
				} else {
					$this->add_error( "You don't have permission to view details of this outlet" );
					$this->set_response( false );
					return $this->response->get_response();
				}
			}
			if ( $transfer_obj->Select() ) {
				$data = $this->get_transfer_details( $transfer_obj );
				$this->set_response( true, 'Data found', $data );
				return $this->response->get_response();
			}
		}
		$this->set_response( false, 'data not found or invalid param' );
		return $this->response->get_response();
	}
	/**
	 * The purchase details is generated by appsbd
	 *
	 * @param any $data Its string.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function stock_logs( $data ) {
		if ( ! empty( $data['id'] ) ) {
			$product_id  = intval( $data['id'] );
			$product_obj = wc_get_product( $product_id );
			$log_obj     = new Mapbd_Pos_Stock_Log();
			$log_obj->product_id( $product_id );
			if ( POS_Settings::is_default_stock() ) {
				$log_obj->stock_type( 'W' );
			} else {
				$log_obj->stock_type( 'O' );
				$log_obj->outlet_id( $this->get_outlet_id() );
			}
			$product               = new \stdClass();
			$product->product_info = POS_Product::get_product_variation_data( $product_obj );
			$product->logs         = $log_obj->select_all( '', 'id', 'asc' );
			foreach ( $product->logs as &$log ) {
				$log->entry_date = appsbd_get_wp_date_with_format( $log->entry_date );
			}
			$this->set_response( true, 'Data found', $product );
			return $this->response->get_response();
		}
		$this->set_response( false, 'data not found or invalid param' );
		return $this->response->get_response();
	}

	/**
	 * The purchase details is generated by appsbd
	 *
	 * @param any $data Its string.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function updated_product_details( $data ) {
		if ( ! empty( $data['id'] ) ) {
			$id      = intval( $data['id'] );
			$product = wc_get_product( $id );
			if ( $product->meta_exists( '_vt_purchase_price_change' ) && $product->get_meta( '_vt_purchase_price_change', true ) == 'Y' ) {
				$data                     = new \stdClass();
				$data->id                 = $product->get_id();
				$data->name               = $product->get_name();
				$data->parent_id          = $product->get_parent_id();
				$data->prev_purchase_cost = (float) $product->meta_exists( '_vt_prev_purchase_price' ) ? $product->get_meta( '_vt_prev_purchase_price', true ) : '0.00';
				$data->purchase_cost      = (float) $product->meta_exists( '_vt_purchase_cost' ) ? $product->get_meta( '_vt_purchase_cost', true ) : '0.00';
				$data->regular_price      = (float) $product->get_regular_price();
				$data->sale_price         = (float) $product->get_sale_price();
				$data->history            = Mapbd_Pos_Purchase_Item::get_purchase_history( $data->id, '', 5 );
				$this->set_response( true, 'data found', $data );
			}
			return $this->response;
		}
		$this->set_response( false, 'Data not found or invalid param' );

		return $this->response;
	}

	/**
	 * The update product is generated by appsbd.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 * @throws \WC_Data_Exception Throws error.
	 */
	public function update_product_price() {
		$new_product = wc_get_product( $this->payload['id'] );
		$new_product->set_regular_price( $this->payload['regular_price'] );
		if ( $this->payload['sale_price'] > 0 ) {
			$new_product->set_sale_price( $this->payload['sale_price'] );
		}
		$new_product->update_meta_data( '_vt_purchase_price_change', 'N' );
		$product_id = $new_product->save();
		if ( $product_id ) {
			$product = wc_get_product( $product_id );
			if ( $product->get_parent_id() ) {
				$product = wc_get_product( $product->get_parent_id() );
			}
			$data = POS_Product::get_product_data( $product );
			$this->add_info( 'Prices updated ' );
			$this->response->set_response( true, '', $data );
			return $this->response;
		} else {
			$this->add_error( 'Prices not updated ' );
			$this->response->set_response( false );
			return $this->response;
		}
	}

	/**
	 * The ignore update price is generated by appsbd.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 * @throws \WC_Data_Exception Throws error.
	 */
	public function ignore_update_price() {
		$new_product = wc_get_product( $this->payload['id'] );
		$new_product->update_meta_data( '_vt_purchase_price_change', 'N' );
		$product_id = $new_product->save();
		if ( $product_id ) {
			$product = wc_get_product( $product_id );
			if ( $product->get_parent_id() ) {
				$product = wc_get_product( $product->get_parent_id() );
			}
			$data = POS_Product::get_product_data( $product );
			$this->add_info( 'Ignore successful' );
			$this->response->set_response( true, '', $data );
			return $this->response;
		} else {
			$this->add_error( 'Failed to ignore' );
			$this->response->set_response( false );
			return $this->response;
		}
	}

}
