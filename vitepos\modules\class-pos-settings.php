<?php
/**
 * Its for EPOS settings module
 *
 * @package VitePos\Modules
 */

namespace VitePos\Modules;

use Appsbd\V1\Core\BaseModel;
use Appsbd\V1\Core\BaseModule;
use Appsbd\V1\libs\Ajax_Confirm_Response;
use Appsbd\V1\libs\AppInput;
use Cassandra\Map;
use VitePos\Libs\Client_Language;
use VitePos\Libs\Invoice_Settings;
use VitePos\Libs\Manifest;
use VitePos\Libs\POS_Customer;
use VitePos\Libs\POS_Order;
use VitePos\Libs\Recaptcha;
use VitePos\Libs\Vitepos_Stock_Manage;
use VitePos\Libs\WC_Stock_Manage;
use Vitepos\Models\Database\Mapbd_Pos_Addon;
use Vitepos\Models\Database\Mapbd_Pos_Addon_Field;
use Vitepos\Models\Database\Mapbd_Pos_Addon_Field_Option;
use Vitepos\Models\Database\Mapbd_Pos_Addon_Rule;
use Vitepos\Models\Database\Mapbd_Pos_Addon_Rule_Group;
use Vitepos\Models\Database\Mapbd_Pos_Cash_Drawer;
use Vitepos\Models\Database\Mapbd_Pos_Cash_Drawer_Log;
use Vitepos\Models\Database\Mapbd_Pos_Cash_Drawer_Types;
use Vitepos\Models\Database\Mapbd_pos_counter;
use Vitepos\Models\Database\Mapbd_Pos_Custom_Field;
use Vitepos\Models\Database\Mapbd_Pos_Message;
use Vitepos\Models\Database\Mapbd_pos_purchase;
use Vitepos\Models\Database\Mapbd_pos_purchase_item;
use Vitepos\Models\Database\Mapbd_Pos_Role;
use Vitepos\Models\Database\Mapbd_Pos_Stock_Log;
use Vitepos\Models\Database\Mapbd_Pos_Stock_Transfer;
use Vitepos\Models\Database\Mapbd_Pos_Stock_Transfer_Item;
use Vitepos\Models\Database\Mapbd_Pos_Table;
use Vitepos\Models\Database\Mapbd_pos_vendor;
use Vitepos\Models\Database\Mapbd_pos_warehouse;

/**
 * Class APBD_EPOS_Settings
 */
class POS_Settings extends BaseModule {
	/**
	 * Its property _synced_product_ids
	 *
	 * @var array
	 */
	private static $_synced_product_ids = array();

	/**
	 * The initialize is generated by appsbd
	 */
	public function initialize() {
		add_filter(
			'appsbd/input/html/fields',
			function ( $html_fields ) {
				$html_fields[] = 'header';
				$html_fields[] = 'footer';

				return $html_fields;
			}
		);
		add_filter(
			'woocommerce_order_data_store_cpt_get_orders_query',
			array( $this, 'vt_meta_query_filter' ),
			10,
			2
		);
		add_filter(
			'woocommerce_product_data_store_cpt_get_products_query',
			array( $this, 'vt_meta_query_filter' ),
			10,
			2
		);
		add_action( 'apbd-vtpos/action/save-product-feature-image', array( $this, 'save_update_product_feature_img' ) );
		add_action(
			'apbd-vtpos/action/save-product-gallery-image',
			array( $this, 'save_update_product_gallery_img' ),
			10,
			2
		);
		add_action(
			'apbd-vtpos/action/save-product-variation-image',
			array( $this, 'save_update_product_variation_img' ),
			10,
			3
		);
		add_filter( 'display_post_states', array( $this, 'post_states' ), 10, 2 );
		add_action( 'apbd-vtpos/action/before-product-list', array( $this, 'before_product_load' ) );
		add_filter( 'vitepos/filter/billing-address', array( $this, 'set_customer_billing_address' ), 10, 3 );
		add_filter( 'vitepos/filter/push-settings', array( $this, 'check_push_settings' ), 5 );
		add_filter( 'vitepos/filter/pay-first-kitchen-status', array( $this, 'pay_first_kitchen_status' ) );
		add_filter( 'vitepos/filter/header-links', array( $this, 'client_header_assets' ) );
		add_filter( 'vitepos/filter/footer-scripts', array( $this, 'client_footer_scripts' ) );
		add_action( 'vitepos/action/send-push', array( $this, 'send_push_message' ), 5, 3 );
		add_action( 'vitepos/action/stock-updated', array( $this, 'stock_updated' ), 5, 2 );
		add_action( 'vitepos/action/send-order-push', array( $this, 'send_order_push' ), 5, 3 );
		if ( self::is_default_stock() ) {
			add_action( 'vitepos/action/wc-default-stock-log-add', array( $this, 'wc_default_stock_log_add' ), 999, 2 );
		}

		add_action( 'vitepos/action/set-item-wise-fee-dis', array( $this, 'set_item_wise_fee_discount' ) );
	}



	/**
	 * The is restaurant mode is generated by appsbd
	 *
	 * @return bool
	 */
	public static function is_restaurant_mode() {
		$mode = self::get_module_option( 'pos_mode', 'G' );
		return 'R' == $mode || 'P' == $mode;
	}
	/**
	 * The is restaurant mode is generated by appsbd
	 *
	 * @return bool
	 */
	public static function get_pos_mode() {
		return self::get_module_option( 'pos_mode', 'G' );
	}
	/**
	 * The is restaurant mode is generated by appsbd
	 *
	 * @return bool
	 */
	public static function tax_method() {
		return self::get_module_option( 'tax_method', 'B' );
	}

	/**
	 * The do version update is generated by appsbd
	 *
	 * @param mixed $current_version Its current_version param.
	 * @param mixed $previous_version Its previous_version param.
	 * @param false $is_force Its isForce param.
	 */
	public static function do_version_update( $current_version, $previous_version, $is_force = false ) {
		if ( $is_force || version_compare( $current_version, $previous_version, '>' ) ) {
			self::process_on_update();
		}
		if ( $is_force || version_compare( $previous_version, '1.3', '<' ) ) {
			Mapbd_Pos_Role::db_column_add_or_modify( 'max_discount', 'decimal', '6,2', '20.00', 'NOT NULL', 'slug' );
			Mapbd_Pos_Role::db_column_add_or_modify( 'discount_type', 'char', '1', "'P'", 'NOT NULL', 'max_discount', 'radio(P=Percentage,A=Amount)' );
			Mapbd_Pos_Role::update_max_discount_non_editable( 100 );
		}
		if ( $is_force || version_compare( $previous_version, '2.0', '<' ) ) {
						Mapbd_pos_purchase::db_column_add_or_modify( 'tax_total', 'decimal', '6,2', '0.0', 'unsigned NOT NULL', 'tax_type' );
			Mapbd_pos_purchase::db_column_add_or_modify( 'discount_total', 'decimal', '6,2', '0.0', 'unsigned NOT NULL', 'discount_type' );
			Mapbd_pos_purchase::db_column_add_or_modify( 'total_item', 'int', '10', '0', 'unsigned NOT NULL', 'added_by' );
			Mapbd_pos_purchase::db_column_add_or_modify( 'total_quantity', 'int', '10', '0', 'unsigned NOT NULL', '' );

			Mapbd_pos_purchase_item::db_column_add_or_modify( 'prev_purchase_cost', 'decimal', '10,2', '0.0', 'unsigned NOT NULL', 'purchase_cost' );

		}
		if ( $is_force || version_compare( $previous_version, '2.0.2', '<' ) ) {
						Mapbd_Pos_Stock_Log::db_column_add_or_modify( 'stock_type', 'char', '1', "'O'", 'NOT NULL', 'outlet_id', 'radio(O=Outlet Wise,W=WooCommerce Stock)' );
			Mapbd_Pos_Stock_Log::db_column_add_or_modify( 'ex1_param', 'char', '64', "''", 'NOT NULL', 'stock_val', 'extra fields' );
			Mapbd_Pos_Stock_Log::db_column_add_or_modify( 'ex2_param', 'char', '64', "''", 'NOT NULL', 'ex1_param', 'extra fields' );
			Mapbd_pos_cash_drawer::db_column_add_or_modify( 'closing_balance', 'decimal', '11,2', '0', 'NOT NULL', 'opening_balance', '' );
		}

	}

	/**
	 * The on plugin version updated is generated by appsbd
	 *
	 * @param \Appsbd\V1\Core\any $current_version Its current_version param.
	 * @param \Appsbd\V1\Core\any $previous_version Its previous_version param.
	 */
	public function on_plugin_version_updated( $current_version, $previous_version ) {
		self::do_version_update( $current_version, $previous_version, false );
	}
	/**
	 * The getFavicon is generated by appsbd
	 *
	 * @return array|mixed|string
	 */
	public function get_favicon() {
		return $this->get_option( 'pos_fav_icon', $this->get_plugin_url( 'templates/pos-assets/favicon.png' ) );
	}
	/**
	 * The vt item meta filter is generated by appsbd
	 *
	 * @param mixed $formatted_meta Its formatted meta data.
	 *
	 * @return array
	 */
	public function vt_item_meta_filter( $formatted_meta ) {
		$temp_metas = array();
		foreach ( $formatted_meta as $key => $meta ) {
			if ( isset( $meta->key ) && ! in_array( $meta->key, array( '_vtp_regular_price' ) ) ) {
				$temp_metas[ $key ] = $meta;
			}
		}
		return $temp_metas;
	}
	/**
	 * The set option is generated by appsbd.
	 */
	public function set_option() {
		parent::set_option();
		if ( ! empty( $this->options['is_rc_v3'] ) ) {
			$this->options['is_rc_v3'] = true;
		} else {
			$this->options['is_rc_v3'] = false;
		}
	}

	/**
	 * The post states is generated by appsbd
	 *
	 * @param mixed $post_states Its post_states param.
	 * @param mixed $post Its post param.
	 *
	 * @return mixed
	 */
	public function post_states( $post_states, $post ) {
		$post_link_page = $this->get_option( 'POS_link', '' );
		if ( 'page' == $post_link_page ) {
			$pos_page = $this->get_option( 'pos_page', '' );
			if ( $pos_page == $post->ID ) {
				$post_states['vitepos'] = '<i class="vt-pg-icon vps vps-vt-pos"></i>';
			}
		}
		return $post_states;

	}

	/**
	 * The increase sync value is generated by appsbd
	 */
	public static function increase_sync_value() {
		$last_sync_id = (int) get_option( '_vtp_ps_id', 0 );
		$last_sync_id++;
		update_option( '_vtp_ps_id', $last_sync_id ) || add_option( '_vtp_ps_id', $last_sync_id );
	}
	/**
	 * The wc force product sync is generated by appsbd
	 *
	 * @param int $post_id Its post id.
	 */
	public function wc_force_product_sync_id( $post_id ) {
		if ( in_array( $post_id, self::$_synced_product_ids ) ) {
			return;
		}
		self::$_synced_product_ids[] = $post_id;
		$wc_product                  = wc_get_product( $post_id );
		if ( ! empty( $wc_product ) ) {
			self::increase_sync_value();
		}
	}
	/**
	 * The wc force product sync is generated by appsbd
	 *
	 * @param int      $post_id Its post id.
	 * @param \WP_Post $post Its post object.
	 */
	public function wc_force_product_sync( $post_id, $post ) {
		$this->wc_force_product_sync_id( $post_id );
	}
	/**
	 * The wc force product sync is generated by appsbd
	 *
	 * @param int $post_id Its post id.
	 */
	public function wc_force_product_sync_meta( int $post_id ) {
		$this->wc_force_product_sync_id( $post_id );
	}

	/**
	 * The wc force product sync is generated by appsbd
	 */
	public function wc_option_update() {
		self::increase_sync_value();
	}
	/**
	 * The get current sync id is generated by appsbd
	 *
	 * @return false|mixed
	 */
	public static function get_current_sync_id() {
		return (int) get_option( '_vtp_ps_id', 0 );
	}
	/**
	 Handle a custom 'customvar' query var to get orders with the 'customvar' meta.
	 *
	 * @param array $query - Args for WP_Query.
	 * @param array $query_vars - Query vars from WC_Order_Query.
	 * @return array modified $query
	 */
	public function vt_meta_query_filter( $query, $query_vars ) {
		if ( ! empty( $query_vars['vt_meta_query'] ) ) {
			$query['meta_query'] = $query_vars['vt_meta_query'];
		}
		return $query;
	}

	/**
	 * The insert media attachment is generated by appsbd
	 *
	 * @param mixed $temp_file Its temp file path.
	 * @param mixed $filename Its filename.
	 * @param mixed $type Its file mine type.
	 * @param int   $parent_post_id Its parent post id.
	 *
	 * @return int|\WP_Error|null
	 */
	public function insert_media_attachment( $temp_file, $filename, $type, $parent_post_id = 0 ) {
		$upload_dir       = wp_upload_dir();
		$unique_file_name = wp_unique_filename( $upload_dir['path'], $filename );
		$filename         = basename( $unique_file_name );
		if ( wp_mkdir_p( $upload_dir['path'] ) ) {
			$file = $upload_dir['path'] . '/' . $filename;
		} else {
			$file = $upload_dir['basedir'] . '/' . $filename;
		}

		if ( move_uploaded_file( $temp_file, $file ) ) {
			$attachment = array(
				'post_mime_type' => $type,
				'post_title'     => sanitize_file_name( $filename ),
				'post_content'   => '',
				'post_status'    => 'inherit',
			);
			$attach_id  = wp_insert_attachment( $attachment, $file, $parent_post_id );
			if ( is_wp_error( $attach_id ) ) {
				return null;
			}
			require_once ABSPATH . 'wp-admin/includes/image.php';
			$attach_data = wp_generate_attachment_metadata( $attach_id, $file );
			wp_update_attachment_metadata( $attach_id, $attach_data );
			return $attach_id;
		}
		return null;
	}

	/**
	 * The save update product variation img is generated by appsbd
	 *
	 * @param mixed $variation_id Its variation_id param.
	 * @param mixed $v_index Its v_index param.
	 * @param mixed $product_id Its product_id param.
	 */
	public function save_update_product_variation_img( $variation_id, $v_index, $product_id ) {
		$files = AppInput::get_uploaded_files();
		if ( ! empty( $files['variations']['name'][ $v_index ]['image'] ) ) {
			if ( empty( $files['variations']['error'][ $v_index ]['image'] ) ) {
				$attach_id = $this->insert_media_attachment( $files['variations']['tmp_name'][ $v_index ]['image'], $files['variations']['name'][ $v_index ]['image'], $files['variations']['type'][ $v_index ]['image'], $variation_id );
				if ( ! empty( $attach_id ) ) {
					set_post_thumbnail( $variation_id, $attach_id );
									}
			}
		}
	}

	/**
	 * The save update product feature img is generated by appsbd
	 *
	 * @param int   $product_id Its product id.
	 * @param array $rm_gallery Its the attachment id array which has been removed.
	 */
	public function save_update_product_gallery_img( $product_id, $rm_gallery ) {
		$attachments = array();
		$files       = AppInput::get_uploaded_files();
		if ( ! empty( $files['images']['name'] ) ) {
			foreach ( $files['images']['name'] as $index => $filename ) {
				if ( empty( $files['images']['error'][ $index ] ) ) {
					$attach_id = $this->insert_media_attachment( $files['images']['tmp_name'][ $index ], $files['images']['name'][ $index ], $files['images']['type'][ $index ], $product_id );
					if ( ! empty( $attach_id ) ) {
						$attachments[] = $attach_id;
					}
				}
			}
		}
		$image_galleries = get_post_meta( $product_id, '_product_image_gallery', true );
		$image_array     = explode( ',', $image_galleries );
		$image_array     = array_filter(
			$image_array,
			function ( $item ) use ( $rm_gallery ) {
				if ( in_array( $item, $rm_gallery ) ) {
					return false;
				}
				return true;
			}
		);
		if ( ! empty( $attachments ) && count( $attachments ) > 0 ) {
			$image_array = array_merge( $image_array, $attachments );
		}
		$update_galleries = implode( ',', $image_array );
		if ( $update_galleries != $image_galleries ) {
			update_post_meta( $product_id, '_product_image_gallery', $update_galleries ) || add_post_meta( $product_id, '_product_image_gallery', $update_galleries );
		}
	}

	/**
	 * The save update product feature img is generated by appsbd
	 *
	 * @param int $product_id Its product id.
	 */
	public function save_update_product_feature_img( $product_id ) {
		$files = AppInput::get_uploaded_files();
		if ( ! empty( $files['feature_image']['name'] ) && empty( $files['feature_image']['error'] ) ) {
			$attach_id = $this->insert_media_attachment( $files['feature_image']['tmp_name'], $files['feature_image']['name'], $files['feature_image']['type'], $product_id );
			if ( ! empty( $attach_id ) ) {
				set_post_thumbnail( $product_id, $attach_id );
			}
		}

	}
	/**
	 * The on init is generated by appsbd
	 */
	public function on_init() {
		parent::on_init();
		if ( current_user_can( 'activate_plugins' ) ) {
			$this->add_ajax_action( 'invoice-settings', array( $this, 'update_invoice_settings' ) );
			$this->add_ajax_action( 'push-settings', array( $this, 'push_invoice_settings' ) );
			$this->add_ajax_action( 'stock-settings', array( $this, 'stock_settings' ) );
			$this->add_ajax_action( 'stock-transfer', array( $this, 'stock_transfer' ) );
			$this->add_ajax_action( 'customers', array( $this, 'get_customers' ) );
			$this->add_ajax_action( 'refresh-app', array( $this, 'refresh_app' ) );
			$this->add_ajax_action( 'default-roles', array( $this, 'create_resto_default_role' ) );
		}

		add_filter( 'elite-pos/email-body-content', array( $this, 'email_body_content' ), 1 );
		add_action( 'template_redirect', array( $this, 'rewrite_templates' ), 1 );
		$this->add_pos_rewrite();
		add_filter( 'query_vars', array( $this, 'register_query_var' ) );
		add_action( 'woocommerce_admin_order_totals_after_total', array( $this, 'show_order_meta' ) );
		add_action(
			'woocommerce_admin_order_totals_after_discount',
			array( $this, 'woocommerce_admin_order_totals_after_discount' )
		);
		add_action(
			'woocommerce_admin_order_totals_after_tax',
			array( $this, 'woocommerce_admin_order_totals_after_tax' )
		);

		add_action( 'vitepos-client-header', array( $this, 'client_header' ) );
		add_action( 'vitepos-client-footer', array( $this, 'client_footer' ) );
		if ( $this->get_option( 'barcode_field', '' ) == 'CUS' ) {
			add_action( 'woocommerce_product_options_sku', array( $this, 'product_barcode_custom_field' ) );
			add_action( 'woocommerce_variation_options', array( $this, 'variation_barcode_custom_field' ), 10, 3 );

			add_action( 'woocommerce_process_product_meta', array( $this, 'save_product_barcode' ) );
			add_action( 'woocommerce_save_product_variation', array( $this, 'save_variation_barcode' ), 10, 2 );
		}
		add_filter( 'manage_edit-shop_order_columns', array( $this, 'vitepos_column_in_order_list' ), 20 );
		add_action(
			'manage_shop_order_posts_custom_column',
			array( $this, 'vitepos_column_value_in_order_list' ),
			20,
			2
		);

		add_action( 'save_post_product', array( $this, 'wc_force_product_sync' ), 10, 2 );
		add_action( 'after_delete_post', array( $this, 'wc_force_product_sync' ), 10, 2 );
		add_action( 'woocommerce_process_product_meta', array( $this, 'wc_force_product_sync_meta' ), 11 );
		add_action( 'woocommerce_save_product_variation', array( $this, 'wc_force_product_sync_meta' ), 11 );
		add_action( 'woocommerce_settings_saved', array( $this, 'wc_option_update' ), 11 );
				if ( is_admin() ) {
			add_action( 'show_user_profile', array( $this, 'add_user_fields' ) );
			add_action( 'user_new_form', array( $this, 'add_user_fields' ) );
			add_action( 'edit_user_profile', array( $this, 'add_user_fields' ) );

			add_action( 'personal_options_update', array( $this, 'save_user_fields' ) );
			add_action( 'user_register', array( $this, 'save_user_fields' ) );
			add_action( 'edit_user_profile_update', array( $this, 'save_user_fields' ) );
		}
		add_filter( 'woocommerce_order_item_get_formatted_meta_data', array( $this, 'vt_item_meta_filter' ), 10, 1 );
		add_action( 'apbd-vtpos/action/send-temp-password-email', array( $this, 'send_temp_password_email' ), 10, 2 );

		if ( self::is_stockable() ) {
			if ( ! self::is_default_stock() ) {
				if ( self::is_stock_linked_with() ) { 					add_filter( 'woocommerce_payment_complete_reduce_order_stock', '__return_false' );
					add_action( 'woocommerce_payment_complete', array( $this, 'online_order_stock_update' ) );
					add_action( 'woocommerce_order_status_completed', array( $this, 'online_order_stock_update' ) );
					add_action( 'woocommerce_order_status_processing', array( $this, 'online_order_stock_update' ) );
					add_action( 'woocommerce_order_status_on-hold', array( $this, 'online_order_stock_update' ) );
					add_filter(
						'woocommerce_product_backorders_allowed',
						array( $this, 'backorders_allowed' ),
						999,
						3
					);
				}
				add_filter(
					'woocommerce_product_get_stock_quantity',
					array( $this, 'online_product_stock_quantity' ),
					10,
					2
				);
				add_filter(
					'woocommerce_product_variation_get_stock_quantity',
					array( $this, 'online_product_stock_quantity' ),
					10,
					2
				);
				add_filter( 'woocommerce_product_is_in_stock', array( $this, 'online_product_in_stock' ), 10, 2 );
			} else {
				new WC_Stock_Manage();

			}
						new Vitepos_Stock_Manage();
		}
		if ( self::is_enable_customer_email() ) {
			add_action( 'vitepos/action/send-customer-email', array( $this, 'send_customer_email' ), 10, 2 );
		}

		$filter_prefix = 'vitepos/filter/';
		add_filter(
			'woocommerce_order_get_total_discount',
			function( $total, $order ) {

			},
			99,
			2
		);

		$this->custom_product_status();
	}

	/**
	 * The manage coupon discount is generated by appsbd
	 *
	 * @param mixed     $total Its total discount.
	 * @param \WC_Order $order its order object.
	 *
	 * @return int
	 */
	public function manage_coupon_discount( $total, $order ) {
		$is_vitepos = $order->get_meta( '_is_vitepos' );
		if ( 'Y' == $is_vitepos ) {
			$coupons = $order->get_items( 'coupon' );
			if ( empty( $coupons ) ) {
				return 0;
			}
		}
		return $total;
	}
	/**
	 * The custom product status is generated by appsbd
	 */
	public function custom_product_status() {
		register_post_status(
			'vt-pos-only',
			array(
				'label'                     => _x( 'Vitepos Only', 'Product status', 'woocommerce' ),
				'public'                    => true,
				'exclude_from_search'       => false,
				'show_in_admin_all_list'    => true,
				'show_in_admin_status_list' => true,
				'label_count'               => _n_noop( 'Vitepos Only <span class="count">(%s)</span>', 'Vitepos Only <span class="count">(%s)</span>', 'woocommerce' ),
			)
		);
	}

	/**
	 * The send temp password email is generated by appsbd
	 *
	 * @param \WP_User $user Its the instance of wp user.
	 * @param Strings  $new_password Its the new password.
	 */
	public function send_temp_password_email( $user, $new_password ) {
		ob_start();
		?>
		<p>Hi <?php echo esc_html( $user->first_name . ' ' . $user->last_name ); ?>,</p>
		<h4>Welcome to our <?php echo esc_html( get_bloginfo( 'name' ) ); ?></h4>
		<p>Your temporary password is : <strong><?php echo esc_html( $new_password ); ?></strong>,
			<br> Please use this in login, it may ask you to change password in first login
		</p>
		<p>Thank you</p>
		<?php
		$email_body = ob_get_clean();
		$headers    = array( 'Content-Type: text/html; charset=UTF-8' );
		wp_mail( $user->user_email, 'Your login information', $email_body, $headers );
	}
	/**
	 * The save user fields is generated by appsbd
	 *
	 * @param mixed $user_id Its user Id.
	 *
	 * @return false
	 */
	public function save_user_fields( $user_id ) {

		if ( ! current_user_can( 'edit_user', $user_id ) ) {
			return false;
		}
		$outlets = AppInput::post_value( 'outlets', array() );
		$outlets = array_filter(
			$outlets,
			function( $item ) {
				return ! empty( $item );
			}
		);
		if ( metadata_exists( 'user', $user_id, 'outlet_id' ) ) {
			update_user_meta( $user_id, 'outlet_id', $outlets );
		} else {
			add_user_meta( $user_id, 'outlet_id', $outlets );
		}

	}

	/**
	 * The get price format settigns is generated by appsbd
	 *
	 * @return array
	 */
	public function get_price_format_settigns() {
		$price_arg = array(
			'decimal_separator'  => wc_get_price_decimal_separator(),
			'thousand_separator' => wc_get_price_thousand_separator(),
			'decimals'           => wc_get_price_decimals(),
			'price_format'       => html_entity_decode( str_replace( array( '%1$s', '%2$s' ), array( get_woocommerce_currency_symbol(), '{{amt}}' ), get_woocommerce_price_format() ) ),
		);
		return $price_arg;
	}
	/**
	 * The add user fields is generated by appsbd
	 *
	 * @param mixed $user Its user.
	 */
	public function add_user_fields( $user ) {
		?>
		<table class="form-table">
			<tr class="form-field">
				<th scope="row"><label for="dropdown"><?php $this->esc_e( 'VitePos Outlets' ); ?></label></th>
				<td>
					<?php
					$selected_outlets = array();
					if ( ! empty( $user->ID ) ) {
						$selected_outlets = get_user_meta( $user->ID, 'outlet_id', true );
					}
					?>
					<select name="outlets[]" id="user_select" multiple>
						<?php
						$outlets = Mapbd_pos_warehouse::find_all_by_key_value( 'status', 'A', 'id', 'name' );
						?>
						<option <?php echo ( empty( $selected_outlets ) ? esc_attr( 'selected' ) : '' ); ?> value=""><?php $this->esc_e( 'None' ); ?></option>
						<?php
						appsbd_get_select_options( $outlets, $selected_outlets );
						?>
					</select>
					<div class="description"><?php $this->esc_e( 'You can choose multiple outlets' ); ?></div>
				</td>
			</tr>
		</table>
		<?php
	}

	/**
	 * The vitepos column in order list is generated by appsbd
	 *
	 * @param array $columns Its column list.
	 *
	 * @return array
	 */
	public function vitepos_column_in_order_list( $columns ) {
		$reordered_columns = array();
				foreach ( $columns as $key => $column ) {
			$reordered_columns[ $key ] = $column;
			if ( 'order_status' == $key ) {
								$reordered_columns['is_vt_pos'] = '<i class="vps vps-vt-pos"></i>';
			}
		}
		return $reordered_columns;

	}

	/**
	 * The vitepos column value in order list is generated by appsbd
	 *
	 * @param mixed $column Its column id.
	 * @param mixed $post_id Its post id.
	 */
	public function vitepos_column_value_in_order_list( $column, $post_id ) {
		if ( 'is_vt_pos' == $column ) {
			$is_vitepos = get_post_meta( $post_id, '_is_vitepos', true );
			if ( ! empty( $is_vitepos ) && 'Y' == $is_vitepos ) {
				?>
				<span data-app-title="<?php $this->esc_e( 'Order processed by vitepos.' ); ?>">
					<?php $this->esc_e( 'Yes' ); ?>
				</span>
				<?php
			} else {
				$this->esc_e( '-' );
			}
		}
	}
			/**
			 * The get barcode input args is generated by appsbd
			 *
			 * @return array
			 */
	public function get_barcode_input_args() {
		return array(
			'id'          => '_vt_barcode',
			'desc_tip'    => true,
			'class'       => 'short',
			'description' => $this->__( 'It is custom barcode data of woocommerce for vitepos' ),
			'label'       => $this->__( 'Barcode Value' ),
		);
	}

	/**
	 * The product barcode custom field is generated by appsbd
	 */
	public function product_barcode_custom_field() {
		$args = $this->get_barcode_input_args();
		woocommerce_wp_text_input( $args );
	}

	/**
	 * The variation barcode custom field is generated by appsbd
	 *
	 * @param mixed $loop Its loop param.
	 * @param mixed $variation_data Its variation_data param.
	 * @param mixed $variation Its variation param.
	 */
	public function variation_barcode_custom_field( $loop, $variation_data, $variation ) {
		$args          = $this->get_barcode_input_args();
		$args['id']    = '_vt_barcode[' . $loop . ']';
		$args['value'] = get_post_meta( $variation->ID, '_vt_barcode', true );
		woocommerce_wp_text_input( $args );
	}

	/**
	 * The save product barcode is generated by appsbd
	 *
	 * @param mixed $post_id Its post_id param.
	 */
	public function save_product_barcode( $post_id ) {
		$product = wc_get_product( $post_id );
		$barcode = AppInput::post_value( '_vt_barcode', '' );
		$product->update_meta_data( '_vt_barcode', $barcode );
		$product->save();
	}

	/**
	 * The save variation barcode is generated by appsbd
	 *
	 * @param mixed $variation_id Its variation_id param.
	 * @param mixed $i Its i param.
	 */
	public function save_variation_barcode( $variation_id, $i ) {
		$barcode_array = AppInput::post_value( '_vt_barcode', array() );
		$barcdoe       = ! empty( $barcode_array[ $i ] ) ? $barcode_array[ $i ] : '';
		update_post_meta( $variation_id, '_vt_barcode', esc_attr( $barcdoe ) ) || add_post_meta( $variation_id, '_vt_barcode', esc_attr( $barcdoe ) );
	}
	/**
	 * The getSettings is generated by appsbd
	 *
	 * @param string $type Its string.
	 *
	 * @return \stdClass Its stdclass.
	 */
	public static function get_settings( $type = '' ) {
		$settings       = new \stdClass();
		$basic_settings = self::get_module_option();
		if ( empty( $basic_settings['barcode_field'] ) ) {
			$basic_settings['barcode_field'] = 'ID';
		}
		$basic_settings['currency_symbol'] = get_woocommerce_currency_symbol();
		$basic_settings['currency_code']   = get_woocommerce_currency();

		if ( isset( $basic_settings['POS_link'] ) ) {
			unset( $basic_settings['POS_link'] );
		}
		if ( ! isset( $basic_settings['login_type'] ) ) {
			$basic_settings['login_type'] = '';
		}
		if ( ! isset( $basic_settings['pos_link'] ) ) {
			$basic_settings['pos_link'] = '';
		}
		if ( ! empty( $basic_settings['login_type'] ) ) {
			$basic_settings['login_type'] = strtoupper( $basic_settings['login_type'] );
			if ( 'W' == $basic_settings['login_type'] ) {
				$basic_settings['pos_link'] = self::get_module_instance()->get_pos_link();
			}
		}

		if ( isset( $basic_settings['wp_login_url'] ) ) {
			unset( $basic_settings['wp_login_url'] );
		}
		if ( isset( $basic_settings['pos_page'] ) ) {
			unset( $basic_settings['pos_page'] );
		}
		if ( isset( $basic_settings['pos_color'] ) ) {
			unset( $basic_settings['pos_color'] );
		}
		if ( isset( $basic_settings['rc_v3_secret_key'] ) ) {
			unset( $basic_settings['rc_v3_secret_key'] );
		}
		if ( ! isset( $basic_settings['pos_mode'] ) ) {
			 $basic_settings['pos_mode'] = 'G';
		}
		if ( ! isset( $basic_settings['pos_mode'] ) ) {
			$basic_settings['tax_method'] = self::tax_method();
		}

		if ( ! isset( $basic_settings['product_status'] ) ) {
			unset( $basic_settings['product_status'] );
		}
		if ( 'R' == $basic_settings['pos_mode'] ) {
			$basic_settings['offline_order_status'] = 'N';
		} elseif ( 'P' == $basic_settings['pos_mode'] ) {
			if ( ! empty( $basic_settings['is_kitchen'] ) && 'Y' == $basic_settings['is_kitchen'] ) {
				$basic_settings['offline_order_status'] = 'N';
			}
		} else {
			$basic_settings['is_kitchen'] = 'N';
		}
		$stock_settings               = self::get_module_instance()->get_stock_settings();
		$basic_settings['stockable']  = $stock_settings->is_stockable;
		$basic_settings['stock_type'] = $stock_settings->stock_type;
		if ( ! empty( $basic_settings['is_kitchen'] ) && 'Y' == $basic_settings['is_kitchen'] ) {
			$obj        = new \stdClass();
			$obj->title = 'Completed';
			$obj->val   = 'completed';
			/**
			 * Its for check is there any change before process
			 *
			 * @since 2.0
			 */
			$basic_settings['kitchen_com_status'] = apply_filters( 'vitepos/filter/pay-first-kitchen-status', null );
		}
		$product_status        = self::get_module_instance()->get_product_status();
		$basic_settings['fps'] = hash( 'crc32b', $basic_settings['stockable'] . $basic_settings['barcode_field'] . $basic_settings['pos_mode'] . $basic_settings['stock_type'] . json_encode( $product_status ) );
		if ( self::is_push_enabled() ) {
			$basic_settings['o_sync_intval'] = 60000;
			$basic_settings['p_sync_intval'] = 600000;
		} else {
			$basic_settings['o_sync_intval'] = 30000;
			$basic_settings['p_sync_intval'] = 300000;
		}
		$settings->basic_settings = $basic_settings;
		$settings->inv_settings   = Invoice_Settings::get_settings();
		if ( 'G' == $basic_settings['pos_mode'] ) {
			$settings->inv_settings->show_waiter_info    = false;
			$settings->inv_settings->show_table_info     = false;
			$settings->inv_settings->show_order_type     = false;
			$settings->inv_settings->show_current_status = false;
		}
		$custom_field = new Mapbd_Pos_Custom_Field();
		$custom_field->status( 'A' );
		$custom_fields = $custom_field->select_all();
		foreach ( $custom_fields as &$field ) {
			if ( ! empty( $field->options ) && is_string( $field->options ) ) {
				$field->options = unserialize( $field->options );
			}
		}
		$settings->custom_fields = $custom_fields;
		$settings->payment_gws   = POS_Payment::get_clients_payment_getways();
		$settings->push_settings = self::get_clients_push_settings();
		/**
		 * Its for check is there any change before process
		 *
		 * @since 2.0
		 */
		$additional = apply_filters( 'vitepos/settings/additional', null );
		if ( ! empty( $additional ) ) {
			$settings->additional = $additional;
		}
		return $settings;
	}

	/**
	 * The is kitchen is generated by appsbd
	 *
	 * @return bool
	 */
	public static function is_kitchen() {
		$pos_mode = self::get_pos_mode();
		if ( 'G' == $pos_mode || 'R' == $pos_mode ) {
			return false;
		}
		return 'Y' == self::get_module_option( 'is_kitchen', 'N' );
	}

	/**
	 * The pay first kitchen status is generated by appsbd
	 *
	 * @param \stdClass $obj It object.
	 *
	 * @return \stdClass
	 */
	public function pay_first_kitchen_status( $obj ) {
		/**
		 * Need to check is kitchen later
		 * if(self::is_kitchen()){
			$obj=new \stdClass();
			$obj->title="Completed";
			$obj->val="completed";
		}else {}
		*/
			$obj        = new \stdClass();
			$obj->title = 'Completed';
			$obj->val   = 'completed';

		return $obj;
	}

	/**
	 * The check push settings is generated by appsbd
	 *
	 * @param mixed $push_settings Its push settings.
	 *
	 * @return mixed
	 */
	public function check_push_settings( $push_settings ) {
		if ( ! empty( $push_settings->pusher ) ) {
			$push_settings->pusher = (object) $push_settings->pusher;
			$pusher                =&$push_settings->pusher;
			if ( empty( $pusher->is_enable ) && ! empty( $pusher->is_pusher_enable ) ) {
				$pusher->is_enable = $pusher->is_pusher_enable;
				unset( $pusher->is_pusher_enable );
			}
			if ( empty( $pusher->pushser_secret ) || empty( $pusher->pushser_app_id ) || empty( $pusher->pushser_key ) || empty( $pusher->pushser_cluster ) ) {
				$pusher->is_enable = 'N';
			}
			if ( ! empty( $pusher->pushser_app_id ) ) {
				unset( $pusher->pushser_app_id );
			}
			if ( ! empty( $pusher->pushser_secret ) ) {
				unset( $pusher->pushser_secret );
			}
		}
		return $push_settings;
	}
	/**
	 * The get clients push settings is generated by appsbd
	 *
	 * @return mixed
	 */
	public static function get_clients_push_settings() {
		$push_settings = self::get_module_instance()->get_push_settings();

		/**
		 * Its for check is there any change before process
		 *
		 * @since 1.0
		 */
		return apply_filters( 'vitepos/filter/push-settings', $push_settings );
	}

	/**
	 * The get countries is generated by appsbd
	 *
	 * @param bool $is_with_states Its is_with_states param.
	 *
	 * @return array
	 */
	public function get_countries( $is_with_states = false ) {
		$countries_obj      = new \WC_Countries();
		$countries          = $countries_obj->__get( 'countries' );
		$response_countries = array();
		foreach ( $countries as $code => $country ) {
			$country_object                    = new \stdClass();
			$country_object->name              = $country;
			$country_object->code              = $code;
			$country_object->nationality_title = $country;
			if ( $is_with_states ) {
				$states = $countries_obj->get_states( $code );
				if ( ! empty( $states ) ) {
					foreach ( $states as $state_id => $state ) {
						$state_obj                = new \stdClass();
						$state_obj->id            = $state_id;
						$state_obj->name          = $state;
						$country_object->states[] = $state_obj;
					}
				}
			}
			$response_countries[] = $country_object;
		}
		return $response_countries;
	}
	/**
	 * The check captcha is generated by appsbd
	 *
	 * @param array $payload Its the payload param.
	 *
	 * @return bool
	 */
	public static function check_captcha( $payload ) {
		$is_enable_rc = self::get_module_option( 'is_rc_v3', false );
		if ( ! empty( $is_enable_rc ) ) {
			$token         = ! empty( $payload['g_token'] ) ? $payload['g_token'] : '';
			$rc_secret_key = self::get_module_option( 'rc_v3_secret_key', false );
			if ( ! empty( $token ) && Recaptcha::is_valid( $token, $rc_secret_key ) ) {
				return true;
			} else {
				self::get_module_instance()->add_error( 'Captcha error, please try again' );
				return false;
			}
		} else {
			return true;
		}
	}
	/**
	 * The get timezone is generated by appsbd
	 */
	public function get_timezone() {
		$tz_list = \DateTimeZone::listIdentifiers( \DateTimeZone::ALL );
		return $tz_list;
	}

	/**
	 * The get admin settings is generated by appsbd
	 *
	 * @return array
	 */
	public function get_admin_settings() {
		$options = array();
		if ( empty( $this->options['barcode_field'] ) ) {
			$this->options['barcode_field'] = 'ID';
		}
		if ( empty( $this->options['new_badge_duration'] ) ) {
			$this->options['new_badge_duration'] = 15;
		}
		if ( ! isset( $this->options['pos_row_col'] ) ) {
			$this->options['pos_row_col'] = '';
		}
		if ( ! isset( $this->options['pos_mode'] ) ) {
			$this->options['pos_mode'] = 'G';
		}
		if ( ! isset( $this->options['tax_method'] ) ) {
			$this->options['tax_method'] = self::tax_method();
		}
		$options['basic_settings'] = $this->options;

		$options['basic_settings']['product_status'] = $this->get_product_status();

		if ( ! empty( $options['basic_settings']['is_rc_v3'] ) ) {
			$options['basic_settings']['is_rc_v3'] = (bool) $options['basic_settings']['is_rc_v3'];
		}
		$options['pages']            = $this->get_page_list();
		$options['df_pos_link']      = site_url( 'vitepos' );
		$options['pos_link']         = $this->get_pos_link();
		$options['pos_login_ph']     = wp_login_url();
		$options['inv_settings']     = Invoice_Settings::get_settings();
		$options['pos_customer_obj'] = null;
		if ( ! empty( $this->options['pos_customer'] ) ) {
			$user = get_user_by( 'id', $this->options['pos_customer'] );
			if ( ! empty( $user->ID ) ) {
				$return_user                 = new \stdClass();
				$return_user->id             = $user->ID;
				$return_user->name           = ! empty( $user->first_name ) ? $user->first_name . ' ' . $user->last_name : $user->user_nicename;
				$options['pos_customer_obj'] = $return_user;
			}
		}
		$options['push_settings']  = $this->get_push_settings();
		$options['stock_settings'] = self::get_stock_settings();
		/**
		 * Its for payment method
		 *
		 * @since 1.0
		 */
		$options['license_info'] = apply_filters( 'vitepos/filter/get-license-info', null );
		if ( ! empty( $options['license_info']->license_key ) ) {
			$options['license_info']->license_key = substr( $options['license_info']->license_key, 0, 4 ) . '-XXXX-XXXX-' . substr( $options['license_info']->license_key, -4 );
		}

		return $options;
	}

	/**
	 * The get product status is generated by appsbd
	 *
	 * @return array|string|string[]
	 */
	public function get_product_status() {
		$current_status = $this->get_option( 'product_status', array( 'publish' ) );
		if ( ! is_array( $current_status ) ) {
			return array( 'publish' );
		}
		return $current_status;
	}

	/**
	 * The get stock settings is generated by appsbd
	 *
	 * @return object|\stdClass
	 */
	public static function get_stock_settings() {
		if ( self::get_pos_mode() == 'G' ) {
			$stock_settings = (object) get_option( 'vtpos_stock_setting', array() );
			if ( empty( $stock_settings->is_stockable ) ) {
				$stock_settings               = new \stdClass();
				$stock_settings->is_stockable = 'N';
			}
		} else {
			$stock_settings               = new \stdClass();
			$stock_settings->is_stockable = 'N';
		}
		if ( 'Y' == $stock_settings->is_stockable && empty( $stock_settings->stock_type ) ) {
			$stock_settings->stock_type = 'O';
		}
		return $stock_settings;
	}
	/**
	 * The get stock settings is generated by appsbd
	 *
	 * @return object|\stdClass
	 */
	public static function is_stockable() {
		return self::get_stock_settings()->is_stockable == 'Y';
	}

	/**
	 * The get stock settings is generated by appsbd
	 *
	 * @return bool
	 */
	public static function is_default_stock() {
		if ( self::get_stock_settings()->is_stockable == 'Y' && self::get_stock_settings()->stock_type == 'O' ) {
			return false;
		}
		return true;
	}

	/**
	 * The is stock linked with is generated by appsbd
	 *
	 * @return bool
	 */
	public function is_stock_linked_with() {
		$stock_settings = self::get_stock_settings();
		if ( ( ! empty( $stock_settings->stock_type ) ) && ( 'O' == $stock_settings->stock_type ) && ( ! empty( $stock_settings->is_linked_outlet ) ) && ( 'Y' == $stock_settings->is_linked_outlet ) && ( ! empty( $stock_settings->linked_outlet ) ) ) {
			return true;
		}
		return false;
	}

	/**
	 * The is stock linked with is generated by appsbd
	 *
	 * @return bool
	 */
	public static function is_enable_customer_email() {
		return 'Y' == self::get_module_option( 'is_email_customer', 'N' );
	}
	/**
	 * The get push settings is generated by appsbd
	 *
	 * @return object
	 */
	public function get_push_settings() {
		$push_settings = (object) get_option( 'vtpos_push_setting', array() );
		if ( empty( $push_settings->pusher ) ) {
			$push_settings->pusher                   = new \stdClass();
			$push_settings->pusher->is_pusher_enable = 'I';
		}
		return $push_settings;
	}

	/**
	 * The online product in stock is generated by appsbd
	 *
	 * @param mixed $value Its the current value.
	 * @param mixed $product Its the product.
	 *
	 * @return bool
	 */
	public function online_product_in_stock( $value, $product ) {
		if ( $this->is_stock_linked_with() ) {
			$value = true;
		}
		return $value;
	}

	/**
	 * The online order stock update is generated by appsbd
	 *
	 * @param mixed $order_id Its order id.
	 *
	 * @return bool|void
	 */
	public function online_order_stock_update( $order_id ) {
		if ( ! self::is_stockable() || ! $this->is_stock_linked_with() ) {
			return true;
		}
		$stock_settings = self::get_stock_settings();
		if ( empty( $stock_settings->linked_outlet ) ) {
			return true;
		}

		$order = wc_get_order( $order_id );
		if ( ! $order ) {
			return;
		}
		if ( $order instanceof \WC_Order ) {
			$is_vitepos = $order->get_meta( '_is_vitepos' ) == 'Y';
			if ( $is_vitepos ) { 				return true;
			}
			$is_already_calculated = $order->get_meta( '_vt_stock_cal' );
			if ( 'Y' == $is_already_calculated ) {
				return true;
			}

			$current_stocks = array();

			foreach ( $order->get_items() as $item ) {
				if ( $item instanceof \WC_Order_Item_Product ) {
					$product_id   = $item->get_product_id();
					$variation_id = $item->get_variation_id();
					$final_id     = ! empty( $variation_id ) ? $variation_id : $product_id;
					Mapbd_Pos_Warehouse::reduce_stock_for_outlet(
						$final_id,
						$stock_settings->linked_outlet,
						$item->get_quantity(),
						'Online Order completed',
						$order->get_id(),
						'OR'
					);
				}
				$std               = new \stdClass();
				$std->product_id   = $product_id;
				$std->variation_id = $variation_id;
				/**
				 * Its for check is there any change before process
				 *
				 * @since 2.0
				 */
				$std->stock = apply_filters(
					'vitepos/filter/outlet-stock',
					0,
					$final_id,
					$stock_settings->linked_outlet
				);

				$current_stocks[] = $std;
			}
			if ( ! empty( $current_stocks ) ) {
				/**
				 * Its for check is there any change before process
				 *
				 * @since 2.0
				 */
				do_action( 'vitepos/action/stock-updated', $current_stocks, $stock_settings->linked_outlet );
			}
			update_post_meta( $order->get_id(), '_vt_stock_cal', 'Y' );
			update_post_meta( $order->get_id(), '_vt_stock_reverse', 'N' );
		}
	}
	/**
	 * The online product stock quantity is generated by appsbd
	 *
	 * @param mixed       $value Its the value.
	 * @param \WC_Product $product Its the product.
	 *
	 * @return mixed
	 */
	public function online_product_stock_quantity( $value, $product ) {
		if ( $this->is_stock_linked_with() ) {
			$stock_settings = self::get_stock_settings();
			/**
			 * Its for check is there any change before process
			 *
			 * @since 2.0
			 */
			return apply_filters(
				'vitepos/filter/outlet-stock',
				0,
				$product->get_id(),
				$stock_settings->linked_outlet
			);
		}
		return $value;
	}

	/**
	 * The backorders allowed is generated by appsbd
	 *
	 * @param mixed $status Its status param.
	 * @param mixed $product_id Its product_id param.
	 * @param mixed $product Its product param.
	 *
	 * @return bool
	 */
	public function backorders_allowed( $status, $product_id, $product ) {
		if ( $this->is_stock_linked_with() ) {
			$stock_settings = self::get_stock_settings();
			/**
			 * Its for check is there any change before process
			 *
			 * @since 2.0
			 */
			$stock = apply_filters(
				'vitepos/filter/outlet-stock',
				0,
				$product_id,
				$stock_settings->linked_outlet
			);
			return $stock > 0;
		}
		return $status;
	}
	/**
	 * The get page list is generated by appsbd
	 *
	 * @return array
	 */
	public function get_page_list() {
		$args         = array(
			'sort_order'  => 'asc',
			'sort_column' => 'post_title',
			'post_type'   => 'page',
			'post_status' => 'publish',
		);
		$pages        = get_pages( $args );
		$return_pages = array();
		foreach ( $pages as $page ) {
			$return_pages[ (int) $page->ID ] = $page->post_title;
		}
		return $return_pages;
	}

	/**
	 * The get admin settings is generated by appsbd
	 */
	public function get_admin_options() {
		$response = new Ajax_Confirm_Response();
		$response->display_with_response( true, $this->get_admin_settings() );
	}
	/**
	 * The ajax request callback is generated by appsbd
	 */
	public function refresh_app() {
		$response = new Ajax_Confirm_Response();
		foreach ( $this->kernel_object->module_list as $module_object ) {
			$module_object->on_table_create();
			$module_object->on_active();
		}
		if ( self::get_pos_mode() == 'R' ) {
			Mapbd_Pos_Role::set_default_restro_role();
		}
		POS_Role::reset_roles();
		$this->do_version_update( $this->kernel_object->plugin_version, $this->kernel_object->plugin_version, true );
		$this->add_info( 'Successfully refreshed' );
		$response->display_with_response( true );
	}
	/**
	 * The ajax request callback is generated by appsbd
	 */
	public function create_resto_default_role() {
		$response = new Ajax_Confirm_Response();
		if ( self::get_pos_mode() == 'R' ) {
			Mapbd_Pos_Role::set_default_restro_role( true );
		}
		$this->add_info( 'Successfully refreshed' );
		$response->display_with_response( true );
	}
	/**
	 * The ajax request callback is generated by appsbd
	 */
	public function update_invoice_settings() {
		$response = new Ajax_Confirm_Response();
		$app_data = AppInput::get_posted_data();
		if ( Invoice_Settings::save_settings( $app_data ) ) {
			$this->add_info( 'Successfully updated' );
			$response->display_with_response(
				true,
				$this->get_admin_settings()
			);
		} else {
			$this->add_error( 'No change for update' );
			$response->display_with_response(
				false,
				$this->get_admin_settings()
			);
		}
	}
	/**
	 * The ajax request callback is generated by appsbd
	 */
	public function push_invoice_settings() {
		$response = new Ajax_Confirm_Response();
		$app_data = AppInput::get_posted_data();
		if ( update_option( 'vtpos_push_setting', $app_data ) || add_option( 'vtpos_push_setting', $app_data ) ) {
			$this->add_info( 'Successfully updated' );
			$response->display_with_response(
				true,
				$this->get_admin_settings()
			);
		} else {
			$this->add_error( 'No change for update' );
			$response->display_with_response(
				false,
				$this->get_admin_settings()
			);
		}
	}
	/**
	 * The ajax request callback is generated by appsbd
	 */
	public function stock_settings() {
		$response = new Ajax_Confirm_Response();
		$app_data = AppInput::get_posted_data();
		if ( update_option( 'vtpos_stock_setting', $app_data ) || add_option( 'vtpos_stock_setting', $app_data ) ) {
			$this->add_info( 'Successfully updated' );
			$response->display_with_response(
				true,
				$this->get_admin_settings()
			);
		} else {
			$this->add_error( 'No change for update' );
			$response->display_with_response(
				false,
				$this->get_admin_settings()
			);
		}
	}

	/**
	 * The stock transfer is generated by appsbd
	 */
	public function stock_transfer() {
		$response  = new Ajax_Confirm_Response();
		$outlet_id = AppInput::post_value( 'id' );

		$stlog = new Mapbd_pos_warehouse();
		if ( self::is_stock_linked_with() ) {
			$linked = self::get_stock_settings();
			if ( $linked->linked_outlet == $outlet_id ) {
				$stock_info           = new \stdClass();
				$stock_info->products = 0;
				$stock_info->stocks   = 0;
				if ( Mapbd_pos_warehouse::transfer_online_to_outlet( $linked->linked_outlet, $stock_info ) ) {
					$stock_info->date = gmdate( 'Y-m-d H:i:s' );
					$logs             = get_option( '_vt_transfer_log', array() );
					if ( empty( $logs ) ) {
						$logs = array();
					}
					$logs[] = $stock_info;
					update_option( '_vt_transfer_log', $logs );
					$this->add_info( 'Successfully Transferred,Product %s Stocks %s', $stock_info->products, $stock_info->stocks );
					$response->display_with_response( true );
				}
			} else {
				$this->add_error( 'Different linked outlet found in database. Would you please save the setting before transfer the stock' );
			}
		} else {
			$this->add_error( 'No lined outlet found' );
		}
		$response->display_with_response( false );
	}
	/**
	 * The ajax request callback is generated by appsbd
	 */
	public function get_customers() {
		$response      = new Ajax_Confirm_Response();
		$src_props     = AppInput::get_value( 'searchKey', '' );
		$response_user = array();
		$args          = array(
			'count_total' => true,
		);
		POS_Customer::set_search_param( $src_props, $args );
		$args        = wp_parse_args( $args );
		$user_search = new \WP_User_Query( $args );
		$users       = $user_search->get_results();
		foreach ( $users as $user ) {
			$customer_obj       = new \stdClass();
			$customer_obj->id   = $user->ID;
			$customer_obj->name = $user->first_name ? $user->first_name . ' ' . $user->last_name : $user->user_nicename;
			$response_user[]    = $customer_obj;
		}
		return $response->display_with_response( true, $response_user );
	}

	/**
	 * The ajax request callback is generated by appsbd
	 */
	public function ajax_request_callback() {
		$response    = new Ajax_Confirm_Response();
		$before_save = $this->options;
		$is_updated  = $this->update_request_option();
		if ( $is_updated && ( $before_save['barcode_field'] != $this->options['barcode_field'] ) ) {
			self::increase_sync_value();
		}
		if ( 'R' == self::get_pos_mode() ) {

			Mapbd_Pos_Role::set_default_restro_role();
		}
		$response->display_with_response( $is_updated, $this->get_admin_settings() );
	}

	/**
	 * The get customer object by id is generated by appsbd
	 *
	 * @param any $id Its integer.
	 *
	 * @return \stdClass|null Its null.
	 */
	public static function get_customer_object_by_id( $id ) {
		$user = get_user_by( 'ID', $id );
		if ( ! empty( $user ) ) {
			$customer_obj               = new \stdClass();
			$customer_obj->id           = $user->ID;
			$customer_obj->first_name   = $user->first_name;
			$customer_obj->last_name    = $user->last_name;
			$customer_obj->username     = $user->user_nicename;
			$customer_obj->email        = $user->user_email;
			$customer_obj->city         = $user->billing_city;
			$customer_obj->state        = $user->billing_state;
			$customer_obj->contact_no   = $user->billing_phone;
			$customer_obj->street       = $user->billing_address_1;
			$customer_obj->country      = $user->billing_country;
			$customer_obj->postcode     = $user->billing_postcode;
			$customer_obj->custom_field = (object) get_user_meta( $user->ID, 'custom_field', true );

			return $customer_obj;
		}

		return null;
	}

	/**
	 * The is admin user is generated by appsbd
	 *
	 * @param null $user Its wp_user object.
	 *
	 * @return bool
	 */
	public static function is_admin_user( $user = null ) {
		if ( empty( $user ) ) {
			$user = wp_get_current_user();
		}
		if ( $user instanceof \WP_User ) {
			if ( empty( $user ) || empty( $user->roles ) ) {
				return false;
			}
			if ( in_array( 'administrator', $user->roles ) ) {
				return true;
			}
		}
		return false;
	}

	/**
	 * The check the user is pos or not by appsbd
	 *
	 * @param \WP_User $user Its user param.
	 *
	 * @return bool
	 */
	public static function is_pos_user( $user = null ) {
		if ( empty( $user ) ) {
			$user = wp_get_current_user();
		}
		if ( $user instanceof \WP_User ) {
			if ( empty( $user ) || empty( $user->roles ) ) {
				return false;
			}
			if ( in_array( 'administrator', $user->roles ) ) {
				return true;
			}

			$pos_roles = Mapbd_Pos_Role::get_role_list( 'status', 'A' );
			foreach ( $pos_roles as $agent_role ) {
				if ( in_array( $agent_role->slug, $user->roles ) ) {
					return true;
				}
			}
		}
		return false;
	}
	/**
	 * The add pos rewrite is generated by appsbd
	 */
	public function add_pos_rewrite() {
		$asset_base = str_replace( site_url(), '', plugins_url( '', $this->plugin_file ) );
		add_rewrite_rule( '([^/]*)/vt_sw[^/]*', 'index.php?vitepos_sw=true', 'top' );
				add_rewrite_rule( '([^/]*)/apbd_vt_manifest\.js', 'index.php?vitepos_mf=true', 'top' );
		add_rewrite_rule( '^vitepos/?$', 'index.php?vitepos=true', 'top' );

		if ( true || get_transient( 'viteposactivate' ) ) {
			flush_rewrite_rules( true );
			delete_transient( 'viteposactivate' );
		}
	}

	/**
	 *
	 * The register query var is generated by appsbd
	 *
	 * @param any $vars Its string.
	 *
	 * @return mixed Its string.
	 */
	public function register_query_var( $vars ) {
		$vars[] = 'vitepos';
		$vars[] = 'vitepos_sw';
		$vars[] = 'vitepos_wb';
		$vars[] = 'vitepos_mf';

		return $vars;
	}

	/**
	 * The rewrite templates is generated by appsbd
	 */
	public function rewrite_templates() {
		if ( wp_validate_boolean( get_query_var( 'vitepos_sw' ) ) ) {
			header( 'Content-Type: application/javascript' );
			readfile( dirname( $this->plugin_file ) . '/templates/pos-assets/service-worker.js' );
			exit;
		}
		if ( wp_validate_boolean( get_query_var( 'vitepos_wb' ) ) ) {
			header( 'Content-Type: application/javascript' );
			readfile( dirname( $this->plugin_file ) . '/templates/pos-assets/workbox-79ffe3e0.js' );
			exit;
		}
		if ( wp_validate_boolean( get_query_var( 'vitepos_mf' ) ) ) {
			$this->print_manifest();
			exit;
		}
		if ( wp_validate_boolean( get_query_var( 'vitepos' ) ) ) {
			$post_link_page = $this->get_option( 'POS_link', '' );
			if ( empty( $post_link_page ) ) {
				$this->load_pos_client();
			} else {
				if ( is_user_logged_in() ) {
					if ( 'page' == $post_link_page ) {
						$pos_page = $this->get_option( 'pos_page', '' );
						wp_redirect( get_permalink( $pos_page ) );
						exit;
					}
				}
			}
		} else {
			$post_link_page = $this->get_option( 'POS_link', '' );
			if ( 'page' == $post_link_page ) {
				$pos_page = $this->get_option( 'pos_page', '' );
				if ( is_page( $pos_page ) ) {

					$this->load_pos_client();
				}
			}
		}
	}

	/**
	 * The get wp login url, its for only default login.
	 *
	 * @param string $redirect_to Its redirect to url.
	 *
	 * @return array|mixed|string
	 */
	public function get_wp_pos_login_url( $redirect_to = '' ) {
		$pos_login_page = strtoupper( $this->get_option( 'login_type', '' ) );
		if ( 'W' == $pos_login_page ) {
			$login_url = $this->get_option( 'wp_login_url', '' );
			if ( ! empty( $login_url ) ) {
				return $login_url;
			}
		}
		return wp_login_url( $redirect_to );
	}

	/**
	 * The get manifest link is generated by appsbd
	 *
	 * @return string
	 */
	public function get_manifest_link() {
		return $this->get_pos_link( false ) . '/apbd_vt_manifest.js';
	}

	/**
	 * The get sw link is generated by appsbd
	 *
	 * @return string
	 */
	public function get_sw_link() {
		return $this->get_plugin_url( 'templates/pos-assets/service-worker.js' );
			}
	/**
	 * The get pos link is generated by appsbd
	 *
	 * @param bool $is_only_wp_type Its only_wp_type.
	 *
	 * @return false|string|\WP_Error
	 */
	public function get_pos_link( $is_only_wp_type = false ) {
		if ( $is_only_wp_type ) {
			$pos_login_page = strtoupper( $this->get_option( 'login_type', '' ) );
			if ( 'W' != $pos_login_page ) {
				return '';
			}
		}
		$post_link_page = $this->get_option( 'POS_link', '' );
		if ( empty( $post_link_page ) ) {
			return site_url( 'vitepos' );
		} else {
			$pos_page = $this->get_option( 'pos_page', '' );
			$url      = get_permalink( $pos_page );
			if ( ! is_wp_error( $url ) ) {
				return $url;
			} else {
				return site_url();
			}
		}
	}

	/**
	 * The disable lightspeed cache is generated by appsbd
	 */
	public function disable_lightspeed_cache() {
		if ( function_exists( 'litespeed_purge_url' ) ) {
			litespeed_purge_url( get_permalink() );
			litespeed_purge_url( get_the_permalink() );
			litespeed_purge_url( appsbd_current_url() );
		}
	}
	/**
	 * The load pos client is generated by appsbd
	 */
	public function load_pos_client() {
		$this->disable_lightspeed_cache();
		if ( is_user_logged_in() ) {
			if ( ! self::is_pos_user() ) {
				wp_logout();
				$post_link_page = $this->get_option( 'POS_link', '' );
				if ( 'page' == $post_link_page ) {
					include_once plugin_dir_path( $this->plugin_file ) . '/templates/unauthorize.php';
					exit;
				}
			}
		} else {
			$pos_login_page = strtoupper( $this->get_option( 'login_type', '' ) );
			if ( 'W' == $pos_login_page ) {
				$login_url = $this->get_option( 'wp_login_url', '' );
				if ( ! empty( $login_url ) ) {
					$login_url = add_query_arg( 'redirect_to', urlencode( $this->get_pos_link() ), $login_url );
					wp_redirect( $login_url );
					exit;
				}
				wp_redirect( wp_login_url( $this->get_pos_link() ) );
				exit;
			}
		}
		if ( AppInput::get_value( '_vtf' ) == 'manifest' ) {
			$this->print_manifest();
			exit;
		}
		if ( AppInput::get_value( '_vtf' ) == 'sw' ) {
			header( 'Content-Type: application/javascript' );
			readfile( dirname( $this->plugin_file ) . '/templates/pos-assets/service-worker.js' );
			exit;
		}
				include_once plugin_dir_path( $this->plugin_file ) . '/templates/pos.php';

		exit;
	}

	/**
	 * The get pos color code is generated by appsbd
	 *
	 * @return string
	 */
	public function get_pos_color_code() {
		$pos_color = $this->get_option( 'pos_color', 'def' );
		if ( empty( $pos_color ) ) {
			$pos_color = 'def';
		} else {
			$pos_color = strtolower( $pos_color );
		}
		$skins = array(
			'def'    => '#2563EB',
			'cyan'   => '#00ACC1',
			'green'  => '#4CAF50',
			'purple' => '#7B1FA2',
			'pink'   => '#F06292',
			'red'    => '#b63431',
			'orange' => '#F57C00',
			'gray'   => '#757575',
			'dark'   => '#000000',
		);
		return isset( $skins[ $pos_color ] ) ? $skins[ $pos_color ] : '#ffffff';
	}
	/**
	 * The print manifest is generated by appsbd
	 */
	public function print_manifest() {
		$manifest = new Manifest();
		$manifest->set_name( 'vitepos', 'vitepos' );
		$manifest->set_theme_color( $this->get_pos_color_code() );
		$manifest->set_display( 'standalone' );
		$manifest->add_icon( self::get_module_instance()->get_favicon(), '256x256', 'image/png' );
		$manifest->set_start_url( $this->get_pos_link() );
				$manifest->set_prop( 'kiosk_enabled', true );
		$manifest->set_prop( 'kiosk_only', true );
		
		$manifest->set_prop( '$schema', 'https://json.schemastore.org/web-manifest-combined.json' );
		wp_send_json( $manifest );
	}
	/**
	 * The option form is generated by appsbd
	 */
	public function option_form() {
		$this->set_title( 'Settings' );

		$this->display();
	}

	/**
	 * The get menu title is generated by appsbd
	 *
	 * @return mixed Its mixed
	 */
	public function get_menu_title() {
		return $this->__( 'Settings' );
	}

	/**
	 * The get menu sub title is generated by appsbd
	 *
	 * @return mixed Its mixed.
	 */
	public function get_menu_sub_title() {
		return $this->__( 'View All Settings' );
	}

	/**
	 * The get menu icon is generated by appsbd
	 *
	 * @return string Its string.
	 */
	public function get_menu_icon() {
		return 'fa fa-cog';
	}

	/**
	 * The on active is generated by appsbd
	 */
	public function on_active() {
		parent::on_active(); 		self::process_on_update();
	}

	/**
	 * The process on update is generated by appsbd
	 */
	public static function process_on_update() {
		Mapbd_pos_purchase::create_db_table();
		Mapbd_pos_purchase_item::create_db_table();
		Mapbd_pos_vendor::create_db_table();
		Mapbd_pos_warehouse::create_db_table();
		Mapbd_pos_counter::create_db_table();
		Mapbd_Pos_Cash_Drawer::create_db_table();
		Mapbd_Pos_Cash_Drawer_Log::create_db_table();
		Mapbd_Pos_Cash_Drawer_Types::create_db_table();
		Mapbd_Pos_Addon::create_db_table();
		Mapbd_Pos_Addon_Field::create_db_table();
		Mapbd_Pos_Addon_Field_Option::create_db_table();
		Mapbd_Pos_Addon_Rule_Group::create_db_table();
		Mapbd_Pos_Addon_Rule::create_db_table();
		Mapbd_Pos_Table::create_db_table();
		Mapbd_Pos_Stock_Transfer::create_db_table();
		Mapbd_Pos_Stock_Transfer_Item::create_db_table();
		Mapbd_Pos_Stock_Log::create_db_table();
		Mapbd_Pos_Custom_Field::create_db_table();
		Mapbd_Pos_Message::create_db_table();
		set_transient( 'viteposactivate', 1 );
	}

	/**
	 * The email body content is generated by appsbd
	 *
	 * @param any $content Its string.
	 *
	 * @return string Its string.
	 */
	public function email_body_content( $content ) {
		if ( empty( $content ) ) {
			$content = 'Dear {{customer name}}';
		}

		return $content;
	}


	/**
	 * The get pos language is generated by appsbd
	 */
	public function get_pos_language() {
		echo json_encode( Client_Language::get_pos_languages( $this->kernel_object ) );
	}

	/**
	 * The client header assets is generated by appsbd
	 *
	 * @param array $links Its all links.
	 *
	 * @return mixed
	 */
	public function client_header_assets( $links ) {
		$pos_color = $this->get_option( 'pos_color', 'def' );
		if ( empty( $pos_color ) || 'def' == $pos_color ) {
			$pos_color = 'default';
		} else {
			$pos_color = strtolower( $pos_color );
		}
		$links[] = array(
			'href' => $this->get_plugin_url( 'assets/font.css?v=' . $this->kernel_object->plugin_version ),
			'rel'  => 'stylesheet',
		);
		$links[] = array(
			'href' => $this->get_plugin_url( 'templates/pos-assets/css/color-' . $pos_color . '.css?v=' . $this->kernel_object->plugin_version ),
			'rel'  => 'stylesheet',
		);
		$links[] = array(
			'href' => $this->get_plugin_url( 'templates/pos-assets/css/vitepos.css?v=' . $this->kernel_object->plugin_version ),
			'rel'  => 'preload',
			'as'   => 'style',
		);
		$links[] = array(
			'href' => $this->get_plugin_url( 'templates/pos-assets/js/vitepos.js?v=' . $this->kernel_object->plugin_version ),
			'rel'  => 'preload',
			'as'   => 'script',
		);
		$links[] = array(
			'href' => $this->get_plugin_url( 'templates/pos-assets/css/vitepos.css?v=' . $this->kernel_object->plugin_version ),
			'rel'  => 'stylesheet',
		);

		$push_settings = $this->get_clients_push_settings();
		if ( ! empty( $push_settings->pusher ) ) {
			$pusher_settings = (object) $push_settings->pusher;
			if ( 'Y' == $pusher_settings->is_enable ) {
				$links[] = array(
					'href' => 'https://js.pusher.com/7.2/pusher.min.js',
					'rel'  => 'preload',
					'as'   => 'script',
				);
			}
		}

		return $links;
	}

	/**
	 * The client footer scripts is generated by appsbd
	 *
	 * @param mixed $scripts Its scripts param.
	 *
	 * @return mixed
	 */
	public function client_footer_scripts( $scripts ) {
		$push_settings = $this->get_clients_push_settings();
		if ( ! empty( $push_settings->pusher ) ) {
			$pusher_settings = (object) $push_settings->pusher;
			if ( 'Y' == $pusher_settings->is_enable ) {
				$scripts[] = array(
					'src' => 'https://js.pusher.com/7.2/pusher.min.js',
				);
			}
		}
		$scripts[] = array(
			'src' => $this->get_plugin_url( 'templates/pos-assets/js/vitepos.js?v=' . $this->kernel_object->plugin_version ),
		);

		return $scripts;
	}

	/**
	 * The client header is generated by appsbd
	 */
	public function client_header() {

		/**
		 * Its for check is there any change before process
		 *
		 * @since 2.0
		 */
		$links = apply_filters( 'vitepos/filter/header-links', array(), $this->kernel_object->plugin_version );
		foreach ( $links as $link ) {
			call_user_func( 'printf', '<link %s>', $this->array_attrs( $link ) );
		}
		/**
		 * Its for check is there any change before process
		 *
		 * @since 2.0
		 */
		$scripts = apply_filters( 'vitepos/filter/header-scripts', array(), $this->kernel_object->plugin_version );
		foreach ( $scripts as $script ) {
			call_user_func( 'printf', '<script %s></script>', $this->array_attrs( $script ) );
		}
	}

	/**
	 * The client header is generated by appsbd
	 */
	public function client_footer() {

		/**
		 * Its for check is there any change before process
		 *
		 * @since 2.0
		 */
		$scripts = apply_filters( 'vitepos/filter/footer-scripts', array(), $this->kernel_object->plugin_version );
		foreach ( $scripts as $link ) {
			call_user_func( 'printf', '<script %s></script>', $this->array_attrs( $link ) );
		}
	}

	/**
	 * The array attrs is generated by appsbd
	 *
	 * @param mixed $arr_data its array to html attr.
	 *
	 * @return string
	 */
	public function array_attrs( $arr_data ) {
		$attr_return = '';
		foreach ( $arr_data as $attr => $val ) {
			$attr_return .= $attr . '="' . $val . '" ';
		}

		return $attr_return;
	}

	/**
	 * The woocommerce admin order totals after discount is generated by appsbd
	 *
	 * @param mixed $order_id Its order id.
	 */
	public function woocommerce_admin_order_totals_after_discount( $order_id ) {
		$is_vite_post = 'Y' == get_post_meta( $order_id, '_is_vitepos', true );
		if ( $is_vite_post ) {
			$tax_method = get_post_meta( $order_id, '_vt_tax_method', true );
			if ( 'A' == $tax_method ) {
				$this->discount_total_line( $order_id );
			}
		}
	}
	/**
	 * The woocommerce admin order totals after tax is generated by appsbd
	 *
	 * @param any $order_id Its string.
	 */
	public function woocommerce_admin_order_totals_after_tax( $order_id ) {
		$is_vite_post = 'Y' == get_post_meta( $order_id, '_is_vitepos', true );
		if ( $is_vite_post ) {
			$tax_method = get_post_meta( $order_id, '_vt_tax_method', true );
			if ( 'A' != $tax_method ) {
				$this->discount_total_line( $order_id );
			}
		}
	}

	/**
	 * The discount total line is generated by appsbd
	 *
	 * @param mixed $order_id Its the order id.
	 */
	public function discount_total_line( $order_id ) {
		$order = wc_get_order( $order_id );
		if ( $order->get_total_fees() < 0 ) {
			?>
			<tr>
				<td class="label">
					<?php $this->esc_e( 'Discount' ); ?>
					<small><i><?php $this->esc_e( '(Fee - Discount)' ); ?></i></small>:
				</td>
				<td width="1%"></td>
				<td class="total">
					<?php
					$fee = round( $order->get_total_fees(), wc_get_price_decimals(), PHP_ROUND_HALF_DOWN );
					echo wp_kses_post(
						wc_price(
							$fee,
							array( 'currency' => $order->get_currency() )
						)
					);
					?>
				</td>
			</tr>
			<?php
		}
		$rounding = (float) $order->get_meta( '_vtp_miss_total' );
		if ( 0.0 != $rounding ) {
			?>
			<tr>
				<td class="label"><?php $this->esc_attr_e( 'Calculation Rounding Factor' ); ?></td>
				<td width="1%"></td>
				<td class="total">
					<?php
					wp_kses_post( wc_price( $rounding, array( 'currency' => $order->get_currency() ) ) );
					?>
				</td>
			</tr>
			<?php
		}

	}

	/**
	 * The show order meta is generated by appsbd
	 *
	 * @param any $order_id Its string.
	 */
	public function show_order_meta( $order_id ) {
		$order        = wc_get_order( $order_id );
		$is_vite_post = $order->get_meta( '_is_vitepos' ) == 'Y';
		if ( $is_vite_post ) {
			?>
			<tr>
				<td colspan="3" class="label label-highlight"> <i class="vps vps-vt-pos vtp-order-dtls-icon"></i> <?php $this->esc_e( 'Information' ); ?></td>
			</tr>
			<tr>
				<td colspan="3" class="vtp-order-tr-line"></td>
			</tr>
			<tr>
				<td class="label label-highlight">
					<?php $this->esc_attr_e( 'Cash tendered amount' ); ?>:
				</td>
				<td width="1%"></td>
				<td>
					<?php
					echo wp_kses_post(
						wc_price(
							$order->get_meta( '_vtp_tendered_amount' ),
							array( 'currency' => $order->get_currency() )
						)
					);
					?>


				</td>
			</tr>
			<tr>
				<td class="label label-highlight">
					<?php $this->esc_attr_e( 'Change amount' ); ?>:
				</td>
				<td width="1%"></td>
				<td>
					<?php
					echo wp_kses_post(
						wc_price(
							$order->get_meta( '_vtp_change_amount' ),
							array( 'currency' => $order->get_currency() )
						)
					);
					?>
				</td>
			</tr>
			<tr>
				<td class="label label-highlight">
					<?php $this->esc_attr_e( 'Order Note' ); ?>:
				</td>
				<td width="1%"></td>
				<td>
					<?php echo esc_html( $order->get_meta( '_vtp_order_note' ) ); ?>
				</td>
			</tr>
			<tr>
				<td class="label label-highlight">
					<?php $this->esc_attr_e( 'Payment Method(s)' ); ?>:
				</td>
				<td width="1%"></td>
				<td> </td>
			</tr>
			<?php
			$methods = array(
				'C' => $this->__( 'Cash' ),
				'S' => $this->__( 'Swipe Machine' ),
				'O' => $this->__( 'Others' ),
			);
			/**
			 * Its for payment method
			 *
			 * @since 1.0
			 */
			$methods         = apply_filters( 'apbd-vitepos/filter/payment-methods', $methods );
			$payment_methods = $order->get_meta( '_vtp_payment_list', true );
			if ( ! empty( $payment_methods ) && count( $payment_methods ) > 0 ) {
				?>
			<tr>
				<td colspan="3" class="vtp-order-tr-line"></td>
			</tr>
				<?php
				foreach ( $payment_methods as $payment_method ) {
					?>
					<tr>
						<td class="">
							<div class="label label-highlight">
							<?php
							$payment_type = ! empty( $payment_method['type'] ) ? $payment_method['type'] : 'U';
							echo esc_html( ! empty( $methods[ $payment_type ] ) ? $methods[ $payment_type ] : $this->__( 'Unknown' ) );
							?>
							 :
							</div>
							<small>
								<i>
								<?php
								if ( 'S' == $payment_type && ! empty( $payment_method['card_info'] ) ) {
									echo esc_html( 'xxxx-xxxx-xxxx-' . $payment_method['card_info'] );
								} elseif ( ! empty( $payment_method['payment_note'] ) ) {
									echo esc_html( $payment_method['payment_note'] );
								}
								?>
								</i>
							</small>
						</td>
						<td width="1%"></td>
						<td valign="top">
							<?php
							echo wp_kses_post( wc_price( $payment_method['amount'], array( 'currency' => $order->get_currency() ) ) );
							?>
						</td>
					</tr>
					<?php
				}
				?>
				<tr>
					<td colspan="3" class="vtp-order-tr-line"></td>
				</tr>
			 <?php } ?>
			<tr>
				<td class="label label-highlight">
					<?php $this->esc_attr_e( 'Outlet' ); ?>:
				</td>
				<td width="1%"></td>
				<td>
					<?php
					$outlet_id  = $order->get_meta( '_vtp_outlet_id', true );
					$counter_id = $order->get_meta( '_vtp_counter_id', true );
					if ( ! empty( $outlet_id ) ) {
						$outlet_info = Mapbd_Pos_Warehouse::find_by( 'id', $outlet_id );
						if ( ! empty( $outlet_info ) ) {
							echo esc_html( $outlet_info->name );
						}
					}
					if ( ! empty( $counter_id ) ) {
						$counter_info = Mapbd_pos_counter::find_by( 'id', $counter_id, array( 'outlet_id' => $outlet_id ) );
						if ( ! empty( $counter_info ) ) {
							echo esc_html( ', ' . $counter_info->name );
						}
					}
					?>
				</td>
			</tr>
			<tr>
				<td class="label label-highlight">
					<?php $this->esc_attr_e( 'Processed by' ); ?>:
				</td>
				<td width="1%"></td>
				<td>
					<?php
					$processed_user_id = $order->get_meta( '_vtp_processed_by', true );
					if ( ! empty( $processed_user_id ) ) {
						$processed_by = get_user_by( 'id', $processed_user_id );
						if ( ! empty( $processed_by ) ) {
							echo esc_html( appsbd_get_user_title_by_user( $processed_by ) );
						}
					}
					?>
				</td>
			</tr>
			<?php
			$offline_id = $order->get_meta( '_vtp_offline_id', true );
			if ( ! empty( $offline_id ) ) {
								$offline_date   = $order->get_meta( '_vtp_offline_process_date', true );
				$offline_date   = gmdate( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $offline_date ) );
				$synced_user_id = $order->get_meta( '_vtp_offline_synced_by', true );
				$synced_by      = '';
				if ( ! empty( $synced_user_id ) ) {
					$synced_user = get_user_by( 'id', $processed_user_id );
					if ( ! empty( $synced_user ) ) {
						$synced_by = appsbd_get_user_title_by_user( $synced_user );
					}
				}
				?>
				<tr>
					<td colspan="3"> &nbsp;</td>
				</tr>
				<tr class="label label-highlight" align="right">
					<td colspan="3"> <?php $this->esc_e( 'Its offline order, details given below' ); ?></td>
				</tr>
				<tr>
					<td colspan="3" class="vtp-order-tr-line"></td>
				</tr>
				<tr>
					<td class="">
						<div class="label label-highlight">
							<?php $this->esc_e( 'Offline ID' ); ?>:
						</div>
					</td>
					<td width="1%"></td>
					<td valign="top">
						<?php
						echo esc_html( $offline_id );
						?>
					</td>
				</tr>
				<tr>
					<td class="">
						<div class="label label-highlight">
							<?php $this->esc_e( 'Offline Order Time' ); ?>:
						</div>
					</td>
					<td width="1%"></td>
					<td valign="top">
						<?php
						echo esc_html( $offline_date );
						?>
					</td>
				</tr>
				<tr>
					<td class="">
						<div class="label label-highlight">
							<?php $this->esc_e( 'Synced By' ); ?>:
						</div>
					</td>
					<td width="1%"></td>
					<td valign="top">
						<?php
						echo esc_html( $synced_by );
						?>
					</td>
				</tr>
				<?php
			}

			?>
			<tr>
				<td colspan="3" class="vtp-order-tr-line"></td>
			</tr>
			<tr>
				<td colspan="3" class="label label-highlight" align="right">
					<?php $this->esc_e( 'This order  processed form' ); ?>
					<i class="vps vps-vt-pos vtp-order-dtls-icon"></i>.
					<?php $this->esc_e( 'Thank you' ); ?>
				</td>
			</tr>
			<?php
		}
	}

	/**
	 * The before product load is generated by appsbd
	 *
	 * @param mixed $api Its api param.
	 */
	public function before_product_load( $api ) {

		if ( class_exists( '\Orderable_Products' ) ) {
			remove_filter( 'woocommerce_format_price_range', 'Orderable_Products::format_price_range' );
		}
	}

	/**
	 * The set customer billing address is generated by appsbd
	 *
	 * @param mixed $billing_address Its billing_address param.
	 * @param mixed $order Its order param.
	 * @param mixed $customer_id Its customer_id param.
	 *
	 * @return array
	 */
	public function set_customer_billing_address( $billing_address, $order, $customer_id ) {
		if(empty($customer_id)){
			$default_customer=$this->get_option( 'pos_customer', null );
			if(!empty($default_customer)){
			    $customer_id=$default_customer;
			}
		}
		if ( ! empty( $customer_id ) ) {
			$customer        = get_user_by( 'id', $customer_id );
			$customer_data   = POS_Customer::get_user_object( $customer );
			$billing_address = array(
				'first_name' => $customer_data->first_name,
				'last_name'  => $customer_data->last_name,
				'email'      => $customer_data->email,
				'phone'      => ! empty( $customer_data->phone ) ? $customer_data->phone : '',
				'address_1'  => $customer_data->street,
				'city'       => $customer_data->city,
				'state'      => $customer_data->state,
				'postcode'   => $customer_data->zip_code,
				'country'    => $customer_data->country,
			);
		}
		return $billing_address;
	}

	/**
	 * The send push message is generated by appsbd
	 *
	 * @param mixed  $data It the pusher msg.
	 * @param string $type Its type param.
	 * @param string $outlet Its outlet param.
	 */
	public function send_push_message( $data, $type = 'g', $outlet = '' ) {
		$push_settings = $this->get_push_settings();
		if ( ! empty( $push_settings->pusher ) ) {
			$pusher_settings = (object) $push_settings->pusher;
			try {
				if ( 'Y' == $pusher_settings->is_enable ) {
					$options = array(
						'cluster' => $pusher_settings->pushser_cluster,
						'useTLS'  => false,
					);
					$pusher  = new \Pusher\Pusher(
						$pusher_settings->pushser_key,
						$pusher_settings->pushser_secret,
						$pusher_settings->pushser_app_id,
						$options
					);
					if ( '*' == $outlet ) {
						$outlet = '';
					}
					$d       = new \stdClass();
					$d->o    = self::is_default_stock() ? 'W' : '' . $outlet;
					$d->t    = $type;
					$d->data = $data;

					$event = 'vtoutlet' . ( ! empty( $outlet ) ? '_' . $outlet : '' );
					$pusher->trigger( '_vtpos_info', $event, $d );
				}
			} catch ( \Pusher\PusherException $e ) {
				return;
			} catch ( \Pusher\ApiErrorException $e ) {
				return;
			} catch ( \GuzzleHttp\Exception\GuzzleException $e ) {
				return;
			}
		}
	}

	/**
	 * The stock updated is generated by appsbd
	 *
	 * @param array  $stocks Its stock object.
	 * @param string $outlet_id its outlet object.
	 */
	public function stock_updated( $stocks = array(), $outlet_id = '' ) {
		if ( self::is_default_stock() ) {
			$outlet_id = '';
		}
		$msg = '';

		if ( is_object( $stocks ) ) {
			$msg .= $this->get_stock_push_str( $stocks );
		} elseif ( is_array( $stocks ) ) {
			foreach ( $stocks as $stock ) {
				$msg .= $this->get_stock_push_str( $stock );
			}
		}
		$msg = rtrim( $msg, '|' );
		if ( ! empty( $msg ) ) {
			/**
			 * Its for check is there any change before process
			 *
			 * @since 2.0
			 */
			do_action( 'vitepos/action/send-push', $msg, 'st', $outlet_id );
		}
	}

	/**
	 * The send order push is generated by appsbd
	 *
	 * @param mixed  $order Its vitepos order details.
	 * @param string $can_cancel Its can_cancel param.
	 * @param string $change_type Optional for change type.
	 */
	public function send_order_push( $order, $can_cancel = 'Y', $change_type = '' ) {
		$obj    = new \stdClass();
		$obj->i = $order->order_id;
		$obj->m = '';
		if ( ! empty( $order->msgs ) ) {
			$obj->m = end( $order->msgs );
		}
		$obj->s = $order->status;
		$obj->t = $order->status_title;
		if ( 'N' == $can_cancel ) {
			$obj->c = 'N';
		}
		if ( 'T' == $change_type && isset( $order->table_id ) ) {
			$obj->a = $order->table_id; 		}
		if ( ! empty( $order->outlet_id ) ) {
			/**
			 * Its for check is there any change before process
			 *
			 * @since 2.0
			 */
			do_action( 'vitepos/action/send-push', $obj, 'or', $order->outlet_id );
		}
	}

	/**
	 * The set item wise fee discount is generated by appsbd
	 *
	 * @param \WC_Order $order
	 */
	public function set_item_wise_fee_discount( $order ) {
		$is_already = $order->get_meta( '_vtp_is_it_feedis' );
		if ( true || 'Y' != $is_already ) {
			$total_fee = (float) $order->get_meta( '_vtp_fee_total' );
			$total_dis = (float) $order->get_meta( '_vtp_discount_total' );
			$total_dis = 0 > $total_dis ? ( -1 ) * $total_dis : $total_dis;
			$total_fee = 0 > $total_fee ? ( -1 ) * $total_fee : $total_fee;
										$order_total = $order->get_subtotal();
						 						if ( 0 < $order_total && ( 0 < $total_dis || 0 < $total_fee ) ) {
				foreach ( $order->get_items() as $item ) {
					$item_sub_total = (float) $item->get_subtotal();
					if ( $total_dis > 0 ) {
						$dis_amount = (float) ( $total_dis / $order_total ) * $item_sub_total;
						$item->update_meta_data( '_vtp_discount', $dis_amount );
					}
					if ( $total_dis > 0 ) {
						$fee_amount = (float) ( $total_fee / $order_total ) * $item_sub_total;
						$item->update_meta_data( '_vtp_fee', $fee_amount );
					}
				}
			}
			update_post_meta( $order->get_id(), '_vtp_is_it_feedis', 'Y' );
		}
	}
	/**
	 * The send order push is generated by appsbd
	 *
	 * @param \WC_Order $order Its vitepos order details.
	 * @param string    $type Its the stock update type R=Reduce,I=Increase
	 */
	public function wc_default_stock_log_add( $order, $type = 'R' ) {

		$current_stocks = array();
		if ( $order instanceof \WC_Order ) {
			switch ( $order->get_status() ) {
				case 'refunded':
					$msg = 'Online order returned';

					break;
				case 'cancelled':
					$msg = 'Online order cancelled';
					break;
				case 'completed':
				case 'processing':
					$msg = 'Online order completed';
					break;
				default:
					$msg = 'Online order updated';
			}
			foreach ( $order->get_items() as $item ) {
				$std = new \stdClass();

				if ( $item instanceof \WC_Order_Item_Product ) {
					$product_id        = $item->get_product_id();
					$variation_id      = $item->get_variation_id();
					$std->product_id   = $product_id;
					$std->variation_id = $variation_id;

					if ( ! empty( $variation_id ) ) {
						$final_id = $variation_id;
					} else {
						$final_id = $product_id;
					}
					$product   = wc_get_product( $final_id );
					$pre_stock = 'R' == $type ? ( $product->get_stock_quantity() + $item->get_quantity() ) : ( $product->get_stock_quantity() - $item->get_quantity() );
					Mapbd_Pos_Stock_Log::AddLog(
						'R' == $type ? 'O' : 'I',
						0,
						$final_id,
						$pre_stock,
						$item->get_quantity(),
						$msg,
						$order->get_id(),
						'OR',
						'W'
					);
					$std->stock = $product->get_stock_quantity();
				}
				$current_stocks[] = $std;
			}
		}

		/**
		 * Its for check is there any change before process
		 *
		 * @since 2.0
		 */
		do_action( 'vitepos/action/stock-updated', $current_stocks, 0 );
	}

	/**
	 * The get stock push str is generated by appsbd
	 *
	 * @param mixed $stock Its stock param.
	 *
	 * @return string
	 */
	public function get_stock_push_str( $stock ) {
		if ( empty( $stock->variation_id ) ) {
			$stock->variation_id = '';
		}
		return $stock->product_id . ':' . $stock->variation_id . ':' . $stock->stock . '|';
	}
	/**
	 * The send customer email is generated by appsbd
	 *
	 * @param mixed $order_id Its order_id param.
	 */
	public function send_customer_email( $order_id ) {
		$order           = wc_get_order( $order_id );
		$email_address   = $order->get_billing_email();
		$is_need_to_send = true;
		$outlet_id       = $order->get_meta( '_vtp_outlet_id' );
		if ( ! empty( $outlet_id ) ) {
			$outlet = Mapbd_pos_warehouse::find_by( 'id', $outlet_id );
			if ( $outlet->email == $email_address ) {
				$is_need_to_send = false;
			}
		}
		if ( $is_need_to_send ) {
			$email_list = WC()->mailer()->get_emails();
			if ( ! empty( $email_list['WC_Email_Customer_Completed_Order'] ) ) {
				$email_list['WC_Email_Customer_Completed_Order']->trigger( $order_id );
				$email_count = $order->get_meta( '_vtp_send_em_count' );
				if ( empty( $email_count ) ) {
					$order->update_meta_data( '_vtp_send_em_count', 1 );
				} else {
					$order->update_meta_data( '_vtp_send_em_count', $email_count + 1 );
				}
				$order->add_order_note( 'Customer email sent' );
			}
		} else {
			$order->add_order_note( 'Skipped customer email sent as same email of outlet' );
		}

	}

	/**
	 * The is push enabled is generated by appsbd
	 *
	 * @return bool
	 */
	public static function is_push_enabled() {
		$push_settings = self::get_module_instance()->get_push_settings();
		if ( ! empty( $push_settings->pusher ) ) {
			$pusher_settings = (object) $push_settings->pusher;
			if ( ! empty( $pusher_settings->is_enable ) && 'Y' == $pusher_settings->is_enable ) {
				return true;
			}
		}
		return false;
	}

}
