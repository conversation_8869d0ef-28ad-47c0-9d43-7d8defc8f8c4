<?php
/**
 * Its api for customer
 *
 * @since: 12/07/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package VitePos\Api\V1
 */

namespace VitePos\Api\V1;

use Appsbd\V1\libs\API_Data_Response;
use Appsbd\V1\libs\AppInput;
use VitePos\Libs\API_Base;
use VitePos\Libs\POS_Customer;
use VitePos\Modules\APBD_EPOS_Settings;
use VitePos\Modules\POS_Settings;

/**
 * Class pos_customer_api
 *
 * @package VitePos\Api\V1
 */
class Pos_Customer_Api extends API_Base {

	/**
	 * The set api base is generated by appsbd
	 *
	 * @return mixed|string
	 */
	public function set_api_base() {
		return 'customer';
	}

	/**
	 * The routes is generated by appsbd
	 *
	 * @return mixed|void
	 */
	public function routes() {
		$this->register_rest_route( 'post', 'list', array( $this, 'get_list' ) );
		$this->register_rest_route( 'POST', 'create', array( $this, 'create_customer' ) );
		$this->register_rest_route( 'POST', 'check-unique', array( $this, 'check_unique' ) );
		$this->register_rest_route( 'POST', 'customer-list', array( $this, 'customer_list' ) );
		$this->register_rest_route( 'POST', 'delete-customer', array( $this, 'delete_customer' ) );
		$this->register_rest_route( 'GET', 'details/(?P<id>\d+)', array( $this, 'customer_details' ) );

	}
	/**
	 * The set route permission is generated by appsbd
	 *
	 * @param \VitePos\Libs\any $route Its string.
	 *
	 * @return bool
	 */
	public function set_route_permission( $route ) {
		switch ( $route ) {
			case 'create':
				return current_user_can( 'customer-add' );
			case 'delete-customer':
				return current_user_can( 'customer-delete' );
			default:
				POS_Settings::is_pos_user();
		}

		return parent::set_route_permission( $route );
	}

	/**
	 * The list is generated by appsbd
	 *
	 * @return API_Data_Response
	 */
	public function get_list() {
		$page          = AppInput::get_value( 'page', 1 );
		$limit         = AppInput::get_value( 'limit', 100 );
		$response_data = new API_Data_Response();
		$response_user = array();
		$src_props     = $this->get_payload( 'src_by', array() );
		$sort_props    = $this->get_payload( 'sort_by', array() );
		$args          = array(
			'count_total' => true,
			'number'      => $limit,
			'paged'       => $page,
		);
		POS_Customer::set_search_param( $src_props, $args );
		POS_Customer::set_sort_param( $sort_props, $args );
		$args        = wp_parse_args( $args );
		$user_search = new \WP_User_Query( $args );
		$total_user  = $user_search->get_total();
		$users       = $user_search->get_results();
		foreach ( $users as $user ) {
			$customer_obj             = new \stdClass();
			$customer_obj->id         = $user->ID;
			$customer_obj->first_name = $user->first_name;
			$customer_obj->last_name  = $user->last_name;
			$customer_obj->username   = $user->user_nicename;
			$customer_obj->email      = $user->user_email;
			$customer_obj->city       = $user->billing_city;
						$customer_obj->contact_no = $user->contact_no;
			$customer_obj->street     = $user->street;
			$customer_obj->country    = $user->billing_country;
			$customer_obj->postcode   = $user->billing_postcode;
			$response_user[]          = $customer_obj;
		}
		if ( $response_data->set_total_records( $total_user ) ) {
			$response_data->rowdata = $response_user;
		}
		return $response_data;
	}

	/**
	 * The get customer is generated by appsbd
	 *
	 * @param mixed $args Its argument of customer search.
	 *
	 * @return array
	 */
	public function get_customer( $args ) {
		$args        = wp_parse_args( $args );
		$user_search = new \WP_User_Query( $args );
		$user_search->get_total();
		return (array) $user_search->get_results();
	}
	/**
	 * The customer list is generated by appsbd
	 *
	 * @return API_Data_Response
	 */
	public function customer_list() {
		$page  = $this->get_payload( 'page', 1 );
		$limit = $this->get_payload( 'limit', 20 );

		$response_data = new API_Data_Response();
		$response_user = array();
		$src_props     = $this->get_payload( 'src_by', array() );
		$sort_props    = $this->get_payload( 'sort_by', array() );
		$args          = array(
			'role__in'    => array( 'customer', 'subscriber' ),
			'count_total' => true,
			'number'      => $limit,
			'paged'       => $page,
		);

		POS_Customer::set_search_param( $src_props, $args );
		POS_Customer::set_sort_param( $sort_props, $args );
		$args        = wp_parse_args( $args );
		$user_search = new \WP_User_Query( $args );
		$total_user  = $user_search->get_total();
		$users       = $user_search->get_results();

		foreach ( $users as $user ) {
			$customer_obj             = new \stdClass();
			$customer_obj->id         = $user->ID;
			$customer_obj->first_name = $user->first_name;
			$customer_obj->last_name  = $user->last_name;
			$customer_obj->username   = $user->user_nicename;
			$customer_obj->email      = $user->user_email;
			$customer_obj->city       = $user->billing_city;
						$customer_obj->contact_no = $user->billing_phone;
			$customer_obj->street     = $user->billing_address_1;
			$customer_obj->country    = $user->billing_country;
			$customer_obj->postcode   = $user->billing_postcode;
			$response_user[]          = $customer_obj;
		}
		$response_data->limit = $this->payload['limit'];
		$response_data->page  = $this->payload['page'];
		if ( $response_data->set_total_records( $total_user ) ) {
			$response_data->rowdata = $response_user;
		}
		return $response_data;
	}

	/**
	 * The delete customer is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function delete_customer() {
		if ( ! empty( $this->payload ) ) {
			$id = intval( $this->payload['id'] );
			require_once ABSPATH . 'wp-admin/includes/user.php';
			$user = get_user_by( 'ID', $id );
			if ( ! empty( $user ) ) {
				if ( wp_delete_user( $user->ID ) ) {
					$this->add_info( 'Successfully deleted' );
					$this->response->set_response( true, '' );
					return $this->response;
				}
			}
		}
		$this->add_error( 'Delete failed' );
		$this->response->set_response( false, '' );
		return $this->response;
	}


	/**
	 * The check unique is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function check_unique() {
		if ( ! empty( $this->payload ) && ! empty( $this->payload['fld_name'] ) ) {
			$this->payload['fld_name'] = strtolower( $this->payload['fld_name'] );
			if ( 'email' == $this->payload['fld_name'] ) {
				if ( email_exists( $this->payload['fld_value'] ) ) {
					$this->response->set_response( false, 'Email exist / not valid' );
					return $this->response;
				} else {
					$this->response->set_response( true, 'Valid email' );
					return $this->response;
				}
			} elseif ( 'username' == $this->payload['fld_name'] ) {
				if ( username_exists( $this->payload['fld_value'] ) ) {
					$this->response->set_response( false, 'Username exist / not valid' );
					return $this->response;
				} else {
					$this->response->set_response( true, 'Valid username' );
					return $this->response;
				}
			} else {
				$this->response->set_response( false, 'No valid field checking' );
				return $this->response;
			}
		} else {
			$this->response->set_response( false, 'Empty field' );
			return $this->response;
		}

	}


	/**
	 * The customer details is generated by appsbd
	 *
	 * @param any $data Its string.
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function customer_details( $data ) {
		if ( ! empty( $data['id'] ) ) {
			$id           = intval( $data['id'] );
			$customer_obj = POS_Settings::get_customer_object_by_id( $id );
			$this->set_response( true, 'data found', $customer_obj );
			return $this->response;
		}
		$this->set_response( false, 'data not found or invalid param' );
		return $this->response;

	}

	/**
	 * The create customer is generated by appsbd
	 *
	 * @return \Appsbd\V1\libs\API_Response
	 */
	public function create_customer() {
		if ( ! empty( $this->payload ) ) {
			$old_cus = get_user_by( 'ID', $this->payload['id'] );
			if ( ! empty( $old_cus ) ) {
				$customer_obj = new POS_Customer();
				if ( $customer_obj->set_from_array( $this->payload ) ) {
					if ( $customer_obj->update() ) {
						$rcustomer_obj = POS_Settings::get_customer_object_by_id( $this->payload['id'] );
						$this->add_info( 'Successfully updated' );
						$this->response->set_response( true, '', $rcustomer_obj );
					}
				}
				return $this->response->get_response();
			}

			$customer_obj = new POS_Customer();
			if ( $customer_obj->set_from_array( $this->payload, true ) ) {
				$customer_obj->added_by( $this->get_current_user_id() );
				$customer_obj->role( 'Customer' );
				if ( $customer_obj->save() ) {
					$this->add_info( 'Successfully created' );
					$this->response->set_response( true, '', $customer_obj );
				}
				return $this->response->get_response();
			}
			return $this->response->get_response();
		}
		$this->add_error( 'No customer found to add' );
		return $this->response->get_response();
	}

}
