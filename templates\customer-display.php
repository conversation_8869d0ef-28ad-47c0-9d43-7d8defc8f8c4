<?php
/**
 * Customer Display Screen Template
 *
 * @var \VitePos\Modules\POS_Settings $this
 * @package vitepos
 */
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="<?php echo esc_html( $this->get_pos_color_code() ); ?>">
    <link rel="icon" href="<?php echo esc_url( $this->get_favicon() ); ?>">
    <title>شاشة عرض الزبون - Customer Display</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Common Display CSS -->
    <link href="<?php echo esc_url( $this->get_plugin_url( 'templates/pos-assets/css/display-common.css' ) ); ?>" rel="stylesheet">
    <!-- Customer Display CSS -->
    <link href="<?php echo esc_url( $this->get_plugin_url( 'templates/pos-assets/css/customer-display.css' ) ); ?>" rel="stylesheet">

    <script>
        var vitePosBase = "<?php echo esc_url( get_rest_url( null, 'vitepos/v1' ) ); ?>/";
        var customerConfig = {
            refreshInterval: 3000, // 3 seconds
            showWelcomeMessage: true,
            autoScroll: true,
            urls: {
                served_list: vitePosBase + "restaurant/served-list",
                order_details: vitePosBase + "order/details"
            }
        };
    </script>
</head>
<body class="customer-display-body">
    <div class="container-fluid h-100">
        <!-- Header -->
        <div class="row customer-header">
            <div class="col-12">
                <div class="header-content text-center p-4">
                    <div class="logo-section mb-3">
                        <img src="<?php echo esc_url( $this->get_plugin_url( 'templates/pos-assets/logo.png' ) ); ?>"
                             alt="Logo" class="restaurant-logo">
                    </div>
                    <h1 class="welcome-title">أهلاً وسهلاً بكم</h1>
                    <p class="welcome-subtitle">نتمنى لكم تجربة ممتعة</p>
                </div>
            </div>
        </div>

        <!-- Current Order Display -->
        <div class="row current-order-section">
            <div class="col-12">
                <div class="current-order-card" id="current-order">
                    <div class="order-header">
                        <h3><i class="fas fa-receipt"></i> الطلب الحالي</h3>
                    </div>
                    <div class="order-content" id="current-order-content">
                        <div class="no-order-message">
                            <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                            <p>لا يوجد طلب حالياً</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Status Display -->
        <div class="row status-section">
            <div class="col-md-4">
                <div class="status-card preparing">
                    <div class="status-icon">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="status-info">
                        <h4>قيد التحضير</h4>
                        <div class="order-numbers" id="preparing-orders">
                            <!-- Preparing orders will be shown here -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="status-card ready">
                    <div class="status-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="status-info">
                        <h4>جاهز للاستلام</h4>
                        <div class="order-numbers" id="ready-orders">
                            <!-- Ready orders will be shown here -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="status-card served">
                    <div class="status-icon">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <div class="status-info">
                        <h4>تم التقديم</h4>
                        <div class="order-numbers" id="served-orders">
                            <!-- Served orders will be shown here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Queue Display -->
        <div class="row queue-section">
            <div class="col-12">
                <div class="queue-header">
                    <h3><i class="fas fa-list-ol"></i> قائمة الانتظار</h3>
                </div>
                <div class="queue-container">
                    <div class="queue-scroll" id="queue-display">
                        <!-- Queue items will be displayed here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row customer-footer">
            <div class="col-12">
                <div class="footer-content text-center p-3">
                    <div class="current-time" id="current-time"></div>
                    <p class="footer-message">شكراً لاختياركم لنا</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Sound -->
    <audio id="notification-sound" preload="auto">
        <source src="<?php echo esc_url( $this->get_plugin_url( 'templates/pos-assets/success_tone.mp3' ) ); ?>" type="audio/mpeg">
    </audio>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo esc_url( $this->get_plugin_url( 'templates/pos-assets/js/display-config.js' ) ); ?>"></script>
    <script src="<?php echo esc_url( $this->get_plugin_url( 'templates/pos-assets/js/customer-display.js' ) ); ?>"></script>
</body>
</html>
