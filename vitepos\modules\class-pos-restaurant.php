<?php
/**
 * Its for Pos Counter module
 *
 * @package VitePos\Modules
 */

namespace VitePos\Modules;

use Appsbd\V1\Core\BaseModule;
use Appsbd\V1\libs\Ajax_Data_Response;
use Appsbd\V1\libs\Ajax_Response;
use Automattic\WooCommerce\Utilities\NumberUtil;
use VitePos\Libs\Pos_Product_Addon;
use Vitepos\Models\Database\Mapbd_Pos_Addon;
use Vitepos\Models\Database\Mapbd_pos_counter;

/**
 * Class POS_Counter
 */
class Pos_Restaurant extends BaseModule {


	/**
	 * The initialize is generated by appsbd
	 */
	public function initialize() {
		add_filter( 'woocommerce_register_shop_order_post_statuses', array( $this, 'register_custom_order_status' ) );
	}

	/**
	 * The on init is generated by appsbd
	 */
	public function on_init() {
		parent::on_init();
		add_filter( 'wc_order_statuses', array( $this, 'show_custom_order_status_single_order_dropdown' ) );
		add_action( 'woocommerce_after_order_itemmeta', array( $this, 'item_addon_description' ), 10, 3 );

	}

	/**
	 * The register custom order status is generated by appsbd
	 *
	 * @param any $order_statuses Its order status param.
	 *
	 * @return mixed
	 */
	public function register_custom_order_status( $order_statuses ) {
				$order_statuses['wc-vt_in_kitchen']     = array(
			'label'                     => 'In Kitchen',
			'public'                    => false, 			'exclude_from_search'       => false,
			'show_in_admin_status_list' => true,
			'show_in_admin_all_list'    => true,
			'label_count'               => _n_noop(
				'In Kitchen <span class="count">(%s)</span>',
				'In Kitchen <span class="count">(%s)</span>',
				'woocommerce'
			),
		);
		$order_statuses['wc-vt_preparing']      = array(
			'label'                     => 'Preparing',
			'public'                    => false,
			'exclude_from_search'       => false,
			'show_in_admin_all_list'    => true,
			'show_in_admin_status_list' => true,
			'label_count'               => _n_noop(
				'Denied <span class="count">(%s)</span>',
				'Denied <span class="count">(%s)</span>',
				'woocommerce'
			),
		);
		$order_statuses['wc-vt_kitchen_deny']   = array(
			'label'                     => 'Denied from kitchen',
			'public'                    => false,
			'exclude_from_search'       => false,
			'show_in_admin_all_list'    => true,
			'show_in_admin_status_list' => true,
			'label_count'               => _n_noop(
				'Preparing <span class="count">(%s)</span>',
				'Preparing <span class="count">(%s)</span>',
				'woocommerce'
			),
		);
		$order_statuses['wc-vt_ready_to_srv']   = array(
			'label'                     => 'Ready to serve',
			'public'                    => false,
			'exclude_from_search'       => false,
			'show_in_admin_all_list'    => true,
			'show_in_admin_status_list' => true,
			'label_count'               => _n_noop(
				'Ready to serve <span class="count">(%s)</span>',
				'Ready to serve <span class="count">(%s)</span>',
				'woocommerce'
			),
		);
		$order_statuses['wc-vt_cancel_request'] = array(
			'label'                     => 'Cancel Requested',
			'public'                    => false,
			'exclude_from_search'       => false,
			'show_in_admin_all_list'    => true,
			'show_in_admin_status_list' => true,
			'label_count'               => _n_noop(
				'Cancel requested <span class="count">(%s)</span>',
				'Cancel requested <span class="count">(%s)</span>',
				'woocommerce'
			),
		);
		$order_statuses['wc-vt_served']         = array(
			'label'                     => 'Served',
			'public'                    => false,
			'exclude_from_search'       => false,
			'show_in_admin_all_list'    => true,
			'show_in_admin_status_list' => true,
			'label_count'               => _n_noop(
				'Served <span class="count">(%s)</span>',
				'Served <span class="count">(%s)</span>',
				'woocommerce'
			),
		);

		return $order_statuses;

	}

	/**
	 * The show custom order status single order dropdown is generated by appsbd
	 *
	 * @param any $order_statuses Its order status param.
	 *
	 * @return mixed
	 */
	public function show_custom_order_status_single_order_dropdown( $order_statuses ) {
		$order_statuses['wc-vt_in_kitchen']     = 'In Kitchen';
		$order_statuses['wc-vt_preparing']      = 'Preparing';
		$order_statuses['wc-vt_kitchen_deny']   = 'Denied from kitchen';
		$order_statuses['wc-vt_ready_to_srv']   = 'Ready to serve';
		$order_statuses['wc-vt_cancel_request'] = 'Cancel Requested';
		$order_statuses['wc-vt_served']         = 'Served';

		return $order_statuses;
	}

	/**
	 * The item addon description is generated by appsbd
	 *
	 * @param mixed                  $item_id Its item id param.
	 * @param \WC_Order_Item_Product $item Its item param.
	 * @param \WC_Product            $product its product param.
	 *
	 * Its Item descriptionArray ( [0] => Array ( [fld_id] => 1 [fld_title] => Test field 1 [fld_val] => test ) [1] => Array ( [fld_id] => 2 [fld_title] => Field Radio 2 [fld_val] => Array ( [opt_id] => 2 [opt_label] => Test Radio 2 [opt_price] => 1 ) ) [2] => Array ( [fld_id] => 3 [fld_title] => Test Dropdown 3 [fld_val] => Array ( [opt_id] => 4 [opt_label] => Drop Option 1 [opt_price] => 0 ) ) [3] => Array ( [fld_id] => 4 [fld_title] => Test checkbox 4 [fld_val] => Array ( [0] => Array ( [opt_id] => 7 [opt_label] => Test checkbox 1 [opt_price] => 0 ) [1] => Array ( [opt_id] => 9 [opt_label] => Test checkbox 3 [opt_price] => 2 ) ) ) ).
	 */
	public function item_addon_description( $item_id, $item, $product ) {
		$addons = $item->get_meta( '_vtp_addons', true );
		if ( ! empty( $addons ) && is_array( $addons ) ) {
			$item_price = $item->get_meta( '_vtp_items_price', true );
			if ( ! empty( $item_price ) ) {
				echo '<div> Item Price : ' . wc_price( $item_price ) . '</div>';
			}
						foreach ( $addons as $addon ) {
				$this->get_addon_title( $addon );
			}
		}

		
	}

	/**
	 * The get addon title is generated by appsbd
	 *
	 * @param any $addon Its addon param.
	 */
	public function get_addon_title( $addon ) {
		?>
		<div>
		<?php
		echo '+ ' . $addon['fld_title'];
		if ( ! empty( $addon['fld_val'] ) ) {
			if ( is_string( $addon['fld_val'] ) ) {
				echo wp_kses_post( ' ' . $addon['fld_val'] );
			} elseif ( ! empty( $addon['fld_val']['opt_id'] ) ) { 				$price = ! empty( $addon['fld_val']['opt_price'] ) ? ' : <b>' . wc_price( $addon['fld_val']['opt_price'] ) . '</b>' : '';
				echo wp_kses_post( ' (' . $addon['fld_val']['opt_label'] . $price . ')' );
			} elseif ( ! empty( $addon['fld_val'][0] ) ) {
				foreach ( $addon['fld_val'] as $addon_opt ) {
					$price = ! empty( $addon_opt['opt_price'] ) ? ' : <b>' . wc_price( $addon_opt['opt_price'] ) . '</b>' : '';
					echo wp_kses_post( ' (' . $addon_opt['opt_label'] . $price . ')' );
				}
			}
		}
					?>
		</div>
		<?php
	}




}
