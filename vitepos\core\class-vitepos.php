<?php
/**
 * This this is main class of this plugin
 *
 * @package VitePos\Core
 */

namespace VitePos\Core;

use Appsbd\V1\Core\Kernel;
use Appsbd\V1\libs\AppInput;
use VitePos\Api\V1\heartbit_api;
use VitePos\Api\V1\Pos_Addon_Api;
use VitePos\Api\V1\pos_api_config;
use VitePos\Api\V1\pos_customer_api;
use VitePos\Api\V1\pos_order_api;
use VitePos\Api\V1\pos_outlet_api;
use VitePos\Api\V1\pos_product_api;
use VitePos\Api\V1\pos_purchase_api;
use VitePos\Api\V1\Pos_Restaurant_Api;
use VitePos\Api\V1\Pos_Table_Api;
use VitePos\Api\V1\Pos_Display_Api;
use VitePos\Api\V1\pos_user_api;
use VitePos\Api\V1\pos_vendor_api;
use VitePos\Libs\Client_Language;

require_once dirname( __FILE__ ) . '/../../vendor/autoload.php';
/**
 * Is the kernel class file
 */
require_once dirname( __FILE__ ) . '/../../appsbd/v1/core/class-kernel.php';


/**
 * Class VitePos
 *
 * @package VitePos\Core
 */
class VitePos extends Kernel {
	/**
	 * The initialize is generated by appsbd
	 *
	 * @return mixed|void
	 */
	public function initialize() {
		$this->plugin_base = 'apbd-vite-pos';
		$logo              = '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';
		$this->menu_icon   = 'data:image/svg+xml;base64,' . $logo;
		$this->menu_label  = 'VitePOS';
		add_filter( 'appsbd/vitepos/update-icons', array( $this, 'update_icon' ) );
		$this->skip_cache();

	}

	/**
	 * The on init is generated by appsbd
	 */
	public function on_init() {
		parent::on_init();

		/**
		 * To get Active plugin
		 *
		 * @since   1.0
		 */
		if ( appsbd_is_activated_plugin( 'woocommerce/woocommerce.php' ) ) {
			add_action(
				'rest_pre_serve_request',
				function () {
					header( 'Access-Control-Allow-Origin: *' );
					header( 'Access-Control-Allow-Headers: VITE_POS_TOKEN,outlet', false );
				}
			);

			if ( vitepos_is_api_request() ) {
				add_filter(
					'option_active_plugins',
					function ( $plugins ) {
						$no_need = array( 'elementor/elementor.php', 'litespeed-cache/litespeed-cache.php', 'query-monitor/query-monitor.php', 'loco-translate/loco.php', 'loco-automatic-translate-addon-pro/loco-automatic-translate-addon-pro.php', 'hide-my-wp/index.php' );
						foreach ( $plugins as $k => $plugin ) {
							if ( in_array( $plugin, $no_need ) ) {
								unset( $plugins[ $k ] );
							}
						}
						$plugins = array_values( $plugins );
						return $plugins;

					}
				);
			}


			add_action(
				'rest_api_init',
				function () {
					$namespace = 'vitepos/v1';
					new pos_api_config( $namespace, $this );
					new heartbit_api( $namespace, $this );
					new pos_product_api( $namespace, $this );
					new pos_customer_api( $namespace, $this );
					new pos_user_api( $namespace, $this );
					new pos_vendor_api( $namespace, $this );
					new pos_purchase_api( $namespace, $this );
					new pos_outlet_api( $namespace, $this );
					new pos_order_api( $namespace, $this );
					new Pos_Restaurant_Api( $namespace, $this );
					new Pos_Display_Api( $namespace, $this );
					new pos_addon_api( $namespace, $this );
					new Pos_Table_Api( $namespace, $this );
				}
			);
			add_filter( 'woocommerce_email_actions', array( $this, 'stop_order_email' ) );
		}

		$this->add_ajax_action( 'country-list', array( $this, 'country_list' ) );
		$this->add_ajax_action( 'timezone-list', array( $this, 'timezone_list' ) );
		/**
		 * Its for check is there any change before process
		 *
		 * @since 2.0
		 */
		do_action( 'vitepos-init' );

	}

	/**
	 * The stop order email is generated by appsbd
	 *
	 * @param mixed $email_list Its email list param.
	 *
	 * @return array
	 */
	public function stop_order_email( $email_list ) {
		if ( vitepos_is_rest( array( 'order/make-payment', 'order/sync-offline-order' ) ) ) {
			return array();
		}
		return $email_list;
	}
	/**
	 * The _myautoload_method is generated by appsbd
	 *
	 * @param any $class_name_space Its class_name_space param.
	 */
	public function _myautoload_method( $class_name_space ) {

		$class_name_space = str_replace( '\\', '/', $class_name_space );
		$path             = plugin_dir_path( $this->plugin_file );
		$class            = basename( $class_name_space );
		$dir              = strtolower( dirname( $class_name_space ) );
		$filename         = realpath( $path . $dir . '/' . $class . '.php' );
		$filename_class   = $path . ( strtolower( $dir . '/class-' . str_replace( '_', '-', $class ) . '.php' ) ) . '';

		if ( ! empty( $filename_class ) && file_exists( $filename_class ) ) {
			require_once $filename_class;
			return;
		}
		if ( ! empty( $filename ) && file_exists( $filename ) ) {
			require_once $filename;
			return;
		}
	}
	/**
	 * The stop order email admin is generated by appsbd
	 *
	 * @param mixed $email_class Its email class param.
	 */
	public function stop_order_email_admin( $email_class ) {
		remove_action( 'woocommerce_order_status_pending_to_processing_notification', array( $email_class->emails['WC_Email_New_Order'], 'trigger' ) );
		remove_action( 'woocommerce_order_status_pending_to_completed_notification', array( $email_class->emails['WC_Email_New_Order'], 'trigger' ) );
		remove_action( 'woocommerce_order_status_pending_to_on-hold_notification', array( $email_class->emails['WC_Email_New_Order'], 'trigger' ) );
		remove_action( 'woocommerce_order_status_failed_to_processing_notification', array( $email_class->emails['WC_Email_New_Order'], 'trigger' ) );
		remove_action( 'woocommerce_order_status_failed_to_completed_notification', array( $email_class->emails['WC_Email_New_Order'], 'trigger' ) );
		remove_action( 'woocommerce_order_status_failed_to_on-hold_notification', array( $email_class->emails['WC_Email_New_Order'], 'trigger' ) );
		remove_action( 'woocommerce_order_status_cancelled_to_processing_notification', array( $email_class->emails['WC_Email_New_Order'], 'trigger' ) );
		remove_action( 'woocommerce_order_status_cancelled_to_completed_notification', array( $email_class->emails['WC_Email_New_Order'], 'trigger' ) );
		remove_action( 'woocommerce_order_status_cancelled_to_on-hold_notification', array( $email_class->emails['WC_Email_New_Order'], 'trigger' ) );
	}

	/**
	 * The skip cache is generated by appsbd
	 */
	public function skip_cache() {
		$option = get_option( 'litespeed.conf.cache-rest' );
		if ( ! empty( $option ) ) {
			update_option( 'litespeed.conf.cache-rest', false );
		}

		$exclude_url_list = array( 'vitepos' );
		$req_url          = AppInput::get_server_data( 'REQUEST_URI' );
		foreach ( $exclude_url_list as $exclude_url ) {
			if ( strpos( $req_url, $exclude_url ) !== false ) {
				/**
				 * This hook is about no cache for rest api
				 *
				 * @since 1.0.0
				 */
				do_action( 'litespeed_control_set_nocache', 'no-cache for rest api' );
			}
		}
	}

	/**
	 * The country list is generated by appsbd
	 */
	public function country_list() {
		wp_send_json( \VitePos\Modules\POS_Settings::get_module_instance()->get_countries( true ) );
	}
	/**
	 * The time list is generated by appsbd
	 */
	public function timezone_list() {
		wp_send_json( \VitePos\Modules\POS_Settings::get_module_instance()->get_timezone() );
	}

	/**
	 * The register_modules is generated by appsbd
	 *
	 * @return mixed
	 */
	public function register_modules() {
		$this->add_module( 'VitePos\Modules\POS_Dashboard' );
		$this->add_module( 'VitePos\Modules\POS_Role' );
		$this->add_module( 'VitePos\Modules\POS_Warehouse' );
		$this->add_module( 'VitePos\Modules\POS_Payment' );
		$this->add_module( 'VitePos\Modules\POS_Settings' );
		$this->add_module( 'VitePos\Modules\Pos_Restaurant' );
		$this->add_module( 'VitePos\Modules\Pos_Addon' );
		$this->add_module( 'VitePos\Modules\POS_Custom_Field' );
		$this->add_module( 'VitePos\Modules\Pos_Message' );

	}

	/**
	 * The update icon is generated by appsbd
	 *
	 * @param any $icons its icons param.
	 *
	 * @return mixed
	 */
	public function update_icon( $icons ) {
		$icons['defaults'] = $this->get_plugin_url( 'assets-global/img/icon-256-256.png' );
		$icons['1x']       = $icons['defaults'];
		return $icons;
	}
	/**
	 * The on admin global scripts is generated by appsbd
	 *
	 * @return mixed
	 */
	public function on_admin_global_scripts() {
		parent::on_admin_global_scripts();
		if ( $this->check_admin_page() ) {
			return;
		}
		$this->add_script( 'vite-global-js', 'assets-global/script.min.js', true, array(), true );
	}

	/**
	 * The on admin global styles is generated by appsbd
	 */
	public function on_admin_global_styles() {
		$this->add_style( 'vite-global-css', 'assets-global/style.min.css', true );
	}

	/**
	 * The on admin scripts is generated by appsbd
	 */
	public function on_admin_scripts() {
		$script_version = self::is_develop_mode() ? time() : $this->plugin_version;
		wp_enqueue_media();
		wp_register_script(
			'vitepos-admin',
			plugins_url( 'assets/js/admin-script.js', $this->plugin_file ),
			array(),
			$script_version,
			true
		);

		$this->add_script( 'vitepos-admin' );
		$jv_object                                      = new \stdClass();
		$jv_object->ajax_url                            = wp_nonce_url( admin_url( 'admin-ajax.php' ) );
		$jv_object->base_slug                           = $this->plugin_base;
		$jv_object->pos_link                            = site_url( 'vitepos' );
		$jv_object->currency_symbol                     = get_woocommerce_currency_symbol();
		$jv_object->decimal_places                      = wc_get_price_decimals();
		$jv_object->ajax_nonce                          = '';
		$jv_object->app_logo                            = $this->get_plugin_url( 'assets/logo.svg' );
		$jv_object->app_version                         = $this->plugin_version;
		$jv_object->translation_obj                     = new \stdClass();
		$jv_object->translation_obj->availableLanguages = new \stdClass();
		$jv_object->translation_obj->availableLanguages->en_US = 'American English';
		$jv_object->translation_obj->defaultLanguage           = 'en_US';
		$jv_object->translation_obj->translations              = new \stdClass();
		$jv_object->translation_obj->translations->en_US       = Client_Language::get_admin_languages( $this );
		wp_localize_script( 'vitepos-admin', 'vitePos', (array) $jv_object );
	}

	/**
	 * The on admin styles is generated by appsbd
	 */
	public function on_admin_styles() {
		 $this->add_style( 'vitepos-admin', 'admin-style.css' );
	}
	/**
	 * The LiteAdminMenu is generated by appsbd
	 */
	public function wc_warning() {
		add_menu_page(
			$this->menu_label,
			$this->menu_label,
			'activate_plugins',
			$this->plugin_base,
			function() {
				?>
				<div id="appsbd-woo-required">
					<div class="apbd-card">
						<div class="apbd-card-header">
							<i class="vps vps-vt-pos"></i>
						</div>
						<div class="apbd-card-body apbd-text-center">
							<div class="apbd-app-logo-container">
										  <span class="vtp-circle-logo me-3">
											<i class="vps vps-vite-pos"></i>
										  </span>
							</div>
							<strong><?php echo esc_html( $this->__( 'Missing dependency' ) ); ?></strong><br>
							<?php
							if ( ! is_plugin_active( 'woocommerce/woocommerce.php' ) ) {
								echo '<div class="mb-3">' . esc_html( $this->__( 'It requires woocommerce, please install and active woocommerce first' ) ) . '</div>';
							}
							if ( ! is_plugin_active( 'vitepos-lite/vitepos-lite.php' ) ) {
								echo '<div class="mb-3">' . esc_html( $this->__( 'It requires vitepos lite, please install and active vitepos lite first' ) ) . '</div>';
							} elseif ( ! function_exists( 'vitepos_is_api_request' ) ) {
								echo '<div class="mb-3">' . esc_html( $this->__( 'Please install and activate Vitepos Lite version greater than 2.0.3 as there is a mismatch with the Vitepos Lite version.' ) ) . '</div>';
							}
							?>

						</div>
					</div>
				</div>
				<?php
			},
			$this->menu_icon
		);

	}

}
