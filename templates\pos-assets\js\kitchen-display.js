/**
 * Kitchen Display JavaScript
 * Handles kitchen order management and real-time updates
 */

class KitchenDisplay {
    constructor() {
        this.orders = [];
        this.currentFilter = 'all';
        this.refreshInterval = null;
        this.soundEnabled = kitchenConfig.soundEnabled;
        this.autoRefresh = kitchenConfig.autoRefresh;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startClock();
        this.loadOrders();
        
        if (this.autoRefresh) {
            this.startAutoRefresh();
        }
    }

    setupEventListeners() {
        // Category filter buttons
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.setActiveFilter(e.target.dataset.status);
            });
        });

        // Refresh button
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.loadOrders();
        });

        // Modal event listeners
        document.getElementById('save-note').addEventListener('click', () => {
            this.saveKitchenNote();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F5') {
                e.preventDefault();
                this.loadOrders();
            }
        });
    }

    setActiveFilter(status) {
        this.currentFilter = status;
        
        // Update active tab
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-status="${status}"]`).classList.add('active');
        
        this.renderOrders();
    }

    async loadOrders() {
        try {
            this.showLoading();
            
            const response = await fetch(kitchenConfig.urls.kitchen_order_list, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    limit: 50,
                    page: 1
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.orders = data.data.orders || [];
                this.renderOrders();
                this.updateCounts();
                this.updateConnectionStatus(true);
            } else {
                this.showError('فشل في تحميل الطلبات');
                this.updateConnectionStatus(false);
            }
        } catch (error) {
            console.error('Error loading orders:', error);
            this.showError('خطأ في الاتصال بالخادم');
            this.updateConnectionStatus(false);
        }
    }

    renderOrders() {
        const container = document.getElementById('orders-container');
        const filteredOrders = this.filterOrders();

        if (filteredOrders.length === 0) {
            container.innerHTML = this.getEmptyStateHTML();
            return;
        }

        container.innerHTML = filteredOrders.map(order => this.createOrderCard(order)).join('');
        
        // Add event listeners to order cards
        this.attachOrderEventListeners();
    }

    filterOrders() {
        if (this.currentFilter === 'all') {
            return this.orders;
        }
        return this.orders.filter(order => order.status === this.currentFilter);
    }

    createOrderCard(order) {
        const timeElapsed = this.calculateTimeElapsed(order.date_created);
        const isUrgent = timeElapsed > 30; // More than 30 minutes
        const urgentClass = isUrgent ? 'urgent' : '';
        const statusClass = this.getStatusClass(order.status);

        return `
            <div class="order-card ${urgentClass} ${statusClass}" data-order-id="${order.id}">
                <div class="order-header">
                    <div class="order-number">#${order.id}</div>
                    <div class="order-time">
                        <i class="fas fa-clock"></i>
                        <span class="time-elapsed ${isUrgent ? 'time-urgent' : ''}">${timeElapsed} دقيقة</span>
                    </div>
                </div>
                
                <div class="order-items">
                    ${order.line_items.map(item => `
                        <div class="order-item">
                            <div>
                                <div class="item-name">${item.name}</div>
                                ${item.meta_data && item.meta_data.length > 0 ? `
                                    <div class="item-notes">${item.meta_data.map(meta => meta.value).join(', ')}</div>
                                ` : ''}
                            </div>
                            <div class="item-quantity">${item.quantity}</div>
                        </div>
                    `).join('')}
                </div>
                
                <div class="order-actions">
                    ${this.getActionButtons(order)}
                </div>
                
                <div class="order-info">
                    <div class="table-info">
                        ${order.table_number ? `طاولة: ${order.table_number}` : 'طلب خارجي'}
                    </div>
                    <div class="priority-badge ${this.getPriorityClass(timeElapsed)}">
                        ${this.getPriorityText(timeElapsed)}
                    </div>
                </div>
            </div>
        `;
    }

    getActionButtons(order) {
        switch (order.status) {
            case 'vt_in_kitchen':
                return `
                    <button class="action-btn btn-start" onclick="kitchenDisplay.startPreparing(${order.id})">
                        <i class="fas fa-play"></i> بدء التحضير
                    </button>
                    <button class="action-btn btn-deny" onclick="kitchenDisplay.denyOrder(${order.id})">
                        <i class="fas fa-times"></i> رفض
                    </button>
                    <button class="action-btn btn-note" onclick="kitchenDisplay.showNoteModal(${order.id})">
                        <i class="fas fa-sticky-note"></i> ملاحظة
                    </button>
                `;
            case 'vt_preparing':
                return `
                    <button class="action-btn btn-complete" onclick="kitchenDisplay.completePreparing(${order.id})">
                        <i class="fas fa-check"></i> اكتمل
                    </button>
                    <button class="action-btn btn-note" onclick="kitchenDisplay.showNoteModal(${order.id})">
                        <i class="fas fa-sticky-note"></i> ملاحظة
                    </button>
                `;
            case 'vt_ready_to_srv':
                return `
                    <button class="action-btn btn-note" onclick="kitchenDisplay.showNoteModal(${order.id})">
                        <i class="fas fa-sticky-note"></i> ملاحظة
                    </button>
                `;
            default:
                return '';
        }
    }

    getStatusClass(status) {
        switch (status) {
            case 'vt_preparing': return 'preparing';
            case 'vt_ready_to_srv': return 'ready';
            default: return '';
        }
    }

    getPriorityClass(timeElapsed) {
        if (timeElapsed > 45) return 'priority-high';
        if (timeElapsed > 30) return 'priority-medium';
        return 'priority-low';
    }

    getPriorityText(timeElapsed) {
        if (timeElapsed > 45) return 'عاجل';
        if (timeElapsed > 30) return 'متوسط';
        return 'عادي';
    }

    calculateTimeElapsed(dateCreated) {
        const now = new Date();
        const created = new Date(dateCreated);
        const diffInMinutes = Math.floor((now - created) / (1000 * 60));
        return diffInMinutes;
    }

    async startPreparing(orderId) {
        try {
            const response = await fetch(kitchenConfig.urls.start_preparing, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ order_id: orderId })
            });

            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('تم بدء تحضير الطلب');
                this.loadOrders();
            } else {
                this.showError('فشل في بدء التحضير');
            }
        } catch (error) {
            console.error('Error starting preparation:', error);
            this.showError('خطأ في الاتصال');
        }
    }

    async completePreparing(orderId) {
        try {
            const response = await fetch(kitchenConfig.urls.complete_preparing, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ order_id: orderId })
            });

            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('تم إكمال تحضير الطلب');
                this.loadOrders();
                this.playNotificationSound();
            } else {
                this.showError('فشل في إكمال التحضير');
            }
        } catch (error) {
            console.error('Error completing preparation:', error);
            this.showError('خطأ في الاتصال');
        }
    }

    async denyOrder(orderId) {
        if (!confirm('هل أنت متأكد من رفض هذا الطلب؟')) {
            return;
        }

        try {
            const response = await fetch(kitchenConfig.urls.deny_order, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ order_id: orderId })
            });

            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('تم رفض الطلب');
                this.loadOrders();
            } else {
                this.showError('فشل في رفض الطلب');
            }
        } catch (error) {
            console.error('Error denying order:', error);
            this.showError('خطأ في الاتصال');
        }
    }

    showNoteModal(orderId) {
        document.getElementById('note-order-id').value = orderId;
        document.getElementById('kitchen-note').value = '';
        
        const modal = new bootstrap.Modal(document.getElementById('noteModal'));
        modal.show();
    }

    async saveKitchenNote() {
        const orderId = document.getElementById('note-order-id').value;
        const note = document.getElementById('kitchen-note').value.trim();

        if (!note) {
            this.showError('يرجى كتابة ملاحظة');
            return;
        }

        try {
            const response = await fetch(kitchenConfig.urls.add_kitchen_note, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    order_id: orderId,
                    message: note
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('تم حفظ الملاحظة');
                bootstrap.Modal.getInstance(document.getElementById('noteModal')).hide();
                this.loadOrders();
            } else {
                this.showError('فشل في حفظ الملاحظة');
            }
        } catch (error) {
            console.error('Error saving note:', error);
            this.showError('خطأ في الاتصال');
        }
    }

    updateCounts() {
        const counts = {
            all: this.orders.length,
            vt_in_kitchen: this.orders.filter(o => o.status === 'vt_in_kitchen').length,
            vt_preparing: this.orders.filter(o => o.status === 'vt_preparing').length,
            vt_ready_to_srv: this.orders.filter(o => o.status === 'vt_ready_to_srv').length
        };

        document.getElementById('count-all').textContent = counts.all;
        document.getElementById('count-waiting').textContent = counts.vt_in_kitchen;
        document.getElementById('count-preparing').textContent = counts.vt_preparing;
        document.getElementById('count-ready').textContent = counts.vt_ready_to_srv;
    }

    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connection-status');
        if (connected) {
            statusElement.textContent = 'متصل';
            statusElement.className = 'badge bg-success';
        } else {
            statusElement.textContent = 'غير متصل';
            statusElement.className = 'badge bg-danger';
        }
    }

    startClock() {
        const updateClock = () => {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        };

        updateClock();
        setInterval(updateClock, 1000);
    }

    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.loadOrders();
        }, kitchenConfig.refreshInterval);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    attachOrderEventListeners() {
        document.querySelectorAll('.order-card').forEach(card => {
            card.addEventListener('click', (e) => {
                if (!e.target.closest('.order-actions')) {
                    const orderId = card.dataset.orderId;
                    this.showOrderDetails(orderId);
                }
            });
        });
    }

    showOrderDetails(orderId) {
        const order = this.orders.find(o => o.id == orderId);
        if (!order) return;

        const modalBody = document.getElementById('order-details');
        modalBody.innerHTML = `
            <div class="order-details">
                <h5>طلب رقم #${order.id}</h5>
                <p><strong>الوقت:</strong> ${new Date(order.date_created).toLocaleString('ar-SA')}</p>
                <p><strong>الحالة:</strong> ${this.getStatusText(order.status)}</p>
                ${order.table_number ? `<p><strong>الطاولة:</strong> ${order.table_number}</p>` : ''}
                
                <h6>العناصر:</h6>
                <ul class="list-group">
                    ${order.line_items.map(item => `
                        <li class="list-group-item d-flex justify-content-between">
                            <span>${item.name}</span>
                            <span class="badge bg-primary">${item.quantity}</span>
                        </li>
                    `).join('')}
                </ul>
                
                ${order.customer_note ? `
                    <div class="mt-3">
                        <h6>ملاحظة العميل:</h6>
                        <p class="text-muted">${order.customer_note}</p>
                    </div>
                ` : ''}
            </div>
        `;

        const modal = new bootstrap.Modal(document.getElementById('orderModal'));
        modal.show();
    }

    getStatusText(status) {
        switch (status) {
            case 'vt_in_kitchen': return 'في المطبخ';
            case 'vt_preparing': return 'قيد التحضير';
            case 'vt_ready_to_srv': return 'جاهز للتقديم';
            default: return status;
        }
    }

    showLoading() {
        document.getElementById('orders-container').innerHTML = `
            <div class="loading-spinner">
                <div class="spinner"></div>
            </div>
        `;
    }

    getEmptyStateHTML() {
        return `
            <div class="empty-state">
                <i class="fas fa-utensils"></i>
                <p>لا توجد طلبات في هذه الفئة</p>
            </div>
        `;
    }

    playNotificationSound() {
        if (this.soundEnabled) {
            // Play notification sound
            const audio = new Audio('/wp-content/plugins/vitepos/templates/pos-assets/success_tone.mp3');
            audio.play().catch(e => console.log('Could not play sound:', e));
        }
    }

    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showError(message) {
        this.showToast(message, 'error');
    }

    showToast(message, type) {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            ${message}
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

// Initialize kitchen display when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.kitchenDisplay = new KitchenDisplay();
});

// Handle page visibility change to pause/resume auto-refresh
document.addEventListener('visibilitychange', () => {
    if (window.kitchenDisplay) {
        if (document.hidden) {
            window.kitchenDisplay.stopAutoRefresh();
        } else {
            window.kitchenDisplay.startAutoRefresh();
            window.kitchenDisplay.loadOrders();
        }
    }
});
