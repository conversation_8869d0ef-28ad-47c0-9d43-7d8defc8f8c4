<?php
/**
 * Pos Vendor Database Model
 *
 * @package Vitepos\Models\Database
 */

namespace Vitepos\Models\Database;

use VitePos\Core\ViteposModel;

/**
 * Class Mapbd_Pos_Stock_Transfer
 *
 * @package Vitepos\Models\Database
 */
class Mapbd_Pos_Stock_Transfer_Item extends ViteposModel {
	/**
	 * Its property id
	 *
	 * @var int
	 */
	public $id;
	/**
	 * Its property Transfer by
	 *
	 * @var int
	 */
	public $product_id;
	/**
	 * Its property transfer from
	 *
	 * @var int
	 */
	public $product_qty;
	/**
	 * Its property transfer to
	 *
	 * @var int
	 */
	public $transfer_id;

	/**
	 * Mapbd_pos_vendor constructor.
	 */
	public function __construct() {
		parent::__construct();
		$this->set_validation();
		$this->table_name     = 'apbd_pos_stock_transfer_item';
		$this->primary_key    = 'id';
		$this->unique_key     = array();
		$this->multi_key      = array();
		$this->auto_inc_field = array( 'id' );
		$this->app_base_name  = 'apbd-elite-pos';
	}


	/**
	 * The set validation is generated by appsbd
	 */
	public function set_validation() {
		$this->validations = array(
			'id'          => array(
				'Text' => 'Id',
				'Rule' => 'max_length[11]|integer',
			),
			'product_id'  => array(
				'Text' => 'Transfer by',
				'Rule' => 'max_length[11]|integer',
			),
			'product_qty' => array(
				'Text' => 'Transfer Quantity',
				'Rule' => 'max_length[11]|numeric',
			),
			'transfer_id' => array(
				'Text' => 'Transfer id',
				'Rule' => 'max_length[11]|integer',
			),
		);
	}

	/**
	 * The get property raw options is generated by appsbd
	 *
	 * @param \Appsbd\V1\Core\any $property Its string.
	 * @param false               $is_with_select Its bool.
	 *
	 * @return array|string[]
	 */
	public function get_property_raw_options( $property, $is_with_select = false ) {
		$return_obj = array();
		switch ( $property ) {
			case 'transfer_status':
				$return_obj = array(
					'P' => 'Pending',
					'R' => 'Received',
					'D' => 'Declined',
				);
				break;
			default:
		}
		if ( $is_with_select ) {
			return array_merge( array( '' => 'Select' ), $return_obj );
		}

		return $return_obj;
	}

	/**
	 * The create db table is generated by appsbd
	 */
	public static function create_db_table() {
		$this_obj = new static();
		$table    = $this_obj->db->prefix . $this_obj->table_name;
		if ( $this_obj->db->get_var( "show tables like '{$table}'" ) != $table ) {
			$sql = "CREATE TABLE `{$table}` (
					  `id` INT ( 11 ) UNSIGNED NOT NULL AUTO_INCREMENT,
					`product_id` INT ( 11 ) UNSIGNED NOT NULL,
					`product_qty` DECIMAL ( 8, 2 ) UNSIGNED NOT NULL,
					`transfer_id` INT ( 11 ) UNSIGNED NOT NULL,
					PRIMARY KEY (`id`)
					) ";
			require_once ABSPATH . 'wp-admin/includes/upgrade.php';
			dbDelta( $sql );
		}
	}

	/**
	 * The delete by id is generated by appsbd
	 *
	 * @param any $id Its vendor id param.
	 *
	 * @return bool
	 */
	public static function delete_by_id( $id ) {
		return parent::delete_by_key_value( 'id', $id );
	}
}
