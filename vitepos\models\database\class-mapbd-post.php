<?php
/**
 * Pos Warehouse Database Model
 *
 * @package Vitepos\Models\Database
 */

namespace Vitepos\Models\Database;

use VitePos\Core\ViteposModel;

/**
 * Class Mapbd_pos_warehouse
 *
 * @properties id,name,email,phone,country,state,city,street,zip_code,status
 */
class Mapbd_Post extends ViteposModel {
	/**
	 * Its property ID
	 *
	 * @var int
	 */
	public $ID;
	/**
	 * Its property post_author
	 *
	 * @var mixed
	 */
	public $post_author;
	/**
	 * Its property post_date
	 *
	 * @var string
	 */
	public $post_date;
	/**
	 * Its property post_date_gmt
	 *
	 * @var string
	 */
	public $post_date_gmt;
	/**
	 * Its property post_content
	 *
	 * @var string
	 */
	public $post_content;
	/**
	 * Its property post_title
	 *
	 * @var string
	 */
	public $post_title;
	/**
	 * Its property post_excerpt
	 *
	 * @var string
	 */
	public $post_excerpt;
	/**
	 * Its property post_status
	 *
	 * @var boolean
	 */
	public $post_status;
	/**
	 * Its property comment_status
	 *
	 * @var boolean
	 */
	public $comment_status;
	/**
	 * Its property ping_status
	 *
	 * @var boolean
	 */
	public $ping_status;
	/**
	 * Its property post_password
	 *
	 * @var string
	 */
	public $post_password;
	/**
	 * Its property post_name
	 *
	 * @var string
	 */
	public $post_name;
	/**
	 * Its property to_ping
	 *
	 * @var mixed
	 */
	public $to_ping;
	/**
	 * Its property pinged
	 *
	 * @var mixed
	 */
	public $pinged;
	/**
	 * Its property post_modified
	 *
	 * @var string
	 */
	public $post_modified;
	/**
	 * Its property post_modified_gmt
	 *
	 * @var string
	 */
	public $post_modified_gmt;
	/**
	 * Its property post_content_filtered
	 *
	 * @var mixed
	 */
	public $post_content_filtered;
	/**
	 * Its property post_parent
	 *
	 * @var mixed
	 */
	public $post_parent;
	/**
	 * Its property guid
	 *
	 * @var int
	 */
	public $guid;
	/**
	 * Its property menu_order
	 *
	 * @var  int
	 */
	public $menu_order;
	/**
	 * Its property post_type
	 *
	 * @var string
	 */
	public $post_type;
	/**
	 * Its property post_mime_type
	 *
	 * @var string
	 */
	public $post_mime_type;
	/**
	 * Its property comment_count
	 *
	 * @var int
	 */
	public $comment_count;


	/**
	 * Mapbd_pos_warehouse constructor.
	 */
	public function __construct() {
		parent::__construct();
		$this->set_validation();
		$this->table_name     = 'posts';
		$this->primary_key    = 'ID';
		$this->unique_key     = array();
		$this->multi_key      = array( array( 'post_name' ), array( 'post_type', 'post_status', 'post_date', 'ID' ), array( 'post_parent' ), array( 'post_author' ) );
		$this->auto_inc_field = array( 'ID' );
		$this->app_base_name  = 'apbd-vite-pos';

	}


	/**
	 * The set validation is generated by appsbd
	 */
	public function set_validation() {
		$this->validations = array(
			'ID'                    => array(
				'Text' => 'ID',
				'Rule' => 'max_length[20]',
			),
			'post_author'           => array(
				'Text' => 'Post Author',
				'Rule' => 'max_length[20]',
			),
			'post_content'          => array(
				'Text' => 'Post Content',
				'Rule' => 'required',
			),
			'post_title'            => array(
				'Text' => 'Post Title',
				'Rule' => 'required',
			),
			'post_excerpt'          => array(
				'Text' => 'Post Excerpt',
				'Rule' => 'required',
			),
			'post_status'           => array(
				'Text' => 'Post Status',
				'Rule' => 'max_length[20]',
			),
			'comment_status'        => array(
				'Text' => 'Comment Status',
				'Rule' => 'max_length[20]',
			),
			'ping_status'           => array(
				'Text' => 'Ping Status',
				'Rule' => 'max_length[20]',
			),
			'post_password'         => array(
				'Text' => 'Post Password',
				'Rule' => 'max_length[255]',
			),
			'post_name'             => array(
				'Text' => 'Post Name',
				'Rule' => 'max_length[200]',
			),
			'to_ping'               => array(
				'Text' => 'To Ping',
				'Rule' => 'required',
			),
			'pinged'                => array(
				'Text' => 'Pinged',
				'Rule' => 'required',
			),
			'post_content_filtered' => array(
				'Text' => 'Post Content Filtered',
				'Rule' => 'required',
			),
			'post_parent'           => array(
				'Text' => 'Post Parent',
				'Rule' => 'max_length[20]',
			),
			'guid'                  => array(
				'Text' => 'Guid',
				'Rule' => 'max_length[255]',
			),
			'menu_order'            => array(
				'Text' => 'Menu Order',
				'Rule' => 'max_length[11]|integer',
			),
			'post_type'             => array(
				'Text' => 'Post Type',
				'Rule' => 'max_length[20]',
			),
			'post_mime_type'        => array(
				'Text' => 'Post Mime Type',
				'Rule' => 'max_length[100]',
			),
			'comment_count'         => array(
				'Text' => 'Comment Count',
				'Rule' => 'max_length[20]',
			),

		);
	}


	/**
	 * The create db table is generated by appsbd
	 */
	public static function create_db_table() {
		return;
	}

	/**
	 * The drop db table is generated by appsbd
	 */
	public function drop_db_table() {
		return;
	}

	/**
	 * The DeleteById is generated by appsbd
	 *
	 * @param any $id Its integer.
	 *
	 * @return bool
	 */
	public static function delete_by_id( $id ) {
		return self::delete_by_key_value( 'id', $id );
	}
}
