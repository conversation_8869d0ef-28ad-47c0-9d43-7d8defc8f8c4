<?php
/**
 * Its used for Client Language
 *
 * @since: 21/09/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package VitePos\Libs
 */

namespace VitePos\Libs;

if ( ! class_exists( __NAMESPACE__ . '\Client_Language' ) ) {
	/**
	 * Class VitePOS_API_Base
	 *
	 * @package VitePos\Libs
	 */
	class Client_Language {
		/**
		 * The get Admin Languages is generated by appsbd
		 *
		 * @param mixed $kernel_object Its the object of kernel.
		 *
		 * @return mixed
		 */
		public static function get_admin_languages( &$kernel_object ) {
			$language                        = array();
			$language['Counter Name']        = $kernel_object->__( 'Counter Name' );
			$language['Counter No']          = $kernel_object->__( 'Counter No' );
			$language['Name']                = $kernel_object->__( 'Name' );
			$language['Email']               = $kernel_object->__( 'Email' );
			$language['Contact No']          = $kernel_object->__( 'Contact No' );
			$language['Street']              = $kernel_object->__( 'Street' );
			$language['City']                = $kernel_object->__( 'City' );
			$language['State']               = $kernel_object->__( 'State' );
			$language['State/District']      = $kernel_object->__( 'State/District' );
			$language['Zip Code']            = $kernel_object->__( 'Zip Code' );
			$language['Select Country']      = $kernel_object->__( 'Select Country' );
			$language['Time Zone']           = $kernel_object->__( 'Time Zone' );
			$language['Allowed Ip']          = $kernel_object->__( 'Allowed Ip' );
			$language['Status']              = $kernel_object->__( 'Status' );
			$language['Add Outlet']          = $kernel_object->__( 'Add Outlet' );
			$language['Edit Outlet']         = $kernel_object->__( 'Edit Outlet' );
			$language['Edit']                = $kernel_object->__( 'Edit' );
			$language['Add Counter']         = $kernel_object->__( 'Add Counter' );
			$language['Edit Counter']        = $kernel_object->__( 'Edit Counter' );
			$language['Delete']              = $kernel_object->__( 'Delete' );
			$language['Cancel']              = $kernel_object->__( 'Cancel' );
			$language['Close']               = $kernel_object->__( 'Close' );
			$language['Outlet Details']      = $kernel_object->__( 'Outlet Details' );
			$language['Outlet Name']         = $kernel_object->__( 'Outlet Name' );
			$language['Address']             = $kernel_object->__( 'Address' );
			$language['Add']                 = $kernel_object->__( 'Add' );
			$language['Update']              = $kernel_object->__( 'Update' );
			$language['Sidebar']             = $kernel_object->__( 'Sidebar' );
			$language['Dashboard']           = $kernel_object->__( 'Dashboard' );
			$language['Orders']              = $kernel_object->__( 'Orders' );
			$language['Outlet']              = $kernel_object->__( 'Outlet' );
			$language['Loading Outlet']      = $kernel_object->__( 'Loading Outlet' );
			$language['Make Main']           = $kernel_object->__( 'Make Main' );
			$language['Add Role']            = $kernel_object->__( 'Add Role' );
			$language['Loading Roles']       = $kernel_object->__( 'Loading Roles' );
			$language['Role Description']    = $kernel_object->__( 'Role Description' );
			$language['Role Name']           = $kernel_object->__( 'Role Name' );
			$language['Edit Role']           = $kernel_object->__( 'Edit Role' );
			$language['Roles']               = $kernel_object->__( 'Roles' );
			$language['Role List']           = $kernel_object->__( 'Role List' );
			$language['Role Access']         = $kernel_object->__( 'Role Access' );
			$language['Loading Role Access'] = $kernel_object->__( 'Loading Role Access' );
			$language['Action']              = $kernel_object->__( 'Action' );
			$language['Main Branch']         = $kernel_object->__( 'Main Branch' );
			$language['Counters']            = $kernel_object->__( 'Counters' );
			$language['Counter']             = $kernel_object->__( 'Counter' );
			$language['Settings']            = $kernel_object->__( 'Settings' );
			$language['Customers']           = $kernel_object->__( 'Customers' );
			$language['Home']                = $kernel_object->__( 'Home' );
			$language['Yes']                 = $kernel_object->__( 'Yes' );
			$language['No']                  = $kernel_object->__( 'No' );
			$language['Country']             = $kernel_object->__( 'Country' );
			$language['Basic Settings']      = $kernel_object->__( 'Basic Settings' );
			$language['Print Settings']      = $kernel_object->__( 'Print Settings' );
			$language['License Info']        = $kernel_object->__( 'License Info' );
			$language['Save']                = $kernel_object->__( 'Save' );
			$language['Help']                = $kernel_object->__( 'Help' );
			$language['View POS']            = $kernel_object->__( 'View POS' );
			$language['Viewing %{ startRecord } to %{ endRecord } of %{ totalRecord } records'] = $kernel_object->__( 'Viewing %{ startRecord } to %{ endRecord } of %{ totalRecord } records' );
			$language['%{ row } rows']           = $kernel_object->__( '%{ row } rows' );
			$language['No record found']         = $kernel_object->__( 'No record found' );
			$language['Version']                 = $kernel_object->__( 'Version' );
			$language['Barcode Field']           = $kernel_object->__( 'Barcode Field' );
			$language['Page Settings']           = $kernel_object->__( 'Page Settings' );
			$language['Header Panel']            = $kernel_object->__( 'Header Panel' );
			$language['Product ID']              = $kernel_object->__( 'Product ID' );
			$language['SKU']                     = $kernel_object->__( 'SKU' );
			$language['Custom Field']            = $kernel_object->__( 'Custom Field' );
			$language['Loading Settings']        = $kernel_object->__( 'Loading Settings' );
			$language['Select']                  = $kernel_object->__( 'Select' );
			$language['%{fld_name} is required'] = $kernel_object->__( '%{fld_name} is required' );

			$language['Are you sure to delete this outlet: %{outlet}?']   = $kernel_object->__( 'Are you sure to delete this outlet: %{outlet}?' );
			$language['Are you sure to delete this counter: %{counter}?'] = $kernel_object->__( 'Are you sure to delete this counter: %{counter}?' );
			$language['Are you sure to remove access from %{role}?']      = $kernel_object->__( 'Are you sure to remove access from %{role}?' );
			$language['Are you sure to give access to %{role}?']          = $kernel_object->__( 'Are you sure to give access to %{role}?' );
			$language['Are you sure to %{status}?']                       = $kernel_object->__( 'Are you sure to %{status}?' );

			$language['Active']                               = $kernel_object->__( 'Active' );
			$language['Inactive']                             = $kernel_object->__( 'Inactive' );
			$language['Page Width']                           = $kernel_object->__( 'Page Width' );
			$language['Font Size']                            = $kernel_object->__( 'Font Size' );
			$language['Show Logo']                            = $kernel_object->__( 'Show Logo' );
			$language['mm']                                   = $kernel_object->__( 'mm' );
			$language['px']                                   = $kernel_object->__( 'px' );
			$language['Recommend logo height %{logoHeight}.'] = $kernel_object->__( 'Recommend logo height %{logoHeight}.' );

			$language['Best size is %{logoWidth} in width and %{logoHeight} in height.'] = $kernel_object->__( 'Best size is %{logoWidth} in width and %{logoHeight} in height.' );
			$language['Show Header']            = $kernel_object->__( 'Show Header' );
			$language['Show Vat Reg no']        = $kernel_object->__( 'Show Vat Reg no' );
			$language['Vat/Tax No Label']       = $kernel_object->__( 'Vat/Tax No Label' );
			$language['Vat/Tax No']             = $kernel_object->__( 'Vat/Tax No' );
			$language['Outlet Info']            = $kernel_object->__( 'Outlet Info' );
			$language['Show outlet info']       = $kernel_object->__( 'Show outlet info' );
			$language['Outlet Email']           = $kernel_object->__( 'Outlet Email' );
			$language['Outlet Phone']           = $kernel_object->__( 'Outlet Phone' );
			$language['Outlet Address']         = $kernel_object->__( 'Outlet Address' );
			$language['Counter Info']           = $kernel_object->__( 'Counter Info' );
			$language['Show Counter info']      = $kernel_object->__( 'Show Counter info' );
			$language['Counter Operator Label'] = $kernel_object->__( 'Counter Operator Label' );
			$language['Show Counter No']        = $kernel_object->__( 'Show Counter No' );
			$language['Counter No Label']       = $kernel_object->__( 'Counter No Label' );
			$language['License Status']         = $kernel_object->__( 'License Status' );
			$language['License Type']           = $kernel_object->__( 'License Type' );
			$language['Support Expired on']     = $kernel_object->__( 'Support Expired on' );
			$language['License Expired on']     = $kernel_object->__( 'License Expired on' );
			$language['Your License Key']       = $kernel_object->__( 'Your License Key' );
			$language['Deactivate License']     = $kernel_object->__( 'Deactivate License' );
			$language['Valid']                  = $kernel_object->__( 'Valid' );
			$language['No expiry']              = $kernel_object->__( 'No expiry' );
			$language['Unlimited']              = $kernel_object->__( 'Unlimited' );
			$language['Show order no']          = $kernel_object->__( 'Show order no' );
			$language['Order no label']         = $kernel_object->__( 'Order no label' );
			$language['Customer Info']          = $kernel_object->__( 'Customer Info' );
			$language['Show customer info']     = $kernel_object->__( 'Show customer info' );
			$language['Customer Info Label']    = $kernel_object->__( 'Customer Info Label' );
			$language['Customer Name']          = $kernel_object->__( 'Customer Name' );
			$language['Customer Id']            = $kernel_object->__( 'Customer Id' );
			$language['Customer Id Label']      = $kernel_object->__( 'Customer Id Label' );
			$language['Customer Phone']         = $kernel_object->__( 'Customer Phone' );
			$language['Customer Phone Label']   = $kernel_object->__( 'Customer Phone Label' );
			$language['Customer Address']       = $kernel_object->__( 'Customer Address' );
			$language['Item Details']           = $kernel_object->__( 'Item Details' );
			$language['Show Item Serial']       = $kernel_object->__( 'Show Item Serial' );
			$language['Show Unit Cost']         = $kernel_object->__( 'Show Unit Cost' );
			$language['Show Discount Row']      = $kernel_object->__( 'Show Discount Row' );
			$language['Show Tax Row']           = $kernel_object->__( 'Show Tax Row' );
			$language['Show Fee Row']           = $kernel_object->__( 'Show Fee Row' );
			$language['Show Payment Method']    = $kernel_object->__( 'Show Payment Method' );
			$language['Footer Panel']           = $kernel_object->__( 'Footer Panel' );
			$language['Show Footer']            = $kernel_object->__( 'Show Footer' );
			$language['Date']                   = $kernel_object->__( 'Date' );
			$language['SL']                     = $kernel_object->__( 'SL' );
			$language['Item']                   = $kernel_object->__( 'Item' );
			$language['Qty:']                   = $kernel_object->__( 'Qty:' );
			$language['Total']                  = $kernel_object->__( 'Total' );
			$language['Tax']                    = $kernel_object->__( 'Tax' );
			$language['Discount']               = $kernel_object->__( 'Discount' );
			$language['Fee']                    = $kernel_object->__( 'Fee' );
			$language['Order Total']            = $kernel_object->__( 'Order Total' );
			$language['Given Amount']           = $kernel_object->__( 'Given Amount' );
			$language['Return']                 = $kernel_object->__( 'Return' );
			$language['Payment Method']         = $kernel_object->__( 'Payment Method' );
			$language['Order No']               = $kernel_object->__( 'Order No' );
			$language['Processed By']           = $kernel_object->__( 'Processed By' );
			$language['Basic Help']             = $kernel_object->__( 'Basic Help' );
			$language['Videos']                 = $kernel_object->__( 'Videos' );
			$language['About VitePos']          = $kernel_object->__( 'About VitePos' );
			$language['Contact Author']         = $kernel_object->__( 'Contact Author' );
			$language['Offline Order']          = $kernel_object->__( 'Offline Order' );
			$language['Enable']                 = $kernel_object->__( 'Enable' );
			$language['Disable']                = $kernel_object->__( 'Disable' );
			$language['POS Login Type']         = $kernel_object->__( 'POS Login Type' );
			$language['Vitepos Login']          = $kernel_object->__( 'Vitepos Login' );
			$language['Wordpress Login']        = $kernel_object->__( 'WordPress Login' );
			$language['Wordpress Login URL']    = $kernel_object->__( 'WordPress Login URL' );
			$language['POS Color']              = $kernel_object->__( 'POS Color' );
			$language['POS Link Type']          = $kernel_object->__( 'POS Link Type' );
			$language['Default']                = $kernel_object->__( 'Default' );
			$language['Page']                   = $kernel_object->__( 'Page' );
			$language['POS Link']               = $kernel_object->__( 'POS Link' );
			$language['POS Page']               = $kernel_object->__( 'POS Page' );
			$language['Video Tutorial']         = $kernel_object->__( 'Video Tutorial' );
			$language['More Details']           = $kernel_object->__( 'More Details' );
			$language['Search']                 = $kernel_object->__( 'Search' );
			$language['Search/Choose Page']     = $kernel_object->__( 'Search/Choose Page' );
			$language['Keep blank to use default wordpress login.']          = $kernel_object->__( 'Keep blank to use default WordPress login.' );
			$language['To watch our all video tutorial click on the button'] = $kernel_object->__( 'To watch our all video tutorial click on the button' );
			$language['In case of any problem, get in touch with the Vitepos team. We always support our clients until their satisfaction comes. And that is our responsibility and duty.'] = $kernel_object->__( 'In case of any problem, get in touch with the Vitepos team. We always support our clients until their satisfaction comes. And that is our responsibility and duty.' );
			$language['Contact Support Team'] = $kernel_object->__( 'Contact Support Team' );
			$language['To delete role, you need to move current users of this roles to another role. Please choose move to role below'] = $kernel_object->__( 'To delete role, you need to move current users of this roles to another role. Please choose move to role below' );
			$language['User Move to']                           = $kernel_object->__( 'User Move to' );
			$language['Delete Role']                            = $kernel_object->__( 'Delete Role' );
			$language['Delete Role Details']                    = $kernel_object->__( 'Delete Role Details' );
			$language['Updating Role Details']                  = $kernel_object->__( 'Updating Role Details' );
			$language['Adding Role']                            = $kernel_object->__( 'Adding Role' );
			$language['Move to Role ']                          = $kernel_object->__( 'Move to Role ' );
			$language['Loading Outlet Details']                 = $kernel_object->__( 'Loading Outlet Details' );
			$language['Reset']                                  = $kernel_object->__( 'Reset' );
			$language['Are you sure to make this main branch?'] = $kernel_object->__( 'Are you sure to make this main branch?' );
			$language['Search/Select Country']                  = $kernel_object->__( 'Search/Select Country' );
			$language['Search/Choose Time Zone']                = $kernel_object->__( 'Search/Choose Time Zone' );
			$language['Invoice Print Settings']                 = $kernel_object->__( 'Invoice Print Settings' );
			$language['POS Logo']                               = $kernel_object->__( 'POS Logo' );
			$language['Logo']                                   = $kernel_object->__( 'Logo' );
			$language['Favicon']                                = $kernel_object->__( 'Favicon' );
			$language['Click the box to select or remove %{fileName}.']  = $kernel_object->__( 'Click the box to select or remove %{fileName}.' );
			$language['Best size is 166px in width and 60px in height.'] = $kernel_object->__( 'Best size is 166px in width and 60px in height.' );
			$language['Build :']                       = $kernel_object->__( 'Build :' );
			$language['Visit']                         = $kernel_object->__( 'Visit' );
			$language['for more details please visit'] = $kernel_object->__( 'for more details please visit' );
			$language['Click here']                    = $kernel_object->__( 'Click here' );
			$language['To get a complete idea of what your agent needs to do to run an outlet ']                                 = $kernel_object->__( 'To get a complete idea of what your agent needs to do to run an outlet ' );
			$language['On the other hand, on the client side, you can set the outlet agents you want, according to their role.'] = $kernel_object->__( 'On the other hand, on the client side, you can set the outlet agents you want, according to their role.' );
			$language['have two sides. One admin panel and another is client panel.'] = $kernel_object->__( 'have two sides. One admin panel and another is client panel.' );
			$language['The admin can add some settings in the admin panel, such as: Outlet Create, Counter Create, Roll Access Management, Custom Invoice, Barcode Settings and License Panel.'] = $kernel_object->__( 'The admin can add some settings in the admin panel, such as: Outlet Create, Counter Create, Roll Access Management, Custom Invoice, Barcode Settings and License Panel.' );
			$language['Welcome to'] = $kernel_object->__( 'Welcome to' );
			$language['Warning, offline orders are stored in the database of browser (Chrome, Firefox, Opera, etc.) . And as soon as the app comes online, it syncs with the main server. If someone deletes the database  of browser before syncing, the order will no longer sync. In this case, the order information will be deleted forever.'] = $kernel_object->__( 'Warning, offline orders are stored in the database of browser (Chrome, Firefox, Opera, etc.) . And as soon as the app comes online, it syncs with the main server. If someone deletes the database  of browser before syncing, the order will no longer sync. In this case, the order information will be deleted forever.' );
			$language['Empowering Your WooCommerce Store with Vitepos'] = $kernel_object->__( 'Empowering Your WooCommerce Store with Vitepos' );
			$language['Total Income']                                   = $kernel_object->__( 'Total Income' );
			$language['Total Orders']                                   = $kernel_object->__( 'Total Orders' );
			$language['Total Outlets']                                  = $kernel_object->__( 'Total Outlets' );
			$language['Total Amount']                                   = $kernel_object->__( 'Total Amount' );
			$language['Based on outlet order(s)']                       = $kernel_object->__( 'Based on outlet order(s)' );
			$language['Custom Barcode']                                 = $kernel_object->__( 'Custom Barcode' );
			$language['Enable reCaptcha V3']                            = $kernel_object->__( 'Enable reCaptcha V3' );
			$language['Site Key']                                       = $kernel_object->__( 'Site Key' );
			$language['Secret Key']                                     = $kernel_object->__( 'Secret Key' );
						$language['Can create & manage unlimited outlets']                 = $kernel_object->__( 'Can create & manage unlimited outlets' );
			$language['Can create & manage unlimited counter for each outlet'] = $kernel_object->__( 'Can create & manage unlimited counter for each outlet' );
			$language['Split payment support in pro version']                  = $kernel_object->__( 'Split payment support in pro version' );
			$language['Offline sale allowed in pro version']                   = $kernel_object->__( 'Offline sale allowed in pro version' );
			$language['Hold cart applicable in pro version']                   = $kernel_object->__( 'Hold cart applicable in pro version' );
			$language['Customization of color support in pro version']         = $kernel_object->__( 'Customization of color support in pro version' );
			$language['Easy product manage (add,update,delete) based on role'] = $kernel_object->__( 'Easy product manage (add,update,delete) based on role' );
			$language['Premium Support']                                       = $kernel_object->__( 'Premium Support' );
			$language['And More..']           = $kernel_object->__( 'And More..' );
			$language['Pro version required'] = $kernel_object->__( 'Pro version required' );
			$language['Go pro']               = $kernel_object->__( 'Go pro' );
			$language['For add more than two counter it requires pro version.']  = $kernel_object->__( 'For add more than two counter it requires pro version.' );
			$language['For multiple outlet you need to active pro version.']     = $kernel_object->__( 'For multiple outlet you need to active pro version.' );
			$language['Offline feature support in pro version only.']            = $kernel_object->__( 'Offline feature support in pro version only.' );
			$language['To change color you need pro version']                    = $kernel_object->__( 'To change color you need pro version' );
			$language['Warning, all role access will be deleted for this role.'] = $kernel_object->__( 'Warning, all role access will be deleted for this role.' );
			$language['Reset Role']             = $kernel_object->__( 'Reset Role' );
			$language['Select a Role to Reset'] = $kernel_object->__( 'Select a Role to Reset' );
			$language['Copy Role Permission']   = $kernel_object->__( 'Copy Role Permission' );
			$language['Copy role']              = $kernel_object->__( 'Copy role' );
			$language['Copy from']              = $kernel_object->__( 'Copy from' );
			$language['Copy to']                = $kernel_object->__( 'Copy to' );
			$language['The role user can create any outlets user if get this access'] = $kernel_object->__( 'The role user can create any outlets user if get this access' );
			$language['The role user can change any users password']                  = $kernel_object->__( 'The role user can change any users password' );
			$language['This roles user can change their password']                    = $kernel_object->__( 'This roles user can change their password' );
			$language['Administrator']      = $kernel_object->__( 'Administrator' );
			$language['Outlet Manager']     = $kernel_object->__( 'Outlet Manager' );
			$language['Cashier']            = $kernel_object->__( 'Cashier' );
			$language['Built-in']           = $kernel_object->__( 'Built-in' );
			$language['outlet']             = $kernel_object->__( 'outlet' );
			$language['No %{type} found']   = $kernel_object->__( 'No %{type} found' );
			$language['Outlet User List']   = $kernel_object->__( 'Outlet User List' );
			$language['Add to this outlet'] = $kernel_object->__( 'Add to this outlet' );
			$language['Users']              = $kernel_object->__( 'Users' );
			$language['Getting user list']  = $kernel_object->__( 'Getting user list' );
			$language['Remove']             = $kernel_object->__( 'Remove' );
			$language['Set the duration in days for new badge on product'] = $kernel_object->__( 'Set the duration in days for new badge on product' );
			$language['days'] = $kernel_object->__( 'days' );
			$language['Set the duration in days for new badge on product'] = $kernel_object->__( 'Set the duration in days for new badge on product' );
			$language['New Product Badge Duration']                        = $kernel_object->__( 'New Product Badge Duration' );
			$language['The new badge will display up to %{dayset} days from product creation date.'] = $kernel_object->__( 'The new badge will display up to %{dayset} days from product creation date.' );
			$language['Phone']                        = $kernel_object->__( 'Phone' );
			$language['Cash']                         = $kernel_object->__( 'Cash' );
			$language['Loading Data']                 = $kernel_object->__( 'Loading Data' );
			$language['Select/Search Role']           = $kernel_object->__( 'Select/Search Role' );
			$language['Select/Search role copy from'] = $kernel_object->__( 'Select/Search role copy from' );
			$language['Select/Search role copy to']   = $kernel_object->__( 'Select/Search role copy to' );
						$language['POS Products Per Row']        = $kernel_object->__( 'POS Products Per Row' );
			$language['Default Customer (Optional)'] = $kernel_object->__( 'Default Customer (Optional)' );
			$language['Search/Choose customer']      = $kernel_object->__( 'Search/Choose customer' );
			$language['You can select the default customer for order processing. If not selected then the order will be processed as a guest user. Note that, if the customer is selected from the POS, that customer will remain in the selected state.'] = $kernel_object->__( 'You can select the default customer for order processing. If not selected then the order will be processed as a guest user. Note that, if the customer is selected from the POS, that customer will remain in the selected state.' );
			$language['%{col} Products Per Row'] = $kernel_object->__( '%{col} Products Per Row' );
			$language['Max Discount']            = $kernel_object->__( 'Max Discount' );
			$language['Refresh App']             = $kernel_object->__( 'Refresh App' );
			$language['POS Link:']               = $kernel_object->__( 'POS Link:' );
			$language['Generated by']            = $kernel_object->__( 'Generated by' );
			$language['Warning, vitepos lock screen might not work properly if 2FA is enabled on login.'] = $kernel_object->__( 'Warning, vitepos lock screen might not work properly if 2FA is enabled on login.' );
			$language['Branding'] = $kernel_object->__( 'Branding' );
						$language['Select/Search Timezone']                    = $kernel_object->__( 'Select/Search Timezone' );
			$language['Select/Search Country']                     = $kernel_object->__( 'Select/Search Country' );
			$language['Select/Search State or Dist']               = $kernel_object->__( 'Select/Search State or Dist' );
			$language['Add Field']                                 = $kernel_object->__( 'Add Field' );
			$language['Label']                                     = $kernel_object->__( 'Label' );
			$language['Field label']                               = $kernel_object->__( 'Field label' );
			$language['Type']                                      = $kernel_object->__( 'Type' );
			$language['Select Type']                               = $kernel_object->__( 'Select Type' );
			$language['Options']                                   = $kernel_object->__( 'Options' );
			$language['Add Option']                                = $kernel_object->__( 'Add Option' );
			$language['Title']                                     = $kernel_object->__( 'Title' );
			$language['Value']                                     = $kernel_object->__( 'Value' );
			$language['No option added']                           = $kernel_object->__( 'No option added' );
			$language['Help text']                                 = $kernel_object->__( 'Help text' );
			$language['Where will show']                           = $kernel_object->__( 'Where will show' );
			$language['Select Module']                             = $kernel_object->__( 'Select Module' );
			$language['Is Calculable']                             = $kernel_object->__( 'Is Calculable' );
			$language['Chose Operator']                            = $kernel_object->__( 'Chose Operator' );
			$language['Half Field']                                = $kernel_object->__( 'Half Field' );
			$language['Is Required']                               = $kernel_object->__( 'Is Required' );
			$language['Add Custom Field']                          = $kernel_object->__( 'Add Custom Field' );
			$language['Are you sure to delete this custom field?'] = $kernel_object->__( 'Are you sure to delete this custom field?' );
			$language['Loading Custom Field Details']              = $kernel_object->__( 'Loading Custom Field Details' );
			$language['Saving Custom Field']                       = $kernel_object->__( 'Saving Custom Field' );
			$language['Updating Custom Fields']                    = $kernel_object->__( 'Updating Custom Fields' );
			$language['Module']                                    = $kernel_object->__( 'Module' );
			$language['Customer']                                  = $kernel_object->__( 'Customer' );
			$language['User']                                      = $kernel_object->__( 'User' );
			$language['Invoice']                                   = $kernel_object->__( 'Invoice' );
			$language['Loading Custom Fields...']                  = $kernel_object->__( 'Loading Custom Fields...' );
			$language['Place to show']                             = $kernel_object->__( 'Place to show' );
			$language['custom field']                              = $kernel_object->__( 'custom field' );
			$language['Custom Fields']                             = $kernel_object->__( 'Custom Fields' );
			$language['Messages']                                  = $kernel_object->__( 'Messages' );
			$language['Push Settings']                             = $kernel_object->__( 'Push Settings' );
			$language['Stock Settings']                            = $kernel_object->__( 'Stock Settings' );
			$language['Payment']                                   = $kernel_object->__( 'Payment' );
			$language['Custom Field Settings']                     = $kernel_object->__( 'Custom Field Settings' );
			$language['Shortcut Message Settings']                 = $kernel_object->__( 'Shortcut Message Settings' );
			$language['Payment Settings']                          = $kernel_object->__( 'Payment Settings' );
			$language['Shortcut Messages']                         = $kernel_object->__( 'Shortcut Messages' );
			$language['Deny Reason']                               = $kernel_object->__( 'Deny Reason' );
			$language['Add Shortcut Message']                      = $kernel_object->__( 'Add Shortcut Message' );
			$language['Add Deny Reason']                           = $kernel_object->__( 'Add Deny Reason' );
			$language['Edit Deny Reason']                          = $kernel_object->__( 'Edit Deny Reason' );
			$language['Message Type']                              = $kernel_object->__( 'Message Type' );
			$language['Message']                                   = $kernel_object->__( 'Message' );
			$language['Write your message']                        = $kernel_object->__( 'Write your message' );
			$language['Deny Message']                              = $kernel_object->__( 'Deny Message' );
			$language['Shortcut Message']                          = $kernel_object->__( 'Shortcut Message' );
			$language['App ID']                                    = $kernel_object->__( 'App ID' );
			$language['Key']                                       = $kernel_object->__( 'Key' );
			$language['Secret']                                    = $kernel_object->__( 'Secret' );
			$language['Cluster']                                   = $kernel_object->__( 'Cluster' );
			$language['Stripe Settings']                           = $kernel_object->__( 'Stripe Settings' );
			$language['Authorize Settings']                        = $kernel_object->__( 'Authorize Settings' );
			$language['Enable Swipe Payment']                      = $kernel_object->__( 'Enable Swipe Payment' );
			$language['Enable Other Method']                       = $kernel_object->__( 'Enable Other Method' );
			$language['Enable %{brand} Payment']                   = $kernel_object->__( 'Enable %{brand} Payment' );
			$language['It will allows to take payment using swipe machine.'] = $kernel_object->__( 'It will allows to take payment using swipe machine.' );
			$language['It will allows to take payment using others method.'] = $kernel_object->__( 'It will allows to take payment using others method.' );
			$language['It will allow you to take payment using %{brand} Payment gateway, make sure you have added %{brand} api info in %{brand} settings tab.'] = $kernel_object->__( 'It will allow you to take payment using %{brand} Payment gateway, make sure you have added %{brand} api info in %{brand} settings tab.' );
			$language['[Coming Soon]']           = $kernel_object->__( '[Coming Soon]' );
			$language['Stripe']                  = $kernel_object->__( 'Stripe' );
			$language['Publishable key']         = $kernel_object->__( 'Publishable key' );
			$language['POS Mode']                = $kernel_object->__( 'POS Mode' );
			$language['Grocery']                 = $kernel_object->__( 'Grocery' );
			$language['Restaurant']              = $kernel_object->__( 'Restaurant' );
			$language['Restaurant Payment Type'] = $kernel_object->__( 'Restaurant Payment Type' );
			$language['Traditional']             = $kernel_object->__( 'Traditional' );
			$language['Pay First']               = $kernel_object->__( 'Pay First' );
			$language['Product Status']          = $kernel_object->__( 'Product Status' );
			$language['Published']               = $kernel_object->__( 'Published' );
			$language['Private']                 = $kernel_object->__( 'Private' );
			$language['Vitepos Only']            = $kernel_object->__( 'Vitepos Only' );

						$language['The stock of this selected outlet product will serve as the inventory for your online orders'] = $kernel_object->__( 'The stock of this selected outlet product will serve as the inventory for your online orders' );
			$language['Are you sure to make this inactive?']       = $kernel_object->__( 'Are you sure to make this inactive?' );
			$language['Are you sure to make this active??']        = $kernel_object->__( 'Are you sure to make this active??' );
			$language['Make Inactive']                             = $kernel_object->__( 'Make Inactive' );
			$language['Make Active']                               = $kernel_object->__( 'Make Active' );
			$language['Loading Messages...']                       = $kernel_object->__( 'Loading Messages...' );
			$language['Loading Message Details...']                = $kernel_object->__( 'Loading Message Details...' );
			$language['Are you sure to delete this message?']      = $kernel_object->__( 'Are you sure to delete this message?' );
			$language['Edit Message']                              = $kernel_object->__( 'Edit Message' );
			$language['Add Message']                               = $kernel_object->__( 'Add Message' );
			$language['Choose Panel']                              = $kernel_object->__( 'Choose Panel' );
			$language['All']                                       = $kernel_object->__( 'All' );
			$language['Cashier Panel']                             = $kernel_object->__( 'Cashier Panel' );
			$language['Kitchen Panel']                             = $kernel_object->__( 'Kitchen Panel' );
			$language['Waiter Panel']                              = $kernel_object->__( 'Waiter Panel' );
			$language['Loading Deny Reasons...']                   = $kernel_object->__( 'Loading Deny Reasons...' );
			$language['Updating message...']                       = $kernel_object->__( 'Updating message...' );
			$language['Saving message...']                         = $kernel_object->__( 'Saving message...' );
			$language['Are you sure to delete this deny message?'] = $kernel_object->__( 'Are you sure to delete this deny message?' );
			$language['Are you sure to enable %{name}?']           = $kernel_object->__( 'Are you sure to enable %{name}?' );
			$language['Are you sure to disable %{name}?']          = $kernel_object->__( 'Are you sure to disable %{name}?' );
			$language['Soon, we will be adding more options such as Stripe Terminal and Authorize.net to our platform.Thank you for your understand.'] = $kernel_object->__( 'Soon, we will be adding more options such as Stripe Terminal and Authorize.net to our platform.Thank you for your understand.' );
			$language['Capture Method']                                   = $kernel_object->__( 'Capture Method' );
			$language['Capture on order complete']                        = $kernel_object->__( 'Capture on order complete' );
			$language['Auto capture on payment auth']                     = $kernel_object->__( 'Auto capture on payment auth' );
			$language['To get pusher server key follow this instruction'] = $kernel_object->__( 'To get pusher server key follow this instruction' );
			$language['Login to pusher.com']                              = $kernel_object->__( 'Login to pusher.com' );
			$language['Channels']                                    = $kernel_object->__( 'Channels' );
			$language['Then select the menu App Keys']               = $kernel_object->__( 'Then select the menu App Keys' );
			$language['Create a channel or manage existing channel'] = $kernel_object->__( 'Create a channel or manage existing channel' );
			$language['In the traditional procedure, a waiter takes the customers order and sends it to the kitchen. Once the kitchen has prepared the order, the waiter is notified to serve it. After the order has been served, the cashier can process the payment.'] = $kernel_object->__( 'In the traditional procedure, a waiter takes the customers order and sends it to the kitchen. Once the kitchen has prepared the order, the waiter is notified to serve it. After the order has been served, the cashier can process the payment.' );
			$language['Pay first procedure, customers are required to pay for their meal upfront at a designated location, typically at the cashiers counter, before they are seated or served. After paying, the customer is given a receipt or a token, which they can then present to the server to receive their food.'] = $kernel_object->__( 'Pay first procedure, customers are required to pay for their meal upfront at a designated location, typically at the cashiers counter, before they are seated or served. After paying, the customer is given a receipt or a token, which they can then present to the server to receive their food.' );
			$language['Enabling the toggle button, below can incorporate the kitchen procedure, allowing the order to be completed by the chef rather than by the cashier. Additionally, the order status can be displayed on a large screen for easy tracking.'] = $kernel_object->__( 'Enabling the toggle button, below can incorporate the kitchen procedure, allowing the order to be completed by the chef rather than by the cashier. Additionally, the order status can be displayed on a large screen for easy tracking.' );
			$language['Kitchen Involvement'] = $kernel_object->__( 'Kitchen Involvement' );
			$language['Allowing the order to be completed by the chef rather than by the cashier.'] = $kernel_object->__( 'Allowing the order to be completed by the chef rather than by the cashier.' );
			$language['Enabling the Kitchen Involvement, It does not support offline order.']       = $kernel_object->__( 'Enabling the Kitchen Involvement, It does not support offline order.' );
			$language['Calculate tax after discounts and fees']                                     = $kernel_object->__( 'Calculate tax after discounts and fees' );
			$language['Calculate tax before discounts and fees']                                    = $kernel_object->__( 'Calculate tax before discounts and fees' );
			$language['Tax calculation is based on the subtotal of the purchase. Discounts and fees will be added after the tax calculation.']       = $kernel_object->__( 'Tax calculation is based on the subtotal of the purchase. Discounts and fees will be added after the tax calculation.' );
			$language['First, It calculate the subtotal including any discounts and fees, and then it apply the tax based on the discounted price.'] = $kernel_object->__( 'First, It calculate the subtotal including any discounts and fees, and then it apply the tax based on the discounted price.' );
			$language['Send Email To Customer'] = $kernel_object->__( 'Send Email To Customer' );
			$language['Enabling this feature, will trigger an email to be sent to the customer once their order is complete.'] = $kernel_object->__( 'Enabling this feature, will trigger an email to be sent to the customer once their order is complete.' );
			$language['Is Separate Tax?']              = $kernel_object->__( 'Is Separate Tax?' );
			$language['Order Info']                    = $kernel_object->__( 'Order Info' );
			$language['Show Waiter Info']              = $kernel_object->__( 'Show Waiter Info' );
			$language['Show Order Status']             = $kernel_object->__( 'Show Order Status' );
			$language['Show Order Type']               = $kernel_object->__( 'Show Order Type' );
			$language['Show Table Info']               = $kernel_object->__( 'Show Table Info' );
			$language['Enable full stock management']  = $kernel_object->__( 'Enable full stock management' );
			$language['Online stock link with outlet'] = $kernel_object->__( 'Online stock link with outlet' );
			$language['Online Stock Outlet']           = $kernel_object->__( 'Online Stock Outlet' );
			$language['It will protect order if there is not stock of the item']                                       = $kernel_object->__( 'It will protect order if there is not stock of the item' );
			$language['The stock of this selected outlet product will serve as the inventory for your online orders.'] = $kernel_object->__( 'The stock of this selected outlet product will serve as the inventory for your online orders.' );
			$language['Transfer current online stocks to'] = $kernel_object->__( 'Transfer current online stocks to' );
			$language['If you have previously used an older version or if you have not enabled the stockable feature before, you may need to transfer your online inventory to your linked outlet to %{outletname}'] = $kernel_object->__( 'If you have previously used an older version or if you have not enabled the stockable feature before, you may need to transfer your online inventory to your linked outlet to %{outletname}' );
			$language['If you want to transfer your current online stock to %{outletname} then click the button below'] = $kernel_object->__( 'If you want to transfer your current online stock to %{outletname} then click the button below' );
			$language['Keep in mind that this procedure will reset the stock of the WooCommerce product to zero.']      = $kernel_object->__( 'Keep in mind that this procedure will reset the stock of the WooCommerce product to zero.' );
			$language['Transfer to %{outletname}']                            = $kernel_object->__( 'Transfer to %{outletname}' );
			$language['Are you sure to transfer online stock to  %{outlet}?'] = $kernel_object->__( 'Are you sure to transfer online stock to  %{outlet}?' );
			$language['Transfer']                                        = $kernel_object->__( 'Transfer' );
			$language['Recommend logo height 60px.']                     = $kernel_object->__( 'Recommend logo height 60px.' );
			$language['Best size is 100px in width and 60px in height.'] = $kernel_object->__( 'Best size is 100px in width and 60px in height.' );

						$language['Waiter']      = $kernel_object->__( 'Waiter' );
			$language['Chef']        = $kernel_object->__( 'Chef' );
			$language['Loading ...'] = $kernel_object->__( 'Loading ...' );
			$language['Are you sure to remove this user from this outlet ?'] = $kernel_object->__( 'Are you sure to remove this user from this outlet ?' );
			$language['Are you sure to remove it?']                          = $kernel_object->__( 'Are you sure to remove it?' );
			$language['Now each outlet will have its own separate stock in Vitepos. For online sales on your website, the system will use the default stock from WooCommerce. To manage online sales stock, simply add stock from the WooCommerce admin panel. Vitepos purchase or add stock will not work for online stock.'] = $kernel_object->__( 'Now each outlet will have its own separate stock in Vitepos. For online sales on your website, the system will use the default stock from WooCommerce. To manage online sales stock, simply add stock from the WooCommerce admin panel. Vitepos purchase or add stock will not work for online stock.' );
			$language['Alternatively, you can be link your online sales to a specific outlet, and the system will use the stock from the linked outlet for online sales.'] = $kernel_object->__( 'Alternatively, you can be link your online sales to a specific outlet, and the system will use the stock from the linked outlet for online sales.' );
			$language['Stock Type']                       = $kernel_object->__( 'Stock Type' );
			$language['Woocommerce stock (Single stock)'] = $kernel_object->__( 'Woocommerce stock (Single stock)' );
			$language['Vitepos will use the WooCommerce stock, ensuring that a single stock is used both on the website and in the POS system.'] = $kernel_object->__( 'Vitepos will use the WooCommerce stock, ensuring that a single stock is used both on the website and in the POS system.' );
			$language['Outlet wise stock (Multi stocks)'] = $kernel_object->__( 'Outlet wise stock (Multi stocks)' );
			$language['You will get separate stock for each outlet, stock transfer, receive feature will be available in the vitepos.You need to purchase or add stock product for each outlet'] = $kernel_object->__( 'You will get separate stock for each outlet, stock transfer, receive feature will be available in the vitepos.You need to purchase or add stock product for each outlet' );
			$language['Tax Calculation Method'] = $kernel_object->__( 'Tax Calculation Method' );
			$language['Show Order Barcode']     = $kernel_object->__( 'Show Order Barcode' );
			$language['Qrcode']                 = $kernel_object->__( 'Qrcode' );
			$language['Barcode']                = $kernel_object->__( 'Barcode' );
			$language['Footer']                 = $kernel_object->__( 'Footer' );
			$language['Header']                 = $kernel_object->__( 'Header' );
			$language['Sale Tax']               = $kernel_object->__( 'Sale Tax' );
			$language['Number']                 = $kernel_object->__( 'Number' );
			$language['Code Type']              = $kernel_object->__( 'Code Type' );
			$language['Barcode Position']       = $kernel_object->__( 'Barcode Position' );

			return $language;
		}

		/**
		 * The get pos languages is generated by appsbd
		 *
		 * @param mixed $kernel_object Its the object of kernel.
		 *
		 * @return array
		 */
		public static function get_pos_languages( &$kernel_object ) {
			$language                             = array();
			$language['Download PDF']             = $kernel_object->__( 'Download PDF' );
			$language['Loaded']                   = $kernel_object->__( 'Loaded' );
			$language['Category Loading']         = $kernel_object->__( 'Category Loading' );
			$language['Product Loading']          = $kernel_object->__( 'Product Loading' );
			$language['Scan']                     = $kernel_object->__( 'Scan' );
			$language['Product']                  = $kernel_object->__( 'Product' );
			$language['Search Product']           = $kernel_object->__( 'Search Product' );
			$language['Are you sure clear cart?'] = $kernel_object->__( 'Are you sure clear cart?' );
			$language['Are you sure?']            = $kernel_object->__( 'Are you sure?' );
			$language['Are you sure to remove item from cart?'] = $kernel_object->__( 'Are you sure to remove item from cart?' );
			$language['Yes']                                    = $kernel_object->__( 'Yes' );
			$language['No']                                     = $kernel_object->__( 'No' );
			$language['Checkout']                               = $kernel_object->__( 'Checkout' );
			$language['Watch tutorial']                         = $kernel_object->__( 'Watch tutorial' );
			$language['Return']                                 = $kernel_object->__( 'Return' );
			$language['Pay']                                    = $kernel_object->__( 'Pay' );
			$language['Add Discount']                           = $kernel_object->__( 'Add Discount' );
			$language['Discount']                               = $kernel_object->__( 'Discount' );
			$language['Given Amount']                           = $kernel_object->__( 'Given Amount' );
			$language['Payment Method']                         = $kernel_object->__( 'Payment Method' );
			$language['Add Fee']                                = $kernel_object->__( 'Add Fee' );
			$language['Fee']                                    = $kernel_object->__( 'Fee' );
			$language['Add Note']                               = $kernel_object->__( 'Add Note' );
			$language['Note']                                   = $kernel_object->__( 'Note' );
			$language['Note:']                                  = $kernel_object->__( 'Note:' );
			$language['Hold']                                   = $kernel_object->__( 'Hold' );
			$language['Tax']                                    = $kernel_object->__( 'Tax' );
			$language['Total']                                  = $kernel_object->__( 'Total' );
			$language['Order Total']                            = $kernel_object->__( 'Order Total' );
			$language['Calculator']                             = $kernel_object->__( 'Calculator' );
			$language['Go Back']                                = $kernel_object->__( 'Go Back' );
			$language['Empty']                                  = $kernel_object->__( 'Empty' );
			$language['Clear']                                  = $kernel_object->__( 'Clear' );
			$language['SL']                                     = $kernel_object->__( 'SL' );
			$language['Order # %{ordNumber}']                   = $kernel_object->__( 'Order # %{ordNumber}' );
			$language['Enter amount']                           = $kernel_object->__( 'Enter amount' );
			$language['Remove from cart?']                      = $kernel_object->__( 'Remove from cart?' );
			$language['Note :']                                 = $kernel_object->__( 'Note :' );
			$language['Pay Now']                                = $kernel_object->__( 'Pay Now' );
			$language['All Category']                           = $kernel_object->__( 'All Category' );
			$language['Select Variations']                      = $kernel_object->__( 'Select Variations' );
			$language['Scan barcode...']                        = $kernel_object->__( 'Scan barcode...' );
			$language['Search products...']                     = $kernel_object->__( 'Search products...' );
			$language['Add Product']                            = $kernel_object->__( 'Add Product' );
			$language['Add Variations']                         = $kernel_object->__( 'Add Variations' );
			$language['Add Attributes']                         = $kernel_object->__( 'Add Attributes' );
			$language['Item Name']                              = $kernel_object->__( 'Item Name' );
			$language['Select Category']                        = $kernel_object->__( 'Select Category' );
			$language['Up Sells']                               = $kernel_object->__( 'Up Sells' );
			$language['Product Unit']                           = $kernel_object->__( 'Product Unit' );
			$language['Cross Sells']                            = $kernel_object->__( 'Cross Sells' );
			$language['Purchase Price']                         = $kernel_object->__( 'Purchase Price' );
			$language['Regular Price']                          = $kernel_object->__( 'Regular Price' );
			$language['Sale Price']                             = $kernel_object->__( 'Sale Price' );
			$language['Manage Stock']                           = $kernel_object->__( 'Manage Stock' );
			$language['Manage Product']                         = $kernel_object->__( 'Manage Product' );
			$language['Stock Quantity']                         = $kernel_object->__( 'Stock Quantity' );
			$language['Stock Alert']                            = $kernel_object->__( 'Stock Alert' );
			$language['Description']                            = $kernel_object->__( 'Description' );
			$language['Feature Image']                          = $kernel_object->__( 'Feature Image' );
			$language['Image']                                  = $kernel_object->__( 'Image' );
			$language['SKU']                                    = $kernel_object->__( 'SKU' );
			$language['Bar Code']                               = $kernel_object->__( 'Bar Code' );
			$language['Slug']                                   = $kernel_object->__( 'Slug' );
			$language['Select Options']                         = $kernel_object->__( 'Select Options' );
			$language['Add Options']                            = $kernel_object->__( 'Add Options' );
			$language['Name']                                   = $kernel_object->__( 'Name' );
			$language['Options']                                = $kernel_object->__( 'Options' );
			$language['Add Variation']                          = $kernel_object->__( 'Add Variation' );
			$language['Use For Variation']                      = $kernel_object->__( 'Use For Variation' );
			$language['Close']                                  = $kernel_object->__( 'Close' );
			$language['Save']                                   = $kernel_object->__( 'Save' );
			$language['Add']                                    = $kernel_object->__( 'Add' );
			$language['Add to cart']                            = $kernel_object->__( 'Add to cart' );
			$language['Manage Customer']                        = $kernel_object->__( 'Manage Customer' );
			$language['Add Customer']                           = $kernel_object->__( 'Add Customer' );
			$language['Edit Customer']                          = $kernel_object->__( 'Edit Customer' );
			$language['First Name']                             = $kernel_object->__( 'First Name' );
			$language['Last Name']                              = $kernel_object->__( 'Last Name' );
			$language['Email']                                  = $kernel_object->__( 'Email' );
			$language['Username']                               = $kernel_object->__( 'Username' );
			$language['Mobile']                                 = $kernel_object->__( 'Mobile' );
			$language['Street Address']                         = $kernel_object->__( 'Street Address' );
			$language['City']                                   = $kernel_object->__( 'City' );
			$language['Post Code']                              = $kernel_object->__( 'Post Code' );
			$language['State']                                  = $kernel_object->__( 'State' );
			$language['Country']                                = $kernel_object->__( 'Country' );
			$language['Create Purchase']                        = $kernel_object->__( 'Create Purchase' );
			$language['Select Supplier']                        = $kernel_object->__( 'Select Supplier' );
			$language['Select Outlet']                          = $kernel_object->__( 'Select Outlet' );
			$language['Select/Search Product']                  = $kernel_object->__( 'Select/Search Product' );
			$language['Orders Item*']                           = $kernel_object->__( 'Orders Item*' );
			$language['Net Unit Cost']                          = $kernel_object->__( 'Net Unit Cost' );
			$language['Stock']                                  = $kernel_object->__( 'Stock' );
			$language['Quantity']                               = $kernel_object->__( 'Quantity' );
			$language['Order Tax']                              = $kernel_object->__( 'Order Tax' );
			$language['Shipping Cost']                          = $kernel_object->__( 'Shipping Cost' );
			$language['Shipping']                               = $kernel_object->__( 'Shipping' );
			$language['Grand Total']                            = $kernel_object->__( 'Grand Total' );
			$language['Create']                                 = $kernel_object->__( 'Create' );
			$language['Manage Vendor']                          = $kernel_object->__( 'Manage Vendor' );
			$language['Status']                                 = $kernel_object->__( 'Status' );
			$language['Vendor Description']                     = $kernel_object->__( 'Vendor Description' );
			$language['Add Vendor']                             = $kernel_object->__( 'Add Vendor' );
			$language['%{fld_name} is required']                = $kernel_object->__( '%{fld_name} is required' );
			$language['Qty:']                                   = $kernel_object->__( 'Qty:' );
			$language['Product List']                           = $kernel_object->__( 'Product List' );
			$language['Title']                                  = $kernel_object->__( 'Title' );
			$language['Price']                                  = $kernel_object->__( 'Price' );
			$language['Category']                               = $kernel_object->__( 'Category' );
			$language['Action']                                 = $kernel_object->__( 'Action' );
			$language['Customer List']                          = $kernel_object->__( 'Customer List' );
			$language['Phone']                                  = $kernel_object->__( 'Phone' );
			$language['Address']                                = $kernel_object->__( 'Address' );
			$language['Manage Purchases']                       = $kernel_object->__( 'Manage Purchases' );
			$language['Purchase List']                          = $kernel_object->__( 'Purchase List' );
			$language['Add Purchase']                           = $kernel_object->__( 'Add Purchase' );
			$language['Vendor']                                 = $kernel_object->__( 'Vendor' );
			$language['Outlet']                                 = $kernel_object->__( 'Outlet' );
			$language['Total Cost']                             = $kernel_object->__( 'Total Cost' );
			$language['Date']                                   = $kernel_object->__( 'Date' );
			$language['Supplier List']                          = $kernel_object->__( 'Supplier List' );
			$language['Add Supplier']                           = $kernel_object->__( 'Add Supplier' );
			$language['Dashboard']                              = $kernel_object->__( 'Dashboard' );
			$language['Orders']                                 = $kernel_object->__( 'Orders' );
			$language['Products']                               = $kernel_object->__( 'Products' );
			$language['Customers']                              = $kernel_object->__( 'Customers' );
			$language['Purchase']                               = $kernel_object->__( 'Purchase' );
			$language['Shipment']                               = $kernel_object->__( 'Shipment' );
			$language['Cash']                                   = $kernel_object->__( 'Cash' );
			$language['Swipe Machine']                          = $kernel_object->__( 'Swipe Machine' );
			$language['Others']                                 = $kernel_object->__( 'Others' );
			$language['Loading..']                              = $kernel_object->__( 'Loading..' );
			$language['No Customer found']                      = $kernel_object->__( 'No Customer found' );
			$language['All Categories']                         = $kernel_object->__( 'All Categories' );
			$language['Add/Search Customer..']                  = $kernel_object->__( 'Add/Search Customer..' );
			$language['Add payment note']                       = $kernel_object->__( 'Add payment note' );
			$language['Last Four Digit of card']                = $kernel_object->__( 'Last Four Digit of card' );
			$language['Credentials not matched ! !']            = $kernel_object->__( 'Credentials not matched ! !' );
			$language['Sign In']                                = $kernel_object->__( 'Sign In' );
			$language['Username or Email address']              = $kernel_object->__( 'Username or Email address' );
			$language['Password']                               = $kernel_object->__( 'Password' );
			$language['Select Role']                            = $kernel_object->__( 'Select Role' );
			$language['Role']                                   = $kernel_object->__( 'Role' );
			$language['I agree to delete the %{rolename} and move all users of %{rolename} to the selected role'] = $kernel_object->__( 'I agree to delete the %{rolename} and move all users of %{rolename} to the selected role' );
			$language['Designation']        = $kernel_object->__( 'Designation' );
			$language['Add User']           = $kernel_object->__( 'Add User' );
			$language['Edit User']          = $kernel_object->__( 'Edit User' );
			$language['Manage User']        = $kernel_object->__( 'Manage User' );
			$language['User List']          = $kernel_object->__( 'User List' );
			$language['User']               = $kernel_object->__( 'User' );
			$language['Edit']               = $kernel_object->__( 'Edit' );
			$language['Update']             = $kernel_object->__( 'Update' );
			$language['Sale History']       = $kernel_object->__( 'Sale History' );
			$language['Hold Sale']          = $kernel_object->__( 'Hold Sale' );
			$language['Offline Sale']       = $kernel_object->__( 'Offline Sale' );
			$language['Brand']              = $kernel_object->__( 'Brand' );
			$language['Contact Info']       = $kernel_object->__( 'Contact Info' );
			$language['Item']               = $kernel_object->__( 'Item' );
			$language['Sub Total']          = $kernel_object->__( 'Sub Total' );
			$language['No %{type} found']   = $kernel_object->__( 'No %{type} found' );
			$language['customer']           = $kernel_object->__( 'customer' );
			$language['Total Quantity']     = $kernel_object->__( 'Total Quantity' );
			$language['%{qty} of %{items}'] = $kernel_object->__( '%{qty} of %{items}' );
			$language['Viewing %{ startRecord } to %{ endRecord } of %{ totalRecord } records'] = $kernel_object->__( 'Viewing %{ startRecord } to %{ endRecord } of %{ totalRecord } records' );
			$language['%{ row } rows']                              = $kernel_object->__( '%{ row } rows' );
			$language['No record found']                            = $kernel_object->__( 'No record found' );
			$language['Weight']                                     = $kernel_object->__( 'Weight' );
			$language['Height']                                     = $kernel_object->__( 'Height' );
			$language['Width']                                      = $kernel_object->__( 'Width' );
			$language['Length']                                     = $kernel_object->__( 'Length' );
			$language['Tax Status']                                 = $kernel_object->__( 'Tax Status' );
			$language['Tax Class']                                  = $kernel_object->__( 'Tax Class' );
			$language['products']                                   = $kernel_object->__( 'products' );
			$language['Loading ...']                                = $kernel_object->__( 'Loading ...' );
			$language['Up Sale']                                    = $kernel_object->__( 'Up Sale' );
			$language['Upload a Feature Image']                     = $kernel_object->__( 'Upload a Feature Image' );
			$language['Barcode']                                    = $kernel_object->__( 'Barcode' );
			$language['Order No']                                   = $kernel_object->__( 'Order No' );
			$language['Seller']                                     = $kernel_object->__( 'Seller' );
			$language['Customer info']                              = $kernel_object->__( 'Customer info' );
			$language['Order Details']                              = $kernel_object->__( 'Order Details' );
			$language['Print']                                      = $kernel_object->__( 'Print' );
			$language['Manage Orders']                              = $kernel_object->__( 'Manage Orders' );
			$language['Customer Loading ...']                       = $kernel_object->__( 'Customer Loading ...' );
			$language['Profile']                                    = $kernel_object->__( 'Profile' );
			$language['Logout']                                     = $kernel_object->__( 'Logout' );
			$language['Lock']                                       = $kernel_object->__( 'Lock' );
			$language['Help']                                       = $kernel_object->__( 'Help' );
			$language['Change Password']                            = $kernel_object->__( 'Change Password' );
			$language['Sync from database']                         = $kernel_object->__( 'Sync from database' );
			$language['You are Connected']                          = $kernel_object->__( 'You are Connected' );
			$language['You Need To Check Your Connection']          = $kernel_object->__( 'You Need To Check Your Connection' );
			$language['Change Outlet']                              = $kernel_object->__( 'Change Outlet' );
			$language['Close fullscreen']                           = $kernel_object->__( 'Close fullscreen' );
			$language['Open fullscreen']                            = $kernel_object->__( 'Open fullscreen' );
			$language['new']                                        = $kernel_object->__( 'new' );
			$language['Select Counter']                             = $kernel_object->__( 'Select Counter' );
			$language['Hello,']                                     = $kernel_object->__( 'Hello,' );
			$language['Counter']                                    = $kernel_object->__( 'Counter' );
			$language['Submit']                                     = $kernel_object->__( 'Submit' );
			$language['Change']                                     = $kernel_object->__( 'Change' );
			$language['Cash Drawer Balance']                        = $kernel_object->__( 'Cash Drawer Balance' );
			$language['Cash Drawer Balance:']                       = $kernel_object->__( 'Cash Drawer Balance:' );
			$language['Go to Pos']                                  = $kernel_object->__( 'Go to Pos' );
			$language['Go With Current Balance']                    = $kernel_object->__( 'Go With Current Balance' );
			$language['Help and info']                              = $kernel_object->__( 'Help and info' );
			$language['Active Barcode Search']                      = $kernel_object->__( 'Active Barcode Search' );
			$language['Active Product Search']                      = $kernel_object->__( 'Active Product Search' );
			$language['Pos Menu']                                   = $kernel_object->__( 'Pos Menu' );
			$language['Checkout Page']                              = $kernel_object->__( 'Checkout Page' );
			$language['Full-screen/Normal-screen']                  = $kernel_object->__( 'Full-screen/Normal-screen' );
			$language['Lock Screen']                                = $kernel_object->__( 'Lock Screen' );
			$language['Shortcut']                                   = $kernel_object->__( 'Shortcut' );
			$language['About']                                      = $kernel_object->__( 'About' );
			$language['Profile Info']                               = $kernel_object->__( 'Profile Info' );
			$language['Manage Profile']                             = $kernel_object->__( 'Manage Profile' );
			$language['Role :']                                     = $kernel_object->__( 'Role :' );
			$language['Current Outlet:']                            = $kernel_object->__( 'Current Outlet:' );
			$language['Address:']                                   = $kernel_object->__( 'Address:' );
			$language['User Name:']                                 = $kernel_object->__( 'User Name:' );
			$language['Designation:']                               = $kernel_object->__( 'Designation:' );
			$language['Email:']                                     = $kernel_object->__( 'Email:' );
			$language['Contact:']                                   = $kernel_object->__( 'Contact:' );
			$language['Total Outlet:']                              = $kernel_object->__( 'Total Outlet:' );
			$language['Current password']                           = $kernel_object->__( 'Current password' );
			$language['New password']                               = $kernel_object->__( 'New password' );
			$language['Re-type New password']                       = $kernel_object->__( 'Re-type New password' );
			$language['Search/Choose country']                      = $kernel_object->__( 'Search/Choose country' );
			$language['Select Country']                             = $kernel_object->__( 'Select Country' );
			$language['Oops !! ']                                   = $kernel_object->__( 'Oops !! ' );
			$language['Reset']                                      = $kernel_object->__( 'Reset' );
			$language['No item found for this category or search']  = $kernel_object->__( 'No item found for this category or search' );
			$language['Menu']                                       = $kernel_object->__( 'Menu' );
			$language['Cart']                                       = $kernel_object->__( 'Cart' );
			$language['Product Details']                            = $kernel_object->__( 'Product Details' );
			$language['Hold List']                                  = $kernel_object->__( 'Hold List' );
			$language['Last 4 Digit:']                              = $kernel_object->__( 'Last 4 Digit:' );
			$language['Details']                                    = $kernel_object->__( 'Details' );
			$language['Search']                                     = $kernel_object->__( 'Search' );
			$language['Choose property']                            = $kernel_object->__( 'Choose property' );
			$language['Property']                                   = $kernel_object->__( 'Property' );
			$language['Value']                                      = $kernel_object->__( 'Value' );
			$language['Amount:']                                    = $kernel_object->__( 'Amount:' );
			$language['Search or add a tag']                        = $kernel_object->__( 'Search or add a tag' );
			$language['Search/Choose Category']                     = $kernel_object->__( 'Search/Choose Category' );
			$language['Dimension and Tax']                          = $kernel_object->__( 'Dimension and Tax' );
			$language['None']                                       = $kernel_object->__( 'None' );
			$language['Taxable']                                    = $kernel_object->__( 'Taxable' );
			$language['Shipping only']                              = $kernel_object->__( 'Shipping only' );
			$language['Standard']                                   = $kernel_object->__( 'Standard' );
			$language['Reduced rate']                               = $kernel_object->__( 'Reduced rate' );
			$language['Zero rate']                                  = $kernel_object->__( 'Zero rate' );
			$language['Select / Custom Attributes']                 = $kernel_object->__( 'Select / Custom Attributes' );
			$language['Example `Color`']                            = $kernel_object->__( 'Example `Color`' );
			$language['Example Red|Blue|Green ']                    = $kernel_object->__( 'Example Red|Blue|Green ' );
			$language['No Variants Selected']                       = $kernel_object->__( 'No Variants Selected' );
			$language['Attributes List']                            = $kernel_object->__( 'Attributes List' );
			$language['Any Options']                                = $kernel_object->__( 'Any Options' );
			$language['Dimension as Parent']                        = $kernel_object->__( 'Dimension as Parent' );
			$language['Shipping and Tax Will be same as Parent']    = $kernel_object->__( 'Shipping and Tax Will be same as Parent' );
			$language['Manage Barcode']                             = $kernel_object->__( 'Manage Barcode' );
			$language['Generate Barcodes']                          = $kernel_object->__( 'Generate Barcodes' );
			$language['Choose a Outlet']                            = $kernel_object->__( 'Choose a Outlet' );
			$language['Paper Size']                                 = $kernel_object->__( 'Paper Size' );
			$language['Choose a paper settings']                    = $kernel_object->__( 'Choose a paper settings' );
			$language['Add Stock']                                  = $kernel_object->__( 'Add Stock' );
			$language['Select Vendor']                              = $kernel_object->__( 'Select Vendor' );
			$language['Choose Outlet']                              = $kernel_object->__( 'Choose Outlet' );
			$language['Choose/Search Product']                      = $kernel_object->__( 'Choose/Search Product' );
			$language['Contact No']                                 = $kernel_object->__( 'Contact No' );
			$language['Choose Role']                                = $kernel_object->__( 'Choose Role' );
			$language['Roles']                                      = $kernel_object->__( 'Roles' );
			$language['Delete']                                     = $kernel_object->__( 'Delete' );
			$language['Cancel']                                     = $kernel_object->__( 'Cancel' );
			$language['Update Status?']                             = $kernel_object->__( 'Update Status?' );
			$language['Add to Cart']                                = $kernel_object->__( 'Add to Cart' );
			$language['Remove from hold']                           = $kernel_object->__( 'Remove from hold' );
			$language['Created Time']                               = $kernel_object->__( 'Created Time' );
			$language['Edit Vendor']                                = $kernel_object->__( 'Edit Vendor' );
			$language['Successfully Updated']                       = $kernel_object->__( 'Successfully Updated' );
			$language['Purchase List Loading ...']                  = $kernel_object->__( 'Purchase List Loading ...' );
			$language['Order List Loading ...']                     = $kernel_object->__( 'Order List Loading ...' );
			$language['Product Loading ...']                        = $kernel_object->__( 'Product Loading ...' );
			$language['Stock List Loading ...']                     = $kernel_object->__( 'Stock List Loading ...' );
			$language['Vendor List Loading ...']                    = $kernel_object->__( 'Vendor List Loading ...' );
			$language['User List Loading ...']                      = $kernel_object->__( 'User List Loading ...' );
			$language['Are you sure to delete this user: %{user}?'] = $kernel_object->__( 'Are you sure to delete this user: %{user}?' );
			$language['Are you sure to delete this Customer: %{customer}?'] = $kernel_object->__( 'Are you sure to delete this Customer: %{customer}?' );
			$language['Are you sure to remove item from Holds?']            = $kernel_object->__( 'Are you sure to remove item from Holds?' );
			$language['Are you sure to remove all item from cart?']         = $kernel_object->__( 'Are you sure to remove all item from cart?' );
			$language['Loading Product Details...']                         = $kernel_object->__( 'Loading Product Details...' );
			$language['Order Details Loading...']                           = $kernel_object->__( 'Order Details Loading...' );
			$language['Loading Vendor Details...']                          = $kernel_object->__( 'Loading Vendor Details...' );
			$language['Loading Customer Details...']                        = $kernel_object->__( 'Loading Customer Details...' );
			$language['Loading User Details...']                            = $kernel_object->__( 'Loading User Details...' );
			$language['Are you sure to delete this vendor: %{vendor}?']     = $kernel_object->__( 'Are you sure to delete this vendor: %{vendor}?' );
			$language['Restore From Hold']                                  = $kernel_object->__( 'Restore From Hold' );
			$language['Want You like to do with current cart ?']            = $kernel_object->__( 'Want You like to do with current cart ?' );
			$language['Hold cart']                             = $kernel_object->__( 'Hold cart' );
			$language['Clear Cart']                            = $kernel_object->__( 'Clear Cart' );
			$language['To POS']                                = $kernel_object->__( 'To POS' );
			$language['Payment Note']                          = $kernel_object->__( 'Payment Note' );
			$language['Payment Amount']                        = $kernel_object->__( 'Payment Amount' );
			$language['Card']                                  = $kernel_object->__( 'Card' );
			$language['Type to search']                        = $kernel_object->__( 'Type to search' );
			$language['Show Details']                          = $kernel_object->__( 'Show Details' );
			$language['Loading Purchase Details...']           = $kernel_object->__( 'Loading Purchase Details...' );
			$language['Purchase Details']                      = $kernel_object->__( 'Purchase Details' );
			$language['Outlet:']                               = $kernel_object->__( 'Outlet:' );
			$language['Supplier:']                             = $kernel_object->__( 'Supplier:' );
			$language['Purchased Date:']                       = $kernel_object->__( 'Purchased Date:' );
			$language['Purchased No:']                         = $kernel_object->__( 'Purchased No:' );
			$language['Purchased By:']                         = $kernel_object->__( 'Purchased By:' );
			$language['Payment Status:']                       = $kernel_object->__( 'Payment Status:' );
			$language['Paid']                                  = $kernel_object->__( 'Paid' );
			$language['Unpaid']                                = $kernel_object->__( 'Unpaid' );
			$language['Purchased Items']                       = $kernel_object->__( 'Purchased Items' );
			$language['Items Price']                           = $kernel_object->__( 'Items Price' );
			$language['Total cost']                            = $kernel_object->__( 'Total cost' );
			$language['Print Receipt']                         = $kernel_object->__( 'Print Receipt' );
			$language['New sale']                              = $kernel_object->__( 'New sale' );
			$language['Payment processing ...']                = $kernel_object->__( 'Payment processing ...' );
			$language['User loading ...']                      = $kernel_object->__( 'User loading ...' );
			$language['Loading From']                          = $kernel_object->__( 'Loading From' );
			$language['Pro version required']                  = $kernel_object->__( 'Pro version required' );
			$language['Go pro']                                = $kernel_object->__( 'Go pro' );
			$language['Pro Version Required for this feature'] = $kernel_object->__( 'Pro Version Required for this feature' );
			$language['Split payment support in pro version']  = $kernel_object->__( 'Split payment support in pro version' );
			$language['Offline sale allowed in pro version']   = $kernel_object->__( 'Offline sale allowed in pro version' );
			$language['Hold cart applicable in pro version']   = $kernel_object->__( 'Hold cart applicable in pro version' );
			$language['Hold Cart Supported In Pro Version']    = $kernel_object->__( 'Hold Cart Supported In Pro Version' );
			$language['Notification details']                  = $kernel_object->__( 'Notification details' );
			$language['Go To Purchase']                        = $kernel_object->__( 'Go To Purchase' );
			$language['Stock Alert!']                          = $kernel_object->__( 'Stock Alert!' );
			$language['Stock is running low Please purchase item to increase stock'] = $kernel_object->__( 'Stock is running low Please purchase item to increase stock' );
			$language['Personal Information']                                        = $kernel_object->__( 'Personal Information' );
			$language['Outlet Information']              = $kernel_object->__( 'Outlet Information' );
			$language['Outlet Name']                     = $kernel_object->__( 'Outlet Name' );
			$language['Total Outlets']                   = $kernel_object->__( 'Total Outlets' );
			$language['Current Cash Drawer Information'] = $kernel_object->__( 'Current Cash Drawer Information' );
			$language['Cash Information']                = $kernel_object->__( 'Cash Information' );
			$language['Opening Cash']                    = $kernel_object->__( 'Opening Cash' );
			$language['Current Cash']                    = $kernel_object->__( 'Current Cash' );
			$language['Counter Information']             = $kernel_object->__( 'Counter Information' );
			$language['Counter Name']                    = $kernel_object->__( 'Counter Name' );
			$language['Counter Number']                  = $kernel_object->__( 'Counter Number' );
			$language['Want to close cash drawer?']      = $kernel_object->__( 'Want to close cash drawer?' );
			$language['Need to close cash drawer to change outlet. Do you want to close now?'] = $kernel_object->__( 'Need to close cash drawer to change outlet. Do you want to close now?' );
			$language['Cash Drawer']                                = $kernel_object->__( 'Cash Drawer' );
			$language['No name found']                              = $kernel_object->__( 'No name found' );
			$language['No number found']                            = $kernel_object->__( 'No number found' );
			$language['No designation found']                       = $kernel_object->__( 'No designation found' );
			$language['No cash drawer information found']           = $kernel_object->__( 'No cash drawer information found' );
			$language['Download ']                                  = $kernel_object->__( 'Download ' );
			$language['Add To Cart (']                              = $kernel_object->__( 'Add To Cart (' );
			$language['Add To Cart']                                = $kernel_object->__( 'Add To Cart' );
			$language['Changed Amount']                             = $kernel_object->__( 'Changed Amount' );
			$language['Other']                                      = $kernel_object->__( 'Other' );
			$language['Cash Summary']                               = $kernel_object->__( 'Cash Summary' );
			$language['Close & Create New']                         = $kernel_object->__( 'Close & Create New' );
			$language['Close & Logout']                             = $kernel_object->__( 'Close & Logout' );
			$language['Current cash drawer order list']             = $kernel_object->__( 'Current cash drawer order list' );
			$language['Hold no : %{holdNo}']                        = $kernel_object->__( 'Hold no : %{holdNo}' );
			$language['Upload Image For This Variation']            = $kernel_object->__( 'Upload Image For This Variation' );
			$language['Do you want to close cash drawer & logout?'] = $kernel_object->__( 'Do you want to close cash drawer & logout?' );
			$language['Do you want to close current cash drawer & create new cash drawer?'] = $kernel_object->__( 'Do you want to close current cash drawer & create new cash drawer?' );
			$language['Cash drawer loading...']         = $kernel_object->__( 'Cash drawer loading...' );
			$language['Thank You For Purchasing']       = $kernel_object->__( 'Thank You For Purchasing' );
			$language['Sign in using another account.'] = $kernel_object->__( 'Sign in using another account.' );
			$language['click here']                     = $kernel_object->__( 'click here' );
			$language['Processed By']                   = $kernel_object->__( 'Processed By' );
			$language['Regular unit price: %{reg_price}, sale price: %{sale}'] = $kernel_object->__( 'Regular unit price: %{reg_price}, sale price: %{sale}' );
			$language['You are not connected']                                 = $kernel_object->__( 'You are not connected' );
			$language['This module is not support in offline']                 = $kernel_object->__( 'This module is not support in offline' );
			$language['Update Note']      = $kernel_object->__( 'Update Note' );
			$language['Selected Product'] = $kernel_object->__( 'Selected Product' );
			$language['Product Name']     = $kernel_object->__( 'Product Name' );
			$language['Product Syncing']  = $kernel_object->__( 'Product Syncing' );
			$language['Last sync :']      = $kernel_object->__( 'Last sync :' );
			$language['To process order offline please ask your admin to enable offline order feature.'] = $kernel_object->__( 'To process order offline please ask your admin to enable offline order feature.' );
			$language['Upload More Images For This Product']    = $kernel_object->__( 'Upload More Images For This Product' );
			$language['No barcode found for this product']      = $kernel_object->__( 'No barcode found for this product' );
			$language['This product already added in the list'] = $kernel_object->__( 'This product already added in the list' );
			$language['Set Password']                           = $kernel_object->__( 'Set Password' );
			$language['Change password required']               = $kernel_object->__( 'Change password required' );
			$language['This would be a temporary password.The user have to change their password on first login.'] = $kernel_object->__( 'This would be a temporary password.The user have to change their password on first login.' );
			$language['%{fld_name} does not match with its password'] = $kernel_object->__( '%{fld_name} does not match with its password' );
			$language['Set password']                                 = $kernel_object->__( 'Set password' );
			$language['Loading...']                                   = $kernel_object->__( 'Loading...' );
			$language['Loading']                                      = $kernel_object->__( 'Loading' );
			$language['Videos']                                       = $kernel_object->__( 'Videos' );
			$language['Roles Loading']                                = $kernel_object->__( 'Roles Loading' );
			$language['Attributes Loading']                           = $kernel_object->__( 'Attributes Loading' );
			$language['Edit Product']                                 = $kernel_object->__( 'Edit Product' );
			$language['Are you sure to delete this product: %{product}?'] = $kernel_object->__( 'Are you sure to delete this product: %{product}?' );
			$language['Customer add not supported in offline']            = $kernel_object->__( 'Customer add not supported in offline' );
			$language['Click to see offline order']                       = $kernel_object->__( 'Click to see offline order' );
			$language['Syncing offline orders']                           = $kernel_object->__( 'Syncing offline orders' );
			$language['You do not have permission of add customer, contact your admin to get this permission.'] = $kernel_object->__( 'You do not have permission of add customer, contact your admin to get this permission.' );
						$language['Settings Loading'] = $kernel_object->__( 'Settings Loading' );
			$language['POS']              = $kernel_object->__( 'POS' );
			$language['Offline Order']    = $kernel_object->__( 'Offline Order' );
			$language['Create New']       = $kernel_object->__( 'Create New' );
			$language['No address found'] = $kernel_object->__( 'No address found' );
			$language['All']              = $kernel_object->__( 'All' );
			$language['hold items']       = $kernel_object->__( 'hold items' );
			$language['Supplier']         = $kernel_object->__( 'Supplier' );
			$language['Last sync :']      = $kernel_object->__( 'Last sync :' );
			$language['Select variation'] = $kernel_object->__( 'Select variation' );
						$language['Drawer Log']                            = $kernel_object->__( 'Drawer Log' );
			$language['Customer View']                         = $kernel_object->__( 'Customer View' );
			$language['Product Syncing.']                      = $kernel_object->__( 'Product Syncing.' );
			$language['Select Sub-category']                   = $kernel_object->__( 'Select Sub-category' );
			$language['Last 7 days cash drawer logs']          = $kernel_object->__( 'Last 7 days cash drawer logs' );
			$language['Opened By']                             = $kernel_object->__( 'Opened By' );
			$language['Closed By']                             = $kernel_object->__( 'Closed By' );
			$language['drawer logs']                           = $kernel_object->__( 'drawer logs' );
			$language['Operate By']                            = $kernel_object->__( 'Operate By' );
			$language['Outlet - Counter']                      = $kernel_object->__( 'Outlet - Counter' );
			$language['Balance']                               = $kernel_object->__( 'Balance' );
			$language['Time']                                  = $kernel_object->__( 'Time' );
			$language['Offline']                               = $kernel_object->__( 'Offline' );
			$language['Completed']                             = $kernel_object->__( 'Completed' );
			$language['Show Price']                            = $kernel_object->__( 'Show Price' );
			$language['Show price on barcode label']           = $kernel_object->__( 'Show price on barcode label' );
			$language['Online']                                = $kernel_object->__( 'Online' );
			$language['Order Status :']                        = $kernel_object->__( 'Order Status :' );
			$language['Drawer Log Loading ...']                = $kernel_object->__( 'Drawer Log Loading ...' );
			$language['Opening - Closing']                     = $kernel_object->__( 'Opening - Closing' );
			$language['Opening time']                          = $kernel_object->__( 'Opening time' );
			$language['Closing Time']                          = $kernel_object->__( 'Closing Time' );
			$language['Open :']                                = $kernel_object->__( 'Open :' );
			$language['Close :']                               = $kernel_object->__( 'Close :' );
			$language['Type']                                  = $kernel_object->__( 'Type' );
			$language['Previous Balance :']                    = $kernel_object->__( 'Previous Balance :' );
			$language['Amount']                                = $kernel_object->__( 'Amount' );
			$language['Opening Balance']                       = $kernel_object->__( 'Opening Balance' );
			$language['Closing Balance']                       = $kernel_object->__( 'Closing Balance' );
			$language['On going']                              = $kernel_object->__( 'On going' );
			$language['No permission to add discount']         = $kernel_object->__( 'No permission to add discount' );
			$language['You can not add this much of discount'] = $kernel_object->__( 'You can not add this much of discount' );
			$language['Vitepos is a POS specialized software which is the right solution for your business.'] = $kernel_object->__( 'Vitepos is a POS specialized software which is the right solution for your business.' );
			$language['Cash Drawer details']        = $kernel_object->__( 'Cash Drawer details' );
			$language['Drawer Opened']              = $kernel_object->__( 'Drawer Opened' );
			$language['Make favorite']              = $kernel_object->__( 'Make favorite' );
			$language['On going']                   = $kernel_object->__( 'On going' );
			$language['Add items to give discount'] = $kernel_object->__( 'Add items to give discount' );
			$language['Add items to add fee']       = $kernel_object->__( 'Add items to add fee' );
			$language['Role wise discount manage']  = $kernel_object->__( 'Role wise discount manage' );
						$language['Table Panel']                                  = $kernel_object->__( 'Table Panel' );
			$language['Loading Table Details...']                     = $kernel_object->__( 'Loading Table Details...' );
			$language['Add Table']                                    = $kernel_object->__( 'Add Table' );
			$language['Edit Table']                                   = $kernel_object->__( 'Edit Table' );
			$language['Types']                                        = $kernel_object->__( 'Types' );
			$language['Table']                                        = $kernel_object->__( 'Table' );
			$language['Parcel']                                       = $kernel_object->__( 'Parcel' );
			$language['Table Title']                                  = $kernel_object->__( 'Table Title' );
			$language['Seat Capability']                              = $kernel_object->__( 'Seat Capability' );
			$language['Table Description']                            = $kernel_object->__( 'Table Description' );
			$language['Table Image']                                  = $kernel_object->__( 'Table Image' );
			$language['Upload Table Image']                           = $kernel_object->__( 'Upload Table Image' );
			$language['Is Mergeable?']                                = $kernel_object->__( 'Is Mergeable?' );
			$language['Empty seat:']                                  = $kernel_object->__( 'Empty seat:' );
			$language['Are you sure to delete this Table: %{table}?'] = $kernel_object->__( 'Are you sure to delete this Table: %{table}?' );
			$language['Add Addon']                                    = $kernel_object->__( 'Add Addon' );
			$language['Fields']                                       = $kernel_object->__( 'Fields' );
			$language['Add Field']                                    = $kernel_object->__( 'Add Field' );
			$language['New Field']                                    = $kernel_object->__( 'New Field' );
			$language['No fields Added']                              = $kernel_object->__( 'No fields Added' );
			$language['Rules']                                        = $kernel_object->__( 'Rules' );
			$language['Add Rules Group']                              = $kernel_object->__( 'Add Rules Group' );
			$language['Are you sure to remove it?']                   = $kernel_object->__( 'Are you sure to remove it?' );
			$language['Remove']                                       = $kernel_object->__( 'Remove' );
			$language['AND']                                     = $kernel_object->__( 'AND' );
			$language['Rule Group']                              = $kernel_object->__( 'Rule Group' );
			$language['Add Rules']                               = $kernel_object->__( 'Add Rules' );
			$language['No Rules Added']                          = $kernel_object->__( 'No Rules Added' );
			$language['No Rules Group Added']                    = $kernel_object->__( 'No Rules Group Added' );
			$language['Addons Panel']                            = $kernel_object->__( 'Addons Panel' );
			$language['Manage Drawer Log']                       = $kernel_object->__( 'Manage Drawer Log' );
			$language['Waiter POS']                              = $kernel_object->__( 'Waiter POS' );
			$language['Addons']                                  = $kernel_object->__( 'Addons' );
			$language['Tables']                                  = $kernel_object->__( 'Tables' );
			$language['Add Addons']                              = $kernel_object->__( 'Add Addons' );
			$language['Addons List Loading...']                  = $kernel_object->__( 'Addons List Loading...' );
			$language['Title/Label']                             = $kernel_object->__( 'Title/Label' );
			$language['Select Type']                             = $kernel_object->__( 'Select Type' );
			$language['Textbox']                                 = $kernel_object->__( 'Textbox' );
			$language['Textbox (Multiline)']                     = $kernel_object->__( 'Textbox (Multiline)' );
			$language['Dropdown']                                = $kernel_object->__( 'Dropdown' );
			$language['Radio']                                   = $kernel_object->__( 'Radio' );
			$language['Checkbox']                                = $kernel_object->__( 'Checkbox' );
			$language['Placeholder']                             = $kernel_object->__( 'Placeholder' );
			$language['Default value']                           = $kernel_object->__( 'Default value' );
			$language['Max Limit']                               = $kernel_object->__( 'Max Limit' );
			$language['Add Option']                              = $kernel_object->__( 'Add Option' );
			$language['No option added']                         = $kernel_object->__( 'No option added' );
			$language['Is Required?']                            = $kernel_object->__( 'Is Required?' );
			$language['Label']                                   = $kernel_object->__( 'Label' );
			$language['Is Selected?']                            = $kernel_object->__( 'Is Selected?' );
			$language['Equal to']                                = $kernel_object->__( 'Equal to' );
			$language['Not equal to']                            = $kernel_object->__( 'Not equal to' );
			$language['Remove from favorite']                    = $kernel_object->__( 'Remove from favorite' );
			$language['Edit custom page size']                   = $kernel_object->__( 'Edit custom page size' );
			$language['Delete custom page size']                 = $kernel_object->__( 'Delete custom page size' );
			$language['Are you sure to delete this page style?'] = $kernel_object->__( 'Are you sure to delete this page style?' );
			$language['Code Type']                               = $kernel_object->__( 'Code Type' );
			$language['Code type is required']                   = $kernel_object->__( 'Code type is required' );
			$language['Title is required']                       = $kernel_object->__( 'Title is required' );
			$language['Choose type of code']                     = $kernel_object->__( 'Choose type of code' );
			$language['Custom Size Title']                       = $kernel_object->__( 'Custom Size Title' );
			$language['QR-code']                                 = $kernel_object->__( 'QR-code' );
			$language['Page Height']                             = $kernel_object->__( 'Page Height' );
			$language['Keep blank for auto height']              = $kernel_object->__( 'Keep blank for auto height' );
			$language['Blank for auto']                          = $kernel_object->__( 'Blank for auto' );
			$language['Page Width']                              = $kernel_object->__( 'Page Width' );
			$language['Please add a width for paper size']       = $kernel_object->__( 'Please add a width for paper size' );
			$language['mm']                                      = $kernel_object->__( 'mm' );
			$language['Page Padding']                            = $kernel_object->__( 'Page Padding' );
			$language['Left,Right']                              = $kernel_object->__( 'Left,Right' );
			$language['Top,Bottom']                              = $kernel_object->__( 'Top,Bottom' );
			$language['Container Margin']                        = $kernel_object->__( 'Container Margin' );
			$language['Font Size']                               = $kernel_object->__( 'Font Size' );
			$language['Container Size']                          = $kernel_object->__( 'Container Size' );
			$language['Barcode width']                           = $kernel_object->__( 'Barcode width' );
			$language['Barcode Height']                          = $kernel_object->__( 'Barcode Height' );
			$language['Number of barcode in a page']             = $kernel_object->__( 'Number of barcode in a page' );
			$language['Page break counter']                      = $kernel_object->__( 'Page break counter' );
			$language['Add page break counter']                  = $kernel_object->__( 'Add page break counter' );

			$language['Price Update list']              = $kernel_object->__( 'Price Update list' );
			$language['Current Cost']                   = $kernel_object->__( 'Current Cost' );
			$language['Previous Cost']                  = $kernel_object->__( 'Previous Cost' );
			$language['price updated product']          = $kernel_object->__( 'price updated product' );
			$language['Select State']                   = $kernel_object->__( 'Select State' );
			$language['Loading cash drawer details...'] = $kernel_object->__( 'Loading cash drawer details...' );
			$language['Outlet :']                       = $kernel_object->__( 'Outlet :' );
			$language['Counter :']                      = $kernel_object->__( 'Counter :' );
			$language['Previous Balance']               = $kernel_object->__( 'Previous Balance' );

			$language['Loading Addon Details...'] = $kernel_object->__( 'Loading Addon Details...' );
			$language['OR']                       = $kernel_object->__( 'OR' );
			$language['Select Waiters']           = $kernel_object->__( 'Select Waiters' );

			$language['Waiter Panel']                            = $kernel_object->__( 'Waiter Panel' );
			$language['Orders Details']                          = $kernel_object->__( 'Orders Details' );
			$language['Order Time']                              = $kernel_object->__( 'Order Time' );
			$language['Reload Caps']                             = $kernel_object->__( 'Reload Caps' );
			$language['Status:']                                 = $kernel_object->__( 'Status:' );
			$language['Sync Orders']                             = $kernel_object->__( 'Sync Orders' );
			$language['Create New Order']                        = $kernel_object->__( 'Create New Order' );
			$language['Back']                                    = $kernel_object->__( 'Back' );
			$language['Create Order']                            = $kernel_object->__( 'Create Order' );
			$language['New Order']                               = $kernel_object->__( 'New Order' );
			$language['Table is']                                = $kernel_object->__( 'Table is' );
			$language['and person count']                        = $kernel_object->__( 'and person count' );
			$language['Choose table and persons']                = $kernel_object->__( 'Choose table and persons' );
			$language['Done']                                    = $kernel_object->__( 'Done' );
			$language['Choose Table']                            = $kernel_object->__( 'Choose Table' );
			$language['Parcel mode is chosen']                   = $kernel_object->__( 'Parcel mode is chosen' );
			$language['Choose Table to enter person']            = $kernel_object->__( 'Choose Table to enter person' );
			$language['Number of person']                        = $kernel_object->__( 'Number of person' );
			$language['No message found']                        = $kernel_object->__( 'No message found' );
			$language['Active']                                  = $kernel_object->__( 'Active' );
			$language['Canceled']                                = $kernel_object->__( 'Canceled' );
			$language['Are you sure to cancel the order?']       = $kernel_object->__( 'Are you sure to cancel the order?' );
			$language['Shortcuts']                               = $kernel_object->__( 'Shortcuts' );
			$language['In Kitchen']                              = $kernel_object->__( 'In Kitchen' );
			$language['To Kitchen']                              = $kernel_object->__( 'To Kitchen' );
			$language['Preparing']                               = $kernel_object->__( 'Preparing' );
			$language['Ready to Serve']                          = $kernel_object->__( 'Ready to Serve' );
			$language['Cancelled']                               = $kernel_object->__( 'Cancelled' );
			$language['Served']                                  = $kernel_object->__( 'Served' );
			$language['Make Serve']                              = $kernel_object->__( 'Make Serve' );
			$language['Waiting']                                 = $kernel_object->__( 'Waiting' );
			$language['Request canceled']                        = $kernel_object->__( 'Request canceled' );
			$language['Order is cancelled']                      = $kernel_object->__( 'Order is cancelled' );
			$language['Cancel requested']                        = $kernel_object->__( 'Cancel requested' );
			$language['See/edit table and person info']          = $kernel_object->__( 'See/edit table and person info' );
			$language['Kitchen Panel']                           = $kernel_object->__( 'Kitchen Panel' );
			$language['Kitchen']                                 = $kernel_object->__( 'Kitchen' );
			$language['Table ']                                  = $kernel_object->__( 'Table ' );
			$language['Waiter requested to cancel']              = $kernel_object->__( 'Waiter requested to cancel' );
			$language['Accept']                                  = $kernel_object->__( 'Accept' );
			$language['Deny']                                    = $kernel_object->__( 'Deny' );
			$language['Deny order']                              = $kernel_object->__( 'Deny order' );
			$language['Start Preparing']                         = $kernel_object->__( 'Start Preparing' );
			$language['Print Order']                             = $kernel_object->__( 'Print Order' );
			$language['Add message']                             = $kernel_object->__( 'Add message' );
			$language['Are you sure you are starting?']          = $kernel_object->__( 'Are you sure you are starting?' );
			$language['Are you sure order is ready to serve?']   = $kernel_object->__( 'Are you sure order is ready to serve?' );
			$language['Are you sure to serve the order?']        = $kernel_object->__( 'Are you sure to serve the order?' );
			$language['Why are denying this order?']             = $kernel_object->__( 'Why are denying this order?' );
			$language['Input is empty']                          = $kernel_object->__( 'Input is empty' );
			$language['No order found']                          = $kernel_object->__( 'No order found' );
			$language['Served By']                               = $kernel_object->__( 'Served By' );
			$language['Current Status']                          = $kernel_object->__( 'Current Status' );
			$language['Order Type']                              = $kernel_object->__( 'Order Type' );
			$language['In Dine']                                 = $kernel_object->__( 'In Dine' );
			$language['Order Note']                              = $kernel_object->__( 'Order Note' );
			$language['Thank You']                               = $kernel_object->__( 'Thank You' );
			$language['Unknown']                                 = $kernel_object->__( 'Unknown' );
			$language['Cashier Panel']                           = $kernel_object->__( 'Cashier Panel' );
			$language['Cashier']                                 = $kernel_object->__( 'Cashier' );
			$language['Loading orders...']                       = $kernel_object->__( 'Loading orders...' );
			$language['TABLE']                                   = $kernel_object->__( 'TABLE' );
			$language['Loading order details...']                = $kernel_object->__( 'Loading order details...' );
			$language['Seat capacity is low than person number'] = $kernel_object->__( 'Seat capacity is low than person number' );

			$language['Add items to give discount']  = $kernel_object->__( 'Add items to give discount' );
			$language['Add items to add fee']        = $kernel_object->__( 'Add items to add fee' );
			$language['Out of Stock']                = $kernel_object->__( 'Out of Stock' );
			$language['Current Stock']               = $kernel_object->__( 'Current Stock' );
			$language['Stock Transfer']              = $kernel_object->__( 'Stock Transfer' );
			$language['Receive Transfer']            = $kernel_object->__( 'Receive Transfer' );
			$language['Transfer List Loading ...']   = $kernel_object->__( 'Transfer List Loading ...' );
			$language['From Outlet']                 = $kernel_object->__( 'From Outlet' );
			$language['To Outlet']                   = $kernel_object->__( 'To Outlet' );
			$language['Transfer Date']               = $kernel_object->__( 'Transfer Date' );
			$language['Receive Date']                = $kernel_object->__( 'Receive Date' );
			$language['Transfer Stock']              = $kernel_object->__( 'Transfer Stock' );
			$language['From outlet']                 = $kernel_object->__( 'From outlet' );
			$language['Notes']                       = $kernel_object->__( 'Notes' );
			$language['Scan Product']                = $kernel_object->__( 'Scan Product' );
			$language['Transfer Item*']              = $kernel_object->__( 'Transfer Item*' );
			$language['In-stock']                    = $kernel_object->__( 'In-stock' );
			$language['Transfer Quantity']           = $kernel_object->__( 'Transfer Quantity' );
			$language['Item no']                     = $kernel_object->__( 'Item no' );
			$language['quantity is']                 = $kernel_object->__( 'quantity is' );
			$language['Transfer']                    = $kernel_object->__( 'Transfer' );
			$language['Loading Transfer Details...'] = $kernel_object->__( 'Loading Transfer Details...' );
			$language['Transfer Details']            = $kernel_object->__( 'Transfer Details' );
			$language['From Outlet:']                = $kernel_object->__( 'From Outlet:' );
			$language['No outlet found:']            = $kernel_object->__( 'No outlet found:' );
			$language['Transfer By:']                = $kernel_object->__( 'Transfer By:' );
			$language['Transfer date:']              = $kernel_object->__( 'Transfer date:' );
			$language['To Outlet:']                  = $kernel_object->__( 'To Outlet:' );
			$language['Declined By']                 = $kernel_object->__( 'Declined By' );
			$language['Receive By']                  = $kernel_object->__( 'Receive By' );
			$language['Not Received yet']            = $kernel_object->__( 'Not Received yet' );
			$language['Transfer Status:']            = $kernel_object->__( 'Transfer Status:' );
			$language['Pending']                     = $kernel_object->__( 'Pending' );
			$language['Transferred Items']           = $kernel_object->__( 'Transferred Items' );
			$language['Transfer Note']               = $kernel_object->__( 'Transfer Note' );
			$language['Receive Note:']               = $kernel_object->__( 'Receive Note:' );
			$language['Add note befor decline']      = $kernel_object->__( 'Add note befor decline' );
			$language['Decline']                     = $kernel_object->__( 'Decline' );
			$language['Receive']                     = $kernel_object->__( 'Receive' );
			$language['Receiving Stocks']            = $kernel_object->__( 'Receiving Stocks' );
			$language['Out of stock']                = $kernel_object->__( 'Out of stock' );

			$language['check for add this product to update price list, if need to update product price'] = $kernel_object->__( 'check for add this product to update price list, if need to update product price' );
			$language['Previous purchase cost :']                         = $kernel_object->__( 'Previous purchase cost :' );
			$language['Current purchase cost :']                          = $kernel_object->__( 'Current purchase cost :' );
			$language['Product name :']                                   = $kernel_object->__( 'Product name :' );
			$language['NOTE : Regular price is less than purchase price'] = $kernel_object->__( 'NOTE : Regular price is less than purchase price' );
			$language['Last 5 Purchase History']                          = $kernel_object->__( 'Last 5 Purchase History' );
			$language['Previous Purchase Price']                          = $kernel_object->__( 'Previous Purchase Price' );
			$language['Current Purchase Price']                           = $kernel_object->__( 'Current Purchase Price' );
			$language['Are you sure to ignore this update of price ?']    = $kernel_object->__( 'Are you sure to ignore this update of price ?' );
			$language['Ignore']        = $kernel_object->__( 'Ignore' );
			$language['Update Prices'] = $kernel_object->__( 'Update Prices' );
			$language['Ignore Update'] = $kernel_object->__( 'Ignore Update' );

						$language['Stock Counter'] = $kernel_object->__( 'Stock Counter' );

			$language['No short messages found']                    = $kernel_object->__( 'No short messages found' );
			$language['No table found please add some table']       = $kernel_object->__( 'No table found please add some table' );
			$language['Inactive']                                   = $kernel_object->__( 'Inactive' );
			$language['Product Status']                             = $kernel_object->__( 'Product Status' );
			$language['Published']                                  = $kernel_object->__( 'Published' );
			$language['Private']                                    = $kernel_object->__( 'Private' );
			$language['Ready to serve']                             = $kernel_object->__( 'Ready to serve' );
			$language['Stock Log']                                  = $kernel_object->__( 'Stock Log' );
			$language['Loading Stock Logs...']                      = $kernel_object->__( 'Loading Stock Logs...' );
			$language['Stock Details']                              = $kernel_object->__( 'Stock Details' );
			$language['Name :']                                     = $kernel_object->__( 'Name :' );
			$language['Price :']                                    = $kernel_object->__( 'Price :' );
			$language['Product Stock Logs']                         = $kernel_object->__( 'Product Stock Logs' );
			$language['Message']                                    = $kernel_object->__( 'Message' );
			$language['Previous']                                   = $kernel_object->__( 'Previous' );
			$language['Current']                                    = $kernel_object->__( 'Current' );
			$language['No logs found for this product']             = $kernel_object->__( 'No logs found for this product' );
			$language['No active orders found']                     = $kernel_object->__( 'No active orders found' );
			$language['No cancelled order found']                   = $kernel_object->__( 'No cancelled order found' );
			$language['No Table found please add table first']      = $kernel_object->__( 'No Table found please add table first' );
			$language['Select Addons']                              = $kernel_object->__( 'Select Addons' );
			$language['Deny Order']                                 = $kernel_object->__( 'Deny Order' );
			$language['Are you sure,want to make cancel request?']  = $kernel_object->__( 'Are you sure,want to make cancel request?' );
			$language['Are sure to deny cancel request?']           = $kernel_object->__( 'Are sure to deny cancel request?' );
			$language['Are sure to accept cancel?']                 = $kernel_object->__( 'Are sure to accept cancel?' );
			$language['Request to cancel order']                    = $kernel_object->__( 'Request to cancel order' );
			$language['Are you sure to make this product favorite'] = $kernel_object->__( 'Are you sure to make this product favorite' );
			$language['Make Completed']                             = $kernel_object->__( 'Make Completed' );
			$language['Sync restaurant order list']                 = $kernel_object->__( 'Sync restaurant order list' );
			$language['Seat capacity : %{seat_cap}']                = $kernel_object->__( 'Seat capacity : %{seat_cap}' );
			$language['Favorite']                                   = $kernel_object->__( 'Favorite' );
			$language['No variations']                              = $kernel_object->__( 'No variations' );
			$language['Full Refund']                                = $kernel_object->__( 'Full Refund' );
			$language['Refund']                                     = $kernel_object->__( 'Refund' );
			$language['Search Again']                               = $kernel_object->__( 'Search Again' );
			$language['Order is loading...']                        = $kernel_object->__( 'Order is loading...' );
			$language['Refunding Order...']                         = $kernel_object->__( 'Refunding Order...' );
			$language['Refund Orders']                              = $kernel_object->__( 'Refund Orders' );
			$language['Mute']                                       = $kernel_object->__( 'Mute' );
			$language['Draft']                                      = $kernel_object->__( 'Draft' );
			$language['Failed']                                     = $kernel_object->__( 'Failed' );
			$language['Refunded']                                   = $kernel_object->__( 'Refunded' );
			$language['On hold']                                    = $kernel_object->__( 'On hold' );
			$language['Processing']                                 = $kernel_object->__( 'Processing' );
			$language['Pending payment']                            = $kernel_object->__( 'Pending payment' );
			$language['Ref']                                        = $kernel_object->__( 'Ref' );
			$language['Refund Qty']                                 = $kernel_object->__( 'Refund Qty' );
			$language['No customer found']                          = $kernel_object->__( 'No customer found' );
			$language['Order No:']                                  = $kernel_object->__( 'Order No:' );
			$language['Choose item(s) to refund']                   = $kernel_object->__( 'Choose item(s) to refund' );
			$language['Order info']                                 = $kernel_object->__( 'Order info' );
			$language['Sub-total :']                                = $kernel_object->__( 'Sub-total :' );
			$language['Tax-total']                                  = $kernel_object->__( 'Tax-total' );
			$language['Total Discount :']                           = $kernel_object->__( 'Total Discount :' );
			$language['Grand-total :']                              = $kernel_object->__( 'Grand-total :' );
			$language['Refund Info"']                               = $kernel_object->__( 'Refund Info"' );
			$language['Aprox']                                      = $kernel_object->__( 'Aprox' );
			$language['Refund Sub-total :']                         = $kernel_object->__( 'Refund Sub-total :' );
			$language['Refund Tax-total :']                         = $kernel_object->__( 'Refund Tax-total :' );
			$language['Refund Discount:']                           = $kernel_object->__( 'Refund Discount:' );
			$language['Refund Fee :']                               = $kernel_object->__( 'Refund Fee :' );
			$language['Refund Grand-total :']                       = $kernel_object->__( 'Refund Grand-total :' );
			$language['Order Has been refunded successfully']       = $kernel_object->__( 'Order Has been refunded successfully' );
			$language['Please return amount']                       = $kernel_object->__( 'Please return amount' );

						$language['Transfer by']               = $kernel_object->__( 'Transfer by' );
			$language['Declined Note']             = $kernel_object->__( 'Declined Note' );
			$language['Refund List']               = $kernel_object->__( 'Refund List' );
			$language['Top']                       = $kernel_object->__( 'Top' );
			$language['Bottom']                    = $kernel_object->__( 'Bottom' );
			$language['Refund Reason']             = $kernel_object->__( 'Refund Reason' );
			$language['Refund Id ']                = $kernel_object->__( 'Refund Id ' );
			$language['Update Product Price']      = $kernel_object->__( 'Update Product Price' );
			$language['No history found']          = $kernel_object->__( 'No history found' );
			$language['No transfer %{type} found'] = $kernel_object->__( 'No transfer %{type} found' );
			$language['stripe']                    = $kernel_object->__( 'stripe' );
			$language['Stripe']                    = $kernel_object->__( 'Stripe' );
			$language['swipe']                     = $kernel_object->__( 'swipe' );
			$language['others']                    = $kernel_object->__( 'others' );

			return $language;
		}
	}
}
