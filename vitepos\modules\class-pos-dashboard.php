<?php
/**
 * Its for Pos Vendor module
 *
 * @package VitePos\Modules
 */

namespace VitePos\Modules;

use Appsbd\V1\Core\BaseModule;
use Appsbd\V1\libs\Ajax_Data_Response;
use Vitepos\Models\Mapbd_Dashboard;
use Vitepos\Models\Mapbd_POS_Dashboard;

/**
 * Class Apbd_pos_vendor
 */
class POS_Dashboard extends BaseModule {
	/**
	 * The initialize is generated by appsbd
	 */
	public function initialize() {

	}
	/**
	 *  Its on init function
	 */
	public function on_init() {

		parent::on_init();

	}

	/**
	 * The OptionForm is generated by appsbd.
	 */
	public function option_form() {
		$this->set_title( 'Dashboard List' );
		$this->set_subtitle( '' );
		$this->display();
	}

	/**
	 * The data is generated by appsbd
	 */
	public function data() {
		$dashboard_data             = new \stdClass();
		$dashboard_data->order_info = Mapbd_POS_Dashboard::get_order_statistics();
		wp_send_json( $dashboard_data );
	}


}
