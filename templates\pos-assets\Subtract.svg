<svg width="375" height="91" viewBox="0 0 375 91" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_552_7865)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M187 51C199.993 51 211.539 44.805 218.846 35.2076C227.544 23.7824 238.641 11 253 11H374.999C375 11 375 11.0004 375 11.001V90.999C375 90.9995 375 91 374.999 91H0.00100583C0.000453544 91 0 90.9995 0 90.999V11.001C0 11.0004 0.000447715 11 0.001 11H121C135.359 11 146.456 23.7824 155.154 35.2076C162.461 44.805 174.007 51 187 51Z" fill="#0A296D"/>
</g>
<defs>
<filter id="filter0_d_552_7865" x="-10" y="0" width="395" height="100" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0809722 0 0 0 0 0.0954 0 0 0 0 0.441667 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_552_7865"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_552_7865" result="shape"/>
</filter>
</defs>
</svg>
