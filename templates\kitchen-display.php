<?php
/**
 * Kitchen Display Screen Template
 *
 * @var \VitePos\Modules\POS_Settings $this
 * @package vitepos
 */
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="<?php echo esc_html( $this->get_pos_color_code() ); ?>">
    <link rel="icon" href="<?php echo esc_url( $this->get_favicon() ); ?>">
    <title>شاشة المطبخ - Kitchen Display</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Common Display CSS -->
    <link href="<?php echo esc_url( $this->get_plugin_url( 'templates/pos-assets/css/display-common.css' ) ); ?>" rel="stylesheet">
    <!-- Kitchen Display CSS -->
    <link href="<?php echo esc_url( $this->get_plugin_url( 'templates/pos-assets/css/kitchen-display.css' ) ); ?>" rel="stylesheet">

    <script>
        var vitePosBase = "<?php echo esc_url( get_rest_url( null, 'vitepos/v1' ) ); ?>/";
        var kitchenConfig = {
            refreshInterval: 5000, // 5 seconds
            soundEnabled: true,
            autoRefresh: true,
            urls: {
                kitchen_order_list: vitePosBase + "restaurant/kitchen-order-list",
                start_preparing: vitePosBase + "restaurant/start-preparing",
                complete_preparing: vitePosBase + "restaurant/complete-preparing",
                deny_order: vitePosBase + "restaurant/deny-order",
                add_kitchen_note: vitePosBase + "restaurant/add-kitchen-note"
            }
        };
    </script>
</head>
<body class="kitchen-display-body">
    <div class="container-fluid h-100">
        <!-- Header -->
        <div class="row kitchen-header">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center p-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-utensils fa-2x text-primary me-3"></i>
                        <h2 class="mb-0">شاشة المطبخ</h2>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="status-indicator me-3">
                            <span class="badge bg-success" id="connection-status">متصل</span>
                        </div>
                        <div class="current-time" id="current-time"></div>
                        <button class="btn btn-outline-secondary ms-3" id="refresh-btn">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Categories -->
        <div class="row kitchen-categories">
            <div class="col-12">
                <div class="category-tabs">
                    <button class="category-tab active" data-status="all">
                        <i class="fas fa-list"></i>
                        جميع الطلبات
                        <span class="badge bg-primary" id="count-all">0</span>
                    </button>
                    <button class="category-tab" data-status="vt_in_kitchen">
                        <i class="fas fa-clock"></i>
                        في الانتظار
                        <span class="badge bg-warning" id="count-waiting">0</span>
                    </button>
                    <button class="category-tab" data-status="vt_preparing">
                        <i class="fas fa-fire"></i>
                        قيد التحضير
                        <span class="badge bg-info" id="count-preparing">0</span>
                    </button>
                    <button class="category-tab" data-status="vt_ready_to_srv">
                        <i class="fas fa-check-circle"></i>
                        جاهز للتقديم
                        <span class="badge bg-success" id="count-ready">0</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Orders Grid -->
        <div class="row kitchen-content">
            <div class="col-12">
                <div class="orders-grid" id="orders-container">
                    <!-- Orders will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Order Details Modal -->
    <div class="modal fade" id="orderModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل الطلب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="order-details">
                    <!-- Order details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Note Modal -->
    <div class="modal fade" id="noteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة ملاحظة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <textarea class="form-control" id="kitchen-note" rows="3" placeholder="اكتب ملاحظة للطلب..."></textarea>
                    <input type="hidden" id="note-order-id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="save-note">حفظ الملاحظة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo esc_url( $this->get_plugin_url( 'templates/pos-assets/js/display-config.js' ) ); ?>"></script>
    <script src="<?php echo esc_url( $this->get_plugin_url( 'templates/pos-assets/js/kitchen-display.js' ) ); ?>"></script>
</body>
</html>
