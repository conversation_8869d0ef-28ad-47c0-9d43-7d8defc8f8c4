<?php
/**
 * Its used for debug execution time
 *
 * @since: 21/09/2021
 * @author: <PERSON><PERSON><PERSON>
 * @version 1.0.0
 * @package VitePos\Libs
 */

namespace VitePos\Libs;

if ( ! class_exists( __NAMESPACE__ . '\Time_Log' ) ) {
	/**
	 * Class Time_Log
	 *
	 * @package VitePos\Libs
	 */
	class Time_Log {
		/**
		 * Its property logs
		 *
		 * @var array
		 */
		public static $logs = array();
		/**
		 * Its property events
		 *
		 * @var array
		 */
		public static $events = array();

		/**
		 * The start debug is generated by appsbd
		 *
		 * @param any    $event its event param.
		 * @param string $msg its mgs param.
		 */
		public static function start_debug( $event, $msg = '' ) {
			$event_obj              = new \stdClass();
			$event_obj->start_time  = microtime( true );
			$event_obj->end_time    = $event_obj->start_time;
			$event_obj->logs        = $msg . "\nStarted : " . $event_obj->start_time . "\n";
			self::$events[ $event ] = $event_obj;
		}

		/**
		 * The update debug is generated by appsbd
		 *
		 * @param any    $event Its event param.
		 * @param string $msg Its mgs param.
		 */
		public static function update_debug( $event, $msg = '' ) {
			if ( empty( self::$events[ $event ] ) ) {
				return;
			}
			$event_obj           =&self::$events[ $event ];
			$event_obj->end_time = microtime( true );

			$event_obj->logs .= "\n\n\t\t" . $msg . "\n";
			$event_obj->logs .= "\t\tUpdated : " . $event_obj->end_time . "\n";
			$execution_time   = ( $event_obj->end_time - $event_obj->start_time );
			$event_obj->logs .= "\t\tIt takes " . $execution_time . " seconds to execute the script\n";

			self::$events[ $event ]->end_time = $event_obj->end_time = $event_obj->start_time;
		}

		/**
		 * The put file is generated by appsbd
		 *
		 * @param any    $event Its event param.
		 * @param string $msg Its mgs param.
		 * @param string $filename Its filename param.
		 */
		public static function put_file( $event, $msg = '', $filename = '' ) {
			if ( empty( self::$events[ $event ] ) ) {
				return;
			}
			$event_obj           =&self::$events[ $event ];
			$event_obj->end_time = microtime( true );

			$event_obj->logs .= "\n\n" . $msg . "\n";
			$event_obj->logs .= 'Completed : ' . $event_obj->end_time . "\n";
			$execution_time   = ( $event_obj->end_time - $event_obj->start_time );
			$event_obj->logs .= 'Finally, It takes ' . $execution_time . " seconds to execute the script\n";
			if ( empty( $filename ) ) {
				$filename = $event;
			}
			
		}
	}
}
