/**
 * Display Configuration for Kitchen, Customer and Preparation Screens
 * Central configuration file for all display screens
 */

window.VitePosDisplayConfig = {
    // Global settings
    global: {
        apiBase: window.vitePosBase || '/wp-json/vitepos/v1/',
        refreshInterval: 5000, // 5 seconds default
        soundEnabled: true,
        autoRefresh: true,
        language: 'ar',
        timezone: 'Asia/Riyadh'
    },

    // Kitchen Display specific settings
    kitchen: {
        refreshInterval: 5000,
        soundEnabled: true,
        autoRefresh: true,
        urgentThreshold: 30, // minutes
        maxOrdersPerPage: 50,
        showOrderDetails: true,
        enableDragDrop: false,
        columns: {
            waiting: { enabled: true, color: '#ffc107' },
            preparing: { enabled: true, color: '#fd7e14' },
            ready: { enabled: true, color: '#28a745' }
        },
        notifications: {
            newOrder: true,
            urgentOrder: true,
            completedOrder: true
        }
    },

    // Customer Display specific settings
    customer: {
        refreshInterval: 3000,
        showWelcomeMessage: true,
        autoScroll: true,
        maxQueueItems: 10,
        showOrderTotal: true,
        showEstimatedTime: false,
        animations: {
            enabled: true,
            duration: 500
        },
        branding: {
            showLogo: true,
            welcomeTitle: 'أهلاً وسهلاً بكم',
            welcomeSubtitle: 'نتمنى لكم تجربة ممتعة',
            footerMessage: 'شكراً لاختياركم لنا'
        }
    },

    // Preparation Screen specific settings
    preparation: {
        refreshInterval: 4000,
        categoryFilter: true,
        timerEnabled: true,
        enableDragDrop: true,
        showStatistics: true,
        maxOrdersPerColumn: 20,
        groupByCategory: true,
        columns: {
            pending: { enabled: true, title: 'في الانتظار' },
            preparing: { enabled: true, title: 'قيد التحضير' },
            ready: { enabled: true, title: 'جاهز للتقديم' }
        },
        statistics: {
            showDaily: true,
            showHourly: false,
            showByCategory: true
        }
    },

    // API endpoints
    endpoints: {
        kitchen_order_list: 'restaurant/kitchen-order-list',
        start_preparing: 'restaurant/start-preparing',
        complete_preparing: 'restaurant/complete-preparing',
        deny_order: 'restaurant/deny-order',
        add_kitchen_note: 'restaurant/add-kitchen-note',
        served_list: 'restaurant/served-list',
        order_details: 'order/details',
        category_list: 'product/categories',
        update_order_status: 'restaurant/update-order-status'
    },

    // Order statuses
    orderStatuses: {
        'vt_in_kitchen': {
            label: 'في المطبخ',
            color: '#ffc107',
            icon: 'fa-clock',
            nextStatus: 'vt_preparing'
        },
        'vt_preparing': {
            label: 'قيد التحضير',
            color: '#fd7e14',
            icon: 'fa-fire',
            nextStatus: 'vt_ready_to_srv'
        },
        'vt_ready_to_srv': {
            label: 'جاهز للتقديم',
            color: '#28a745',
            icon: 'fa-check-circle',
            nextStatus: 'vt_served'
        },
        'vt_served': {
            label: 'تم التقديم',
            color: '#6c5ce7',
            icon: 'fa-utensils',
            nextStatus: null
        }
    },

    // Priority levels
    priorities: {
        low: {
            threshold: 15, // minutes
            label: 'عادي',
            color: '#28a745',
            class: 'priority-low'
        },
        medium: {
            threshold: 30, // minutes
            label: 'متوسط',
            color: '#ffc107',
            class: 'priority-medium'
        },
        high: {
            threshold: 45, // minutes
            label: 'عاجل',
            color: '#dc3545',
            class: 'priority-high'
        }
    },

    // Sound settings
    sounds: {
        newOrder: {
            enabled: true,
            file: 'new-order.mp3',
            volume: 0.7
        },
        orderReady: {
            enabled: true,
            file: 'order-ready.mp3',
            volume: 0.8
        },
        urgentOrder: {
            enabled: true,
            file: 'urgent-order.mp3',
            volume: 0.9
        },
        success: {
            enabled: true,
            file: 'success_tone.mp3',
            volume: 0.6
        }
    },

    // Display themes
    themes: {
        default: {
            primaryColor: '#007bff',
            secondaryColor: '#6c757d',
            successColor: '#28a745',
            warningColor: '#ffc107',
            dangerColor: '#dc3545',
            backgroundColor: '#f8f9fa'
        },
        dark: {
            primaryColor: '#0d6efd',
            secondaryColor: '#6c757d',
            successColor: '#198754',
            warningColor: '#ffc107',
            dangerColor: '#dc3545',
            backgroundColor: '#212529'
        }
    },

    // Responsive breakpoints
    breakpoints: {
        mobile: 768,
        tablet: 992,
        desktop: 1200
    },

    // Error messages
    messages: {
        connectionError: 'خطأ في الاتصال بالخادم',
        loadingError: 'فشل في تحميل البيانات',
        updateError: 'فشل في تحديث الطلب',
        noOrders: 'لا توجد طلبات',
        success: 'تم بنجاح',
        confirmAction: 'هل أنت متأكد؟'
    },

    // Date and time formatting
    dateTime: {
        locale: 'ar-SA',
        timeFormat: {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        },
        dateFormat: {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }
    },

    // Performance settings
    performance: {
        maxCachedOrders: 100,
        cacheTimeout: 300000, // 5 minutes
        debounceDelay: 300,
        throttleDelay: 1000
    },

    // Debug settings
    debug: {
        enabled: false,
        logLevel: 'info', // error, warn, info, debug
        showApiCalls: false,
        showTimestamps: true
    }
};

// Utility functions for configuration
window.VitePosDisplayConfig.utils = {
    /**
     * Get configuration for specific display type
     */
    getDisplayConfig: function(displayType) {
        return {
            ...this.global,
            ...this[displayType] || {}
        };
    },

    /**
     * Get API endpoint URL
     */
    getEndpointUrl: function(endpoint) {
        return this.global.apiBase + this.endpoints[endpoint];
    },

    /**
     * Get order status configuration
     */
    getOrderStatus: function(status) {
        return this.orderStatuses[status] || {
            label: status,
            color: '#6c757d',
            icon: 'fa-question'
        };
    },

    /**
     * Get priority configuration based on elapsed time
     */
    getPriority: function(elapsedMinutes) {
        if (elapsedMinutes >= this.priorities.high.threshold) {
            return this.priorities.high;
        } else if (elapsedMinutes >= this.priorities.medium.threshold) {
            return this.priorities.medium;
        } else {
            return this.priorities.low;
        }
    },

    /**
     * Format time for display
     */
    formatTime: function(date) {
        return new Intl.DateTimeFormat(this.dateTime.locale, this.dateTime.timeFormat).format(date);
    },

    /**
     * Format date for display
     */
    formatDate: function(date) {
        return new Intl.DateTimeFormat(this.dateTime.locale, this.dateTime.dateFormat).format(date);
    },

    /**
     * Calculate elapsed time in minutes
     */
    calculateElapsedTime: function(startTime) {
        const now = new Date();
        const start = new Date(startTime);
        return Math.floor((now - start) / (1000 * 60));
    },

    /**
     * Play notification sound
     */
    playSound: function(soundType) {
        if (!this.global.soundEnabled) return;
        
        const soundConfig = this.sounds[soundType];
        if (!soundConfig || !soundConfig.enabled) return;

        try {
            const audio = new Audio(`/wp-content/plugins/vitepos/templates/pos-assets/${soundConfig.file}`);
            audio.volume = soundConfig.volume;
            audio.play().catch(e => console.log('Could not play sound:', e));
        } catch (error) {
            console.log('Sound playback error:', error);
        }
    },

    /**
     * Show toast notification
     */
    showToast: function(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        
        const icon = type === 'success' ? 'check-circle' : 
                    type === 'error' ? 'exclamation-circle' : 
                    type === 'warning' ? 'exclamation-triangle' : 'info-circle';
        
        toast.innerHTML = `
            <i class="fas fa-${icon}"></i>
            ${message}
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, duration);
    },

    /**
     * Log debug information
     */
    log: function(level, message, data = null) {
        if (!this.debug.enabled) return;
        
        const levels = ['error', 'warn', 'info', 'debug'];
        const currentLevelIndex = levels.indexOf(this.debug.logLevel);
        const messageLevelIndex = levels.indexOf(level);
        
        if (messageLevelIndex <= currentLevelIndex) {
            const timestamp = this.debug.showTimestamps ? `[${new Date().toISOString()}]` : '';
            console[level](`${timestamp} VitePos Display:`, message, data || '');
        }
    }
};

// Initialize configuration on load
document.addEventListener('DOMContentLoaded', function() {
    // Apply theme if specified
    const theme = localStorage.getItem('vitepos-display-theme') || 'default';
    document.documentElement.setAttribute('data-theme', theme);
    
    // Set up global error handling
    window.addEventListener('error', function(e) {
        VitePosDisplayConfig.utils.log('error', 'JavaScript Error:', e.error);
    });
    
    // Set up unhandled promise rejection handling
    window.addEventListener('unhandledrejection', function(e) {
        VitePosDisplayConfig.utils.log('error', 'Unhandled Promise Rejection:', e.reason);
    });
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VitePosDisplayConfig;
}
