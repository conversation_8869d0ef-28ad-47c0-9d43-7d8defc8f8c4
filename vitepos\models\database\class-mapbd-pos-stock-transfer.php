<?php
/**
 * Pos Vendor Database Model
 *
 * @package Vitepos\Models\Database
 */

namespace Vitepos\Models\Database;

use VitePos\Core\ViteposModel;

/**
 * Class Mapbd_Pos_Stock_Transfer
 *
 * @package Vitepos\Models\Database
 */
class Mapbd_Pos_Stock_Transfer extends ViteposModel {
	/**
	 * Its property id
	 *
	 * @var int
	 */
	public $id;
	/**
	 * Its property Transfer by
	 *
	 * @var int
	 */
	public $transfer_by;
	/**
	 * Its property transfer from
	 *
	 * @var int
	 */
	public $transfer_from;
	/**
	 * Its property transfer to
	 *
	 * @var int
	 */
	public $transfer_to;
	/**
	 * Its property transfer_note
	 *
	 * @var int
	 */
	public $transfer_note;
	/**
	 * Its property receive_note
	 *
	 * @var int
	 */
	public $receive_note;
	/**
	 * Its property receive_by
	 *
	 * @var int
	 */
	public $receive_by;
	/**
	 * Its property transfer status
	 *
	 * @var string
	 */
	public $transfer_status;
	/**
	 * Its property transfer date
	 *
	 * @var string
	 */
	public $transfer_date;
	/**
	 * Its property receive date
	 *
	 * @var string
	 */
	public $receive_date;
	/**
	 * Its property receive date
	 *
	 * @var string
	 */
	public $accept_by;
	/**
	 * Its property receive date
	 *
	 * @var string
	 */
	public $accept_date;

	/**
	 * Mapbd_pos_vendor constructor.
	 */
	public function __construct() {
		parent::__construct();
		$this->set_validation();
		$this->table_name     = 'apbd_pos_stock_transfer';
		$this->primary_key    = 'id';
		$this->unique_key     = array();
		$this->multi_key      = array();
		$this->auto_inc_field = array( 'id' );
		$this->app_base_name  = 'apbd-elite-pos';
	}


	/**
	 * The set validation is generated by appsbd
	 */
	public function set_validation() {
		$this->validations = array(
			'id'              => array(
				'Text' => 'Id',
				'Rule' => 'max_length[11]|integer',
			),
			'transfer_by'     => array(
				'Text' => 'Transfer by',
				'Rule' => 'max_length[11]|integer',
			),
			'transfer_from'   => array(
				'Text' => 'Transfer from',
				'Rule' => 'max_length[11]|integer',
			),
			'receive_by'      => array(
				'Text' => 'Transfer to',
				'Rule' => 'max_length[11]|integer',
			),
			'transfer_to'     => array(
				'Text' => 'Transfer to',
				'Rule' => 'max_length[11]|integer',
			),
			'transfer_note'   => array(
				'Text' => 'Transfer note',
				'Rule' => 'max_length[255]',
			),
			'receive_note'    => array(
				'Text' => 'Receive note',
				'Rule' => 'max_length[255]',
			),
			'transfer_date'   => array(
				'Text' => 'Transfer date',
				'Rule' => 'max_length[20]',
			),
			'receive_date'    => array(
				'Text' => 'Transfer date',
				'Rule' => 'max_length[20]',
			),
			'transfer_status' => array(
				'Text' => 'Status',
				'Rule' => 'max_length[1]',
			),
		);
	}

	/**
	 * The get property raw options is generated by appsbd
	 *
	 * @param \Appsbd\V1\Core\any $property Its string.
	 * @param false               $is_with_select Its bool.
	 *
	 * @return array|string[]
	 */
	public function get_property_raw_options( $property, $is_with_select = false ) {
		$return_obj = array();
		switch ( $property ) {
			case 'transfer_status':
				$return_obj = array(
					'P' => 'Pending',
					'R' => 'Received',
					'D' => 'Declined',
					'E' => 'Draft',
					'A' => 'Accepted',
					'C' => 'Cancelled',
				);
				break;
			default:
		}
		if ( $is_with_select ) {
			return array_merge( array( '' => 'Select' ), $return_obj );
		}

		return $return_obj;

	}

	/**
	 * The create db table is generated by appsbd
	 */
	public static function create_db_table() {
		$this_obj = new static();
		$table    = $this_obj->db->prefix . $this_obj->table_name;
		if ( $this_obj->db->get_var( "show tables like '{$table}'" ) != $table ) {
			$sql = "CREATE TABLE `{$table}` (
					  `id` INT ( 11 ) UNSIGNED NOT NULL AUTO_INCREMENT,
					`transfer_by` INT ( 11 ) UNSIGNED NOT NULL COMMENT 'Transfering user',
					`transfer_from` INT ( 11 ) UNSIGNED NOT NULL COMMENT 'Transfer from outlet',
					`transfer_to` INT ( 11 ) UNSIGNED NOT NULL COMMENT 'Transfer to outlet',
					`transfer_note` char(255) NOT NULL DEFAULT '',
					`transfer_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  					`receive_note` char(255) NOT NULL DEFAULT '',
					`receive_by` INT ( 11 ) UNSIGNED NOT NULL COMMENT 'Receiving user',
  					`transfer_status` char(1) NOT NULL DEFAULT 'P' COMMENT 'radio(P=Pending,R=Received,D=Declined,C=Cancelled,E=Draft,A=Accepted)',
					`receive_date` timestamp NULL DEFAULT NULL,
					`accepted_by` INT ( 11 ) UNSIGNED NOT NULL COMMENT 'Receiving user',
					`accepted_date` timestamp NULL DEFAULT NULL,
					PRIMARY KEY (`id`)
					) ";
			require_once ABSPATH . 'wp-admin/includes/upgrade.php';
			dbDelta( $sql );
		}
	}

	/**
	 * The delete by id is generated by appsbd
	 *
	 * @param any $id Its vendor id param.
	 *
	 * @return bool
	 */
	public static function delete_by_id( $id ) {
		return parent::delete_by_key_value( 'id', $id );
	}

	/**
	 * The get receive count is generated by appsbd
	 *
	 * @param any $outlet_id its outlet id param.
	 *
	 * @return false|int
	 */
	public static function get_receive_count( $outlet_id ) {
		$outlet_id = (int) $outlet_id;
		if ( empty( $outlet_id ) ) {
			return 0;
		}
		$obj = new self();
		$obj->transfer_to( $outlet_id );
		$obj->transfer_from( "!='$outlet_id'", true );
		$obj->transfer_status( 'P' );
		return $obj->count_all();
	}

	/**
	 * The get declined count is generated by appsbd
	 *
	 * @param any $outlet_id its outlet id param.
	 *
	 * @return false|int
	 */
	public static function get_declined_count( $outlet_id ) {
		$outlet_id = (int) $outlet_id;
		if ( empty( $outlet_id ) ) {
			return 0;
		}
		$obj = new self();
		$obj->transfer_from( $outlet_id );
		$obj->transfer_status( 'D' );
		return $obj->count_all();
	}
}
