<?php

/**
 * Its used for Client Language
 *
 * @since: 21/09/2023
 *
 * @author: <PERSON><PERSON><PERSON>
 * @version 2.0.2
 * @package VitePos\Libs
 */

namespace VitePos\Libs;

use Vitepos\Models\Database\Mapbd_Pos_Stock_Log;
use Vitepos\Models\Database\Mapbd_Pos_Warehouse;
use VitePos\Modules\POS_Settings;
use WPMailSMTP\Vendor\Google\Service\Gmail\PopSettings;

/**
 * Class WC_Stock_Manage
 *
 * @package VitePos\Libs
 */
class Vitepos_Stock_Manage {
	/**
	 * WC_Stock_Manage constructor.
	 */
	public function __construct() {
		add_action( 'vitepos/action/return-stock', array( $this, 'stock_return'), 10, 4 );
		add_action( 'woocommerce_order_refunded', array( $this, 'on_refund_create'), 10, 2 );
	}

	/**
	 * The on refund create is generated by appsbd
	 *
	 * @param mixed $order_id Its the main order id.
	 * @param mixed $refund_id Its the refund id.
	 */
	public function on_refund_create( $order_id, $refund_id){
		update_post_meta($order_id,'_vt_has_refund','Y');
	}
	/**
	 * The check restored is generated by appsbd
	 *
	 * @param mixed $product_id Its product_id param.
	 * @param mixed $qty Its qty param.
	 * @param mixed $type Its type param.
	 * @param mixed $ref_type Its ref_type param.
	 * @param mixed $ref_val Its ref_val param.
	 * @param mixed $ex1_param Its ex1_param param.
	 *
	 * @return bool
	 */
	protected function check_restored($product_id,$qty,$type,$ref_type,$ref_val,$ex1_param){
		$obj=new Mapbd_Pos_Stock_Log();
		$obj->product_id($product_id);
		$obj->type($type);
		$obj->ref_type($ref_type);
		$obj->ref_val($ref_val);
		$obj->ex1_param($ex1_param);
		$obj->stock_val($qty);
		return $obj->count_all()>0;

	}
	/**
	 * The stock return is generated by appsbd
	 *
	 * @param mixed $type Its type param.
	 * @param \WC_Order $main_order Its main_order param.
	 * @param \WC_Order_Refund $refund_order Its refund_order param.
	 */
	public function stock_return( $type, $main_order, $refund_order,$current_outlet) {
		if(!POS_Settings::is_stockable()){
			return;
		}
		if ( $type == 'F' ) {
			$items = $main_order->get_items();
		} else {
			$items = $refund_order->get_items();
		}
		$current_stocks = array();
		$stock_type='O';
		if(POS_Settings::is_default_stock()) {
			$current_outlet = 0;
			$stock_type     = 'W';
		}
		foreach ( $items as $item ) {
			$std               = new \stdClass();
			$std->product_id   = $item->get_product_id();
			$std->variation_id = $item->get_variation_id();

			if ( $type == 'F' ) {
				$item_id       = $item->get_id();
			}else {
				$item_id = vitepos_unsigned_value( absint( $item->get_meta( '_refunded_item_id' ) ) );
			}

			if ( ! empty( $std->variation_id ) ) {
				$final_id = $std->variation_id;
			} else {
				$final_id = $std->product_id;
			}

			$qty=vitepos_unsigned_value(absint($item->get_quantity()));

			if(!$this->check_restored($final_id,$qty,'I','OR',$main_order->get_id(),$refund_order->get_id())){
				$pre_stock=apply_filters( 'vitepos/filter/outlet-stock', 0, $final_id, $current_outlet );
				$product=wc_get_product($final_id);
				if(POS_Settings::is_default_stock()){
					$product->set_stock_quantity($pre_stock+$qty);
					$product->save();
				}else{
					if ( ! Mapbd_Pos_Warehouse::increase_stock_for_outlet( $product, $current_outlet, $qty, 'Product return', $main_order->get_id(), 'OR' ,false) ) {
						$this->add_error( 'Stock update process does not finished properly' );
					}
				}
				Mapbd_Pos_Stock_Log::AddLog(
					'I',
					$current_outlet,
					$final_id,
					$pre_stock,
					$qty,
					"Order Returned",
					$main_order->get_id(),
					'OR',
					$stock_type,
					$refund_order->get_id(),
					$item_id
				);
				$std->stock = apply_filters( 'vitepos/filter/outlet-stock', 0, $final_id, $current_outlet );
				$current_stocks[]=$std;
			}
		}
		/**
		 * Its for check is there any change before process
		 *
		 * @since 2.0.2
		 */
		do_action( 'vitepos/action/stock-updated', $current_stocks,$current_outlet);

	}
}
