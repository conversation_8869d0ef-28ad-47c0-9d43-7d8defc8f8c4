<?php
/**
 * Its for Pos Counter module
 *
 * @package VitePos\Modules
 */

namespace VitePos\Modules;

use Appsbd\V1\Core\BaseModule;
use Appsbd\V1\libs\Ajax_Data_Response;
use Appsbd\V1\libs\Ajax_Response;
use Vitepos\Models\Database\Mapbd_pos_counter;

/**
 * Class POS_Counter
 */
class POS_Counter extends BaseModule {
	/**
	 * The initialize is generated by appsbd
	 */
	public function initialize() {
		 $this->add_ajax_action( 'add', array( $this, 'add' ) );
		 $this->add_ajax_action( 'edit', array( $this, 'edit' ) );
		 $this->add_ajax_action( 'delete_item', array( $this, 'delete_item' ) );
	}
	/**
	 * The add is generated by appsbd
	 */
	public function add() {
		$response = new Ajax_Response();
		if ( APPSBD_IS_POST_BACK ) {
			$n_object = new Mapbd_pos_counter();
			if ( $n_object->set_from_post_data( true ) ) {
				if ( $n_object->save() ) {
					$this->add_info( 'Successfully added' );
					$response->display_with_response( true );
				}
			}
		}
		$response->display_with_response( false );
	}

	/**
	 * The edit is generated by appsbd
	 *
	 * @param string $param_id Its string.
	 */
	public function edit( $param_id = '' ) {
		 $this->set_popup_col_class( 'col-sm-6' );

		$param_id = APBD_GetValue( 'id' );
		if ( empty( $param_id ) ) {
			  $this->add_error( 'Invalid request' );
			 $this->display_popup_msg();
			 return;
		}
		 $this->set_title( 'Edit Apbd Pos Counter' );
		if ( APPSBD_IS_POST_BACK ) {
				  $uobject = new Mapbd_pos_counter();
			if ( $uobject->SetFromPostData( false ) ) {
				$uobject->SetWhereUpdate( 'id', $param_id );
				if ( $uobject->Update() ) {
						   APBD_AddLog( 'U', $uobject->settedPropertyforLog(), 'l002', '' );
						   $this->add_info( 'Successfully updated' );
						   $this->display_popup_msg();
						   return;
				}
			}
		}
		 $mainobj = new Mapbd_pos_counter();
		 $mainobj->id( $param_id );
		if ( ! $mainobj->Select() ) {
				$this->add_error( 'Invalid request' );
			   $this->display_popup_msg();
			   return;
		}
			  APBD_OldFields( $mainobj, 'name,number,outlet_id' );
			  			  $this->add_view_data( 'mainobj', $mainobj );
			  $this->add_view_data( 'isUpdateMode', true );
			  $this->display_popup( 'add' );
	}

	/**
	 * The data is generated by appsbd
	 */
	public function data() {
		$main_response = new Ajax_Data_Response();
		$main_response->set_download_file_name( 'apbd-pos-counter-list' );
		$mainobj = new Mapbd_pos_counter();

		$records = $mainobj->count_all(
			$main_response->src_item,
			$main_response->src_text,
			$main_response->multiparam,
			'after'
		);
		if ( $records > 0 ) {
			$main_response->set_grid_records( $records );
			$result = $mainobj->select_all_grid_data(
				'',
				$main_response->order_by,
				$main_response->order,
				$main_response->rows,
				$main_response->limit_start,
				$main_response->src_item,
				$main_response->src_text,
				$main_response->multiparam,
				'after'
			);

			$main_response->set_grid_data( $result );
		}
		$main_response->display_grid_response();
	}
	/**
	 * The delete item is generated by appsbd
	 *
	 * @param string $param Its string.
	 */
	public function delete_item( $param = '' ) {
		$main_response = new AppsbdAjaxConfirmResponse();
				$main_response->DisplayWithResponse( false, __( 'Delete is temporary disabled' ) );
		return;
		if ( empty( $param ) ) {
			 $main_response->DisplayWithResponse( false, __( 'Invalid Request' ) );
			 return;
		}
		$mr = new Mapbd_pos_counter();
		$mr->id( $param );
		if ( $mr->Select() ) {
			if ( Mapbd_pos_counter::DeleteByKeyValue( 'id', $param ) ) {
				APBD_AddLog( 'D', "id={$param}", 'l003', 'Wp_apbd_pos_counter_confirm' );
				$main_response->DisplayWithResponse( true, __( 'Successfully deleted' ) );
			} else {
				$main_response->DisplayWithResponse( false, __( 'Delete failed try again' ) );
			}
		}
	}




}
